{"ast": null, "code": "/* eslint-disable */\nimport { ICON_PRIMENG_LIST } from 'app/shared/tapas-ui';\nexport const UI_CONFIGURATION = {\n  stage: {\n    title: 'Quality360',\n    subTitle: 'Quality & Compliance Automation Platform',\n    homePage: 'home',\n    menu: [{\n      id: 'home',\n      title: 'Home',\n      icon: ICON_PRIMENG_LIST.PI_HOME,\n      type: 'basic',\n      isParent: true,\n      link: 'home',\n      ACL_CODE: \"HOME\"\n    }, {\n      id: 'Administration',\n      title: 'Admin',\n      icon: ICON_PRIMENG_LIST.PI_COG,\n      isParent: true,\n      type: 'collapsable',\n      ACL_CODE: \"ADMIN\",\n      children: [{\n        id: 'HealthCheck',\n        title: 'Health Check',\n        icon: ICON_PRIMENG_LIST.PI_COG,\n        type: 'basic',\n        link: '/admin/health-check',\n        ACL_CODE: \"ADMIN_HEALTH_CHECK\"\n      }, {\n        id: 'Users',\n        title: 'Users',\n        icon: ICON_PRIMENG_LIST.PI_USERS,\n        type: 'basic',\n        link: '/admin/users',\n        ACL_CODE: \"ADMIN_USERS\"\n      }, {\n        id: 'Sections',\n        title: 'Sections',\n        icon: ICON_PRIMENG_LIST.PI_MAP_MARKER,\n        type: 'basic',\n        link: '/admin/sections/list',\n        ACL_CODE: \"ADMIN_LOCATIONS\"\n      }, {\n        id: 'Roles',\n        title: 'Roles',\n        icon: ICON_PRIMENG_LIST.PI_COG,\n        type: 'basic',\n        link: '/admin/role',\n        ACL_CODE: \"ADMIN_ROLE_MASTER\"\n      }, {\n        id: 'Brands',\n        title: 'Brands',\n        icon: ICON_PRIMENG_LIST.PI_TAG,\n        type: 'basic',\n        link: '/admin/brand',\n        ACL_CODE: \"ADMIN_BRAND_MASTER\"\n      }]\n    }, {\n      id: 'help-center',\n      title: 'Help Center',\n      icon: ICON_PRIMENG_LIST.PI_INFO_CIRCLE,\n      isParent: true,\n      type: 'basic',\n      link: 'help-center',\n      ACL_CODE: \"HELP_CENTER\"\n    }]\n  },\n  madura: {\n    title: 'Quality360',\n    subTitle: 'Quality & Compliance Automation Platform',\n    homePage: 'home',\n    menu: [{\n      id: 'home',\n      title: 'Home',\n      icon: ICON_PRIMENG_LIST.PI_HOME,\n      type: 'basic',\n      isParent: true,\n      link: 'home',\n      ACL_CODE: \"HOME\"\n    }, {\n      id: 'Administration',\n      title: 'Admin',\n      icon: ICON_PRIMENG_LIST.PI_COG,\n      isParent: true,\n      type: 'collapsable',\n      ACL_CODE: \"ADMIN\",\n      children: [{\n        id: 'Auditor',\n        title: 'Auditor',\n        icon: ICON_PRIMENG_LIST.PI_COG,\n        type: 'basic',\n        link: '/admin/auditor',\n        ACL_CODE: \"ADMIN_AUDITOR_MASTER\"\n      }, {\n        id: 'Technician',\n        title: 'Technician',\n        icon: ICON_PRIMENG_LIST.PI_USER,\n        type: 'basic',\n        link: '/admin/technician',\n        ACL_CODE: \"ADMIN_TECHNICIAN_MASTER\"\n      }, {\n        id: 'Roles',\n        title: 'Roles',\n        icon: ICON_PRIMENG_LIST.PI_COG,\n        type: 'basic',\n        link: '/admin/role',\n        ACL_CODE: \"ADMIN_ROLE_MASTER\"\n      }, {\n        id: 'Users',\n        title: 'Users',\n        icon: ICON_PRIMENG_LIST.PI_USERS,\n        type: 'basic',\n        link: '/admin/users',\n        ACL_CODE: \"ADMIN_USERS\"\n      }, {\n        id: 'Brands',\n        title: 'Brands',\n        icon: ICON_PRIMENG_LIST.PI_TAG,\n        type: 'basic',\n        link: '/admin/brand',\n        ACL_CODE: \"ADMIN_BRAND_MASTER\"\n      }, {\n        id: 'Category',\n        title: 'Categories',\n        icon: ICON_PRIMENG_LIST.PI_TAG,\n        type: 'basic',\n        link: '/admin/category',\n        ACL_CODE: \"ADMIN_CATEGORY_MASTER\"\n      }]\n    }, {\n      id: 'help-center',\n      title: 'Help Center',\n      icon: ICON_PRIMENG_LIST.PI_INFO_CIRCLE,\n      isParent: true,\n      type: 'basic',\n      link: 'help-center',\n      ACL_CODE: \"HELP_CENTER\"\n    }]\n  }\n};", "map": {"version": 3, "names": ["ICON_PRIMENG_LIST", "UI_CONFIGURATION", "stage", "title", "subTitle", "homePage", "menu", "id", "icon", "PI_HOME", "type", "isParent", "link", "ACL_CODE", "PI_COG", "children", "PI_USERS", "PI_MAP_MARKER", "PI_TAG", "PI_INFO_CIRCLE", "<PERSON>ura", "PI_USER"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\app\\core\\navigation\\navigation.ts"], "sourcesContent": ["/* eslint-disable */\r\nimport { ICON_PRIMENG_LIST } from 'app/shared/tapas-ui';\r\n\r\nexport const UI_CONFIGURATION = {\r\n\r\n    stage: {\r\n        title: 'Quality360',\r\n        subTitle: 'Quality & Compliance Automation Platform',\r\n        homePage: 'home',\r\n        menu: [\r\n            {\r\n                id: 'home',\r\n                title: 'Home',\r\n                icon: ICON_PRIMENG_LIST.PI_HOME,\r\n                type: 'basic',\r\n                isParent: true,\r\n                link: 'home',\r\n                ACL_CODE: \"HOME\",\r\n            },\r\n            {\r\n                id: 'Administration',\r\n                title: 'Admin',\r\n                icon: ICON_PRIMENG_LIST.PI_COG,\r\n                isParent: true,\r\n                type: 'collapsable',\r\n                ACL_CODE: \"ADMIN\",\r\n                children: [\r\n                    {\r\n                        id: 'HealthCheck',\r\n                        title: 'Health Check',\r\n                        icon: ICON_PRIMENG_LIST.PI_COG,\r\n                        type: 'basic',\r\n                        link: '/admin/health-check',\r\n                        ACL_CODE: \"ADMIN_HEALTH_CHECK\",\r\n                    },\r\n                    {\r\n                        id: 'Users',\r\n                        title: 'Users',\r\n                        icon: ICON_PRIMENG_LIST.PI_USERS,\r\n                        type: 'basic',\r\n                        link: '/admin/users',\r\n                        ACL_CODE: \"ADMIN_USERS\",\r\n                    },\r\n                    {\r\n                        id: 'Sections',\r\n                        title: 'Sections',\r\n                        icon: ICON_PRIMENG_LIST.PI_MAP_MARKER,\r\n                        type: 'basic',\r\n                        link: '/admin/sections/list',\r\n                        ACL_CODE: \"ADMIN_LOCATIONS\",\r\n                    },\r\n                    {\r\n                        id: 'Roles',\r\n                        title: 'Roles',\r\n                        icon: ICON_PRIMENG_LIST.PI_COG,\r\n                        type: 'basic',\r\n                        link: '/admin/role',\r\n                        ACL_CODE: \"ADMIN_ROLE_MASTER\",\r\n                    },\r\n                    {\r\n                        id: 'Brands',\r\n                        title: 'Brands',\r\n                        icon: ICON_PRIMENG_LIST.PI_TAG,\r\n                        type: 'basic',\r\n                        link: '/admin/brand',\r\n                        ACL_CODE: \"ADMIN_BRAND_MASTER\",\r\n                    },\r\n                ]\r\n            },\r\n            {\r\n                id: 'help-center',\r\n                title: 'Help Center',\r\n                icon: ICON_PRIMENG_LIST.PI_INFO_CIRCLE,\r\n                isParent: true,\r\n                type: 'basic',\r\n                link: 'help-center',\r\n                ACL_CODE: \"HELP_CENTER\",\r\n            },\r\n\r\n        ]\r\n    },\r\n    madura: {\r\n        title: 'Quality360',\r\n        subTitle: 'Quality & Compliance Automation Platform',\r\n        homePage: 'home',\r\n        menu: [\r\n            {\r\n                id: 'home',\r\n                title: 'Home',\r\n                icon: ICON_PRIMENG_LIST.PI_HOME,\r\n                type: 'basic',\r\n                isParent: true,\r\n                link: 'home',\r\n                ACL_CODE: \"HOME\",\r\n            },\r\n            {\r\n                id: 'Administration',\r\n                title: 'Admin',\r\n                icon: ICON_PRIMENG_LIST.PI_COG,\r\n                isParent: true,\r\n                type: 'collapsable',\r\n                ACL_CODE: \"ADMIN\",\r\n                children: [\r\n                    {\r\n                        id: 'Auditor',\r\n                        title: 'Auditor',\r\n                        icon: ICON_PRIMENG_LIST.PI_COG,\r\n                        type: 'basic',\r\n                        link: '/admin/auditor',\r\n                        ACL_CODE: \"ADMIN_AUDITOR_MASTER\",\r\n                    },\r\n                    {\r\n                        id: 'Technician',\r\n                        title: 'Technician',\r\n                        icon: ICON_PRIMENG_LIST.PI_USER,\r\n                        type: 'basic',\r\n                        link: '/admin/technician',\r\n                        ACL_CODE: \"ADMIN_TECHNICIAN_MASTER\",\r\n                    },\r\n                    {\r\n                        id: 'Roles',\r\n                        title: 'Roles',\r\n                        icon: ICON_PRIMENG_LIST.PI_COG,\r\n                        type: 'basic',\r\n                        link: '/admin/role',\r\n                        ACL_CODE: \"ADMIN_ROLE_MASTER\",\r\n                    },\r\n                    {\r\n                        id: 'Users',\r\n                        title: 'Users',\r\n                        icon: ICON_PRIMENG_LIST.PI_USERS,\r\n                        type: 'basic',\r\n                        link: '/admin/users',\r\n                        ACL_CODE: \"ADMIN_USERS\",\r\n                    },\r\n                    {\r\n                        id: 'Brands',\r\n                        title: 'Brands',\r\n                        icon: ICON_PRIMENG_LIST.PI_TAG,\r\n                        type: 'basic',\r\n                        link: '/admin/brand',\r\n                        ACL_CODE: \"ADMIN_BRAND_MASTER\",\r\n                    },\r\n                    {\r\n                        id: 'Category',\r\n                        title: 'Categories',\r\n                        icon: ICON_PRIMENG_LIST.PI_TAG,\r\n                        type: 'basic',\r\n                        link: '/admin/category',\r\n                        ACL_CODE: \"ADMIN_CATEGORY_MASTER\",\r\n                    },\r\n                ]\r\n            },\r\n            {\r\n                id: 'help-center',\r\n                title: 'Help Center',\r\n                icon: ICON_PRIMENG_LIST.PI_INFO_CIRCLE,\r\n                isParent: true,\r\n                type: 'basic',\r\n                link: 'help-center',\r\n                ACL_CODE: \"HELP_CENTER\",\r\n            },\r\n\r\n        ]\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA,SAASA,iBAAiB,QAAQ,qBAAqB;AAEvD,OAAO,MAAMC,gBAAgB,GAAG;EAE5BC,KAAK,EAAE;IACHC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,0CAA0C;IACpDC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,CACF;MACIC,EAAE,EAAE,MAAM;MACVJ,KAAK,EAAE,MAAM;MACbK,IAAI,EAAER,iBAAiB,CAACS,OAAO;MAC/BC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;KACb,EACD;MACIN,EAAE,EAAE,gBAAgB;MACpBJ,KAAK,EAAE,OAAO;MACdK,IAAI,EAAER,iBAAiB,CAACc,MAAM;MAC9BH,QAAQ,EAAE,IAAI;MACdD,IAAI,EAAE,aAAa;MACnBG,QAAQ,EAAE,OAAO;MACjBE,QAAQ,EAAE,CACN;QACIR,EAAE,EAAE,aAAa;QACjBJ,KAAK,EAAE,cAAc;QACrBK,IAAI,EAAER,iBAAiB,CAACc,MAAM;QAC9BJ,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,qBAAqB;QAC3BC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,OAAO;QACXJ,KAAK,EAAE,OAAO;QACdK,IAAI,EAAER,iBAAiB,CAACgB,QAAQ;QAChCN,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,UAAU;QACdJ,KAAK,EAAE,UAAU;QACjBK,IAAI,EAAER,iBAAiB,CAACiB,aAAa;QACrCP,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,sBAAsB;QAC5BC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,OAAO;QACXJ,KAAK,EAAE,OAAO;QACdK,IAAI,EAAER,iBAAiB,CAACc,MAAM;QAC9BJ,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,QAAQ;QACZJ,KAAK,EAAE,QAAQ;QACfK,IAAI,EAAER,iBAAiB,CAACkB,MAAM;QAC9BR,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE;OACb;KAER,EACD;MACIN,EAAE,EAAE,aAAa;MACjBJ,KAAK,EAAE,aAAa;MACpBK,IAAI,EAAER,iBAAiB,CAACmB,cAAc;MACtCR,QAAQ,EAAE,IAAI;MACdD,IAAI,EAAE,OAAO;MACbE,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE;KACb;GAGR;EACDO,MAAM,EAAE;IACJjB,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE,0CAA0C;IACpDC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE,CACF;MACIC,EAAE,EAAE,MAAM;MACVJ,KAAK,EAAE,MAAM;MACbK,IAAI,EAAER,iBAAiB,CAACS,OAAO;MAC/BC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE,IAAI;MACdC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;KACb,EACD;MACIN,EAAE,EAAE,gBAAgB;MACpBJ,KAAK,EAAE,OAAO;MACdK,IAAI,EAAER,iBAAiB,CAACc,MAAM;MAC9BH,QAAQ,EAAE,IAAI;MACdD,IAAI,EAAE,aAAa;MACnBG,QAAQ,EAAE,OAAO;MACjBE,QAAQ,EAAE,CACN;QACIR,EAAE,EAAE,SAAS;QACbJ,KAAK,EAAE,SAAS;QAChBK,IAAI,EAAER,iBAAiB,CAACc,MAAM;QAC9BJ,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,gBAAgB;QACtBC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,YAAY;QAChBJ,KAAK,EAAE,YAAY;QACnBK,IAAI,EAAER,iBAAiB,CAACqB,OAAO;QAC/BX,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,mBAAmB;QACzBC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,OAAO;QACXJ,KAAK,EAAE,OAAO;QACdK,IAAI,EAAER,iBAAiB,CAACc,MAAM;QAC9BJ,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,OAAO;QACXJ,KAAK,EAAE,OAAO;QACdK,IAAI,EAAER,iBAAiB,CAACgB,QAAQ;QAChCN,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,QAAQ;QACZJ,KAAK,EAAE,QAAQ;QACfK,IAAI,EAAER,iBAAiB,CAACkB,MAAM;QAC9BR,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE;OACb,EACD;QACIN,EAAE,EAAE,UAAU;QACdJ,KAAK,EAAE,YAAY;QACnBK,IAAI,EAAER,iBAAiB,CAACkB,MAAM;QAC9BR,IAAI,EAAE,OAAO;QACbE,IAAI,EAAE,iBAAiB;QACvBC,QAAQ,EAAE;OACb;KAER,EACD;MACIN,EAAE,EAAE,aAAa;MACjBJ,KAAK,EAAE,aAAa;MACpBK,IAAI,EAAER,iBAAiB,CAACmB,cAAc;MACtCR,QAAQ,EAAE,IAAI;MACdD,IAAI,EAAE,OAAO;MACbE,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE;KACb;;CAIZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}