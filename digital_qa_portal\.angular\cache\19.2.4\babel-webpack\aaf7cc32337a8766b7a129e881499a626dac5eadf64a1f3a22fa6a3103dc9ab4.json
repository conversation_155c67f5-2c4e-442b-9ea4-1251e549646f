{"ast": null, "code": "import { get, set } from \"./schedule.js\";\nfunction durationFunction(id, value) {\n  return function () {\n    set(this, id).duration = +value.apply(this, arguments);\n  };\n}\nfunction durationConstant(id, value) {\n  return value = +value, function () {\n    set(this, id).duration = value;\n  };\n}\nexport default function (value) {\n  var id = this._id;\n  return arguments.length ? this.each((typeof value === \"function\" ? durationFunction : durationConstant)(id, value)) : get(this.node(), id).duration;\n}", "map": {"version": 3, "names": ["get", "set", "durationFunction", "id", "value", "duration", "apply", "arguments", "durationConstant", "_id", "length", "each", "node"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-transition/src/transition/duration.js"], "sourcesContent": ["import {get, set} from \"./schedule.js\";\n\nfunction durationFunction(id, value) {\n  return function() {\n    set(this, id).duration = +value.apply(this, arguments);\n  };\n}\n\nfunction durationConstant(id, value) {\n  return value = +value, function() {\n    set(this, id).duration = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? durationFunction\n          : durationConstant)(id, value))\n      : get(this.node(), id).duration;\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,GAAG,QAAO,eAAe;AAEtC,SAASC,gBAAgBA,CAACC,EAAE,EAAEC,KAAK,EAAE;EACnC,OAAO,YAAW;IAChBH,GAAG,CAAC,IAAI,EAAEE,EAAE,CAAC,CAACE,QAAQ,GAAG,CAACD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACxD,CAAC;AACH;AAEA,SAASC,gBAAgBA,CAACL,EAAE,EAAEC,KAAK,EAAE;EACnC,OAAOA,KAAK,GAAG,CAACA,KAAK,EAAE,YAAW;IAChCH,GAAG,CAAC,IAAI,EAAEE,EAAE,CAAC,CAACE,QAAQ,GAAGD,KAAK;EAChC,CAAC;AACH;AAEA,eAAe,UAASA,KAAK,EAAE;EAC7B,IAAID,EAAE,GAAG,IAAI,CAACM,GAAG;EAEjB,OAAOF,SAAS,CAACG,MAAM,GACjB,IAAI,CAACC,IAAI,CAAC,CAAC,OAAOP,KAAK,KAAK,UAAU,GAClCF,gBAAgB,GAChBM,gBAAgB,EAAEL,EAAE,EAAEC,KAAK,CAAC,CAAC,GACjCJ,GAAG,CAAC,IAAI,CAACY,IAAI,CAAC,CAAC,EAAET,EAAE,CAAC,CAACE,QAAQ;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}