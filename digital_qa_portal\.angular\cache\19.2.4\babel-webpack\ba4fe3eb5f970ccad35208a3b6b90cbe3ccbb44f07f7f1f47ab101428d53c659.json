{"ast": null, "code": "export class Technician {\n  constructor() {\n    this.uuid = '';\n    this.code = '';\n    this.id = 0;\n    this.userName = '';\n    this.password = '';\n    this.firstName = '';\n    this.lastName = '';\n    this.fullName = '';\n    this.addressLine1 = '';\n    this.addressLine2 = '';\n    this.city = '';\n    this.country = '';\n    this.pincode = '';\n    this.contactNo = '';\n    this.email = '';\n    this.role = '';\n    this.createdDate = 0;\n    this.modifiedDate = 0;\n    this.status = 0;\n    this.isActive = true;\n    this.mappedWith = 0;\n    this.altemail = '';\n    this.createdDateText = '';\n  }\n}", "map": {"version": 3, "names": ["Technician", "constructor", "uuid", "code", "id", "userName", "password", "firstName", "lastName", "fullName", "addressLine1", "addressLine2", "city", "country", "pincode", "contactNo", "email", "role", "createdDate", "modifiedDate", "status", "isActive", "mappedWith", "altemail", "createdDateText"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\app\\core\\models\\technician.model.ts"], "sourcesContent": ["export interface iTechnician {\n  uuid?: string;\n  code: string;\n  id?: number;\n  userName?: string;\n  password?: string;\n  firstName?: string;\n  lastName?: string;\n  fullName?: string;\n  addressLine1?: string;\n  addressLine2?: string;\n  city?: string;\n  country?: string;\n  pincode?: string;\n  contactNo?: string;\n  email?: string;\n  role?: string;\n  createdDate?: number;\n  modifiedDate?: number;\n  status?: number;\n  isActive?: boolean;\n  mappedWith?: number;\n  altemail?: string;\n  createdDateText?: string; // For display purposes\n}\n\nexport class Technician implements iTechnician {\n  uuid: string = '';\n  code: string = '';\n  id: number = 0;\n  userName: string = '';\n  password: string = '';\n  firstName: string = '';\n  lastName: string = '';\n  fullName: string = '';\n  addressLine1: string = '';\n  addressLine2: string = '';\n  city: string = '';\n  country: string = '';\n  pincode: string = '';\n  contactNo: string = '';\n  email: string = '';\n  role: string = '';\n  createdDate: number = 0;\n  modifiedDate: number = 0;\n  status: number = 0;\n  isActive: boolean = true;\n  mappedWith: number = 0;\n  altemail: string = '';\n  createdDateText: string = '';\n}\n"], "mappings": "AA0BA,OAAM,MAAOA,UAAU;EAAvBC,YAAA;IACE,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,EAAE,GAAW,CAAC;IACd,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,MAAM,GAAW,CAAC;IAClB,KAAAC,QAAQ,GAAY,IAAI;IACxB,KAAAC,UAAU,GAAW,CAAC;IACtB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,eAAe,GAAW,EAAE;EAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}