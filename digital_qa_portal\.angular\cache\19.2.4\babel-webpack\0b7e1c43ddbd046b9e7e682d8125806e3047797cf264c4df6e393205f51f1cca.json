{"ast": null, "code": "import creator from \"../creator.js\";\nimport selector from \"../selector.js\";\nfunction constantNull() {\n  return null;\n}\nexport default function (name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n    select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function () {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}", "map": {"version": 3, "names": ["creator", "selector", "constant<PERSON><PERSON>", "name", "before", "create", "select", "insertBefore", "apply", "arguments"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-selection/src/selection/insert.js"], "sourcesContent": ["import creator from \"../creator.js\";\nimport selector from \"../selector.js\";\n\nfunction constantNull() {\n  return null;\n}\n\nexport default function(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name),\n      select = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select.apply(this, arguments) || null);\n  });\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,gBAAgB;AAErC,SAASC,YAAYA,CAAA,EAAG;EACtB,OAAO,IAAI;AACb;AAEA,eAAe,UAASC,IAAI,EAAEC,MAAM,EAAE;EACpC,IAAIC,MAAM,GAAG,OAAOF,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGH,OAAO,CAACG,IAAI,CAAC;IAC1DG,MAAM,GAAGF,MAAM,IAAI,IAAI,GAAGF,YAAY,GAAG,OAAOE,MAAM,KAAK,UAAU,GAAGA,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;EACrG,OAAO,IAAI,CAACE,MAAM,CAAC,YAAW;IAC5B,OAAO,IAAI,CAACC,YAAY,CAACF,MAAM,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,EAAEH,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI,CAAC;EAChG,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}