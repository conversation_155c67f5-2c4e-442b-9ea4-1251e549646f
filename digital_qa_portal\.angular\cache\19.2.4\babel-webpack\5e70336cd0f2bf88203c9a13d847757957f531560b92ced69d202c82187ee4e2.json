{"ast": null, "code": "import { selectorAll } from \"d3-selection\";\nimport { Transition } from \"./index.js\";\nimport schedule, { get } from \"./schedule.js\";\nexport default function (select) {\n  var name = this._name,\n    id = this._id;\n  if (typeof select !== \"function\") select = selectorAll(select);\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children = select.call(node, node.__data__, i, group), child, inherit = get(node, id), k = 0, l = children.length; k < l; ++k) {\n          if (child = children[k]) {\n            schedule(child, name, id, k, children, inherit);\n          }\n        }\n        subgroups.push(children);\n        parents.push(node);\n      }\n    }\n  }\n  return new Transition(subgroups, parents, name, id);\n}", "map": {"version": 3, "names": ["selectorAll", "Transition", "schedule", "get", "select", "name", "_name", "id", "_id", "groups", "_groups", "m", "length", "subgroups", "parents", "j", "group", "n", "node", "i", "children", "call", "__data__", "child", "inherit", "k", "l", "push"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-transition/src/transition/selectAll.js"], "sourcesContent": ["import {selectorAll} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\nimport schedule, {get} from \"./schedule.js\";\n\nexport default function(select) {\n  var name = this._name,\n      id = this._id;\n\n  if (typeof select !== \"function\") select = selectorAll(select);\n\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        for (var children = select.call(node, node.__data__, i, group), child, inherit = get(node, id), k = 0, l = children.length; k < l; ++k) {\n          if (child = children[k]) {\n            schedule(child, name, id, k, children, inherit);\n          }\n        }\n        subgroups.push(children);\n        parents.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, parents, name, id);\n}\n"], "mappings": "AAAA,SAAQA,WAAW,QAAO,cAAc;AACxC,SAAQC,UAAU,QAAO,YAAY;AACrC,OAAOC,QAAQ,IAAGC,GAAG,QAAO,eAAe;AAE3C,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,EAAE,GAAG,IAAI,CAACC,GAAG;EAEjB,IAAI,OAAOJ,MAAM,KAAK,UAAU,EAAEA,MAAM,GAAGJ,WAAW,CAACI,MAAM,CAAC;EAE9D,KAAK,IAAIK,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,SAAS,GAAG,EAAE,EAAEC,OAAO,GAAG,EAAE,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IAClG,KAAK,IAAIC,KAAK,GAAGP,MAAM,CAACM,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACJ,MAAM,EAAEM,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MACrE,IAAID,IAAI,GAAGF,KAAK,CAACG,CAAC,CAAC,EAAE;QACnB,KAAK,IAAIC,QAAQ,GAAGhB,MAAM,CAACiB,IAAI,CAACH,IAAI,EAAEA,IAAI,CAACI,QAAQ,EAAEH,CAAC,EAAEH,KAAK,CAAC,EAAEO,KAAK,EAAEC,OAAO,GAAGrB,GAAG,CAACe,IAAI,EAAEX,EAAE,CAAC,EAAEkB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGN,QAAQ,CAACR,MAAM,EAAEa,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;UACtI,IAAIF,KAAK,GAAGH,QAAQ,CAACK,CAAC,CAAC,EAAE;YACvBvB,QAAQ,CAACqB,KAAK,EAAElB,IAAI,EAAEE,EAAE,EAAEkB,CAAC,EAAEL,QAAQ,EAAEI,OAAO,CAAC;UACjD;QACF;QACAX,SAAS,CAACc,IAAI,CAACP,QAAQ,CAAC;QACxBN,OAAO,CAACa,IAAI,CAACT,IAAI,CAAC;MACpB;IACF;EACF;EAEA,OAAO,IAAIjB,UAAU,CAACY,SAAS,EAAEC,OAAO,EAAET,IAAI,EAAEE,EAAE,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}