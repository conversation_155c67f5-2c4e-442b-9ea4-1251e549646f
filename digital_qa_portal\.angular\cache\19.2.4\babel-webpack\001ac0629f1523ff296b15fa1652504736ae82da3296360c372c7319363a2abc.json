{"ast": null, "code": "/*!\n * perfect-scrollbar v1.5.6\n * Copyright 2024 <PERSON><PERSON><PERSON>, MDBootstrap and Contributors\n * Licensed under MIT\n */\n\nfunction get(element) {\n  return getComputedStyle(element);\n}\nfunction set(element, obj) {\n  for (var key in obj) {\n    var val = obj[key];\n    if (typeof val === 'number') {\n      val = val + \"px\";\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\nfunction div(className) {\n  var div = document.createElement('div');\n  div.className = className;\n  return div;\n}\nvar elMatches = typeof Element !== 'undefined' && (Element.prototype.matches || Element.prototype.webkitMatchesSelector || Element.prototype.mozMatchesSelector || Element.prototype.msMatchesSelector);\nfunction matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n  return elMatches.call(element, query);\n}\nfunction remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\nfunction queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, function (child) {\n    return matches(child, selector);\n  });\n}\nvar cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: function (x) {\n      return \"ps__thumb-\" + x;\n    },\n    rail: function (x) {\n      return \"ps__rail-\" + x;\n    },\n    consuming: 'ps__child--consume'\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: function (x) {\n      return \"ps--active-\" + x;\n    },\n    scrolling: function (x) {\n      return \"ps--scrolling-\" + x;\n    }\n  }\n};\n\n/*\n * Helper methods\n */\nvar scrollingClassTimeout = {\n  x: null,\n  y: null\n};\nfunction addScrollingClass(i, x) {\n  var classList = i.element.classList;\n  var className = cls.state.scrolling(x);\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\nfunction removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(function () {\n    return i.isAlive && i.element.classList.remove(cls.state.scrolling(x));\n  }, i.settings.scrollingThreshold);\n}\nfunction setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\nvar EventElement = function EventElement(element) {\n  this.element = element;\n  this.handlers = {};\n};\nvar prototypeAccessors = {\n  isEmpty: {\n    configurable: true\n  }\n};\nEventElement.prototype.bind = function bind(eventName, handler) {\n  if (typeof this.handlers[eventName] === 'undefined') {\n    this.handlers[eventName] = [];\n  }\n  this.handlers[eventName].push(handler);\n  this.element.addEventListener(eventName, handler, false);\n};\nEventElement.prototype.unbind = function unbind(eventName, target) {\n  var this$1 = this;\n  this.handlers[eventName] = this.handlers[eventName].filter(function (handler) {\n    if (target && handler !== target) {\n      return true;\n    }\n    this$1.element.removeEventListener(eventName, handler, false);\n    return false;\n  });\n};\nEventElement.prototype.unbindAll = function unbindAll() {\n  for (var name in this.handlers) {\n    this.unbind(name);\n  }\n};\nprototypeAccessors.isEmpty.get = function () {\n  var this$1 = this;\n  return Object.keys(this.handlers).every(function (key) {\n    return this$1.handlers[key].length === 0;\n  });\n};\nObject.defineProperties(EventElement.prototype, prototypeAccessors);\nvar EventManager = function EventManager() {\n  this.eventElements = [];\n};\nEventManager.prototype.eventElement = function eventElement(element) {\n  var ee = this.eventElements.filter(function (ee) {\n    return ee.element === element;\n  })[0];\n  if (!ee) {\n    ee = new EventElement(element);\n    this.eventElements.push(ee);\n  }\n  return ee;\n};\nEventManager.prototype.bind = function bind(element, eventName, handler) {\n  this.eventElement(element).bind(eventName, handler);\n};\nEventManager.prototype.unbind = function unbind(element, eventName, handler) {\n  var ee = this.eventElement(element);\n  ee.unbind(eventName, handler);\n  if (ee.isEmpty) {\n    // remove\n    this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n  }\n};\nEventManager.prototype.unbindAll = function unbindAll() {\n  this.eventElements.forEach(function (e) {\n    return e.unbindAll();\n  });\n  this.eventElements = [];\n};\nEventManager.prototype.once = function once(element, eventName, handler) {\n  var ee = this.eventElement(element);\n  var onceHandler = function (evt) {\n    ee.unbind(eventName, onceHandler);\n    handler(evt);\n  };\n  ee.bind(eventName, onceHandler);\n};\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  }\n  var evt = document.createEvent('CustomEvent');\n  evt.initCustomEvent(name, false, false, undefined);\n  return evt;\n}\nfunction processScrollDiff(i, axis, diff, useScrollingClass, forceFireReachEvent) {\n  if (useScrollingClass === void 0) useScrollingClass = true;\n  if (forceFireReachEvent === void 0) forceFireReachEvent = false;\n  var fields;\n  if (axis === 'top') {\n    fields = ['contentHeight', 'containerHeight', 'scrollTop', 'y', 'up', 'down'];\n  } else if (axis === 'left') {\n    fields = ['contentWidth', 'containerWidth', 'scrollLeft', 'x', 'left', 'right'];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n  processScrollDiff$1(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\nfunction processScrollDiff$1(i, diff, ref, useScrollingClass, forceFireReachEvent) {\n  var contentHeight = ref[0];\n  var containerHeight = ref[1];\n  var scrollTop = ref[2];\n  var y = ref[3];\n  var up = ref[4];\n  var down = ref[5];\n  if (useScrollingClass === void 0) useScrollingClass = true;\n  if (forceFireReachEvent === void 0) forceFireReachEvent = false;\n  var element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n  if (diff) {\n    element.dispatchEvent(createEvent(\"ps-scroll-\" + y));\n    if (diff < 0) {\n      element.dispatchEvent(createEvent(\"ps-scroll-\" + up));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent(\"ps-scroll-\" + down));\n    }\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent(\"ps-\" + y + \"-reach-\" + i.reach[y]));\n  }\n}\nfunction toInt(x) {\n  return parseInt(x, 10) || 0;\n}\nfunction isEditable(el) {\n  return matches(el, 'input,[contenteditable]') || matches(el, 'select,[contenteditable]') || matches(el, 'textarea,[contenteditable]') || matches(el, 'button,[contenteditable]');\n}\nfunction outerWidth(element) {\n  var styles = get(element);\n  return toInt(styles.width) + toInt(styles.paddingLeft) + toInt(styles.paddingRight) + toInt(styles.borderLeftWidth) + toInt(styles.borderRightWidth);\n}\nvar env = {\n  isWebKit: typeof document !== 'undefined' && 'WebkitAppearance' in document.documentElement.style,\n  supportsTouch: typeof window !== 'undefined' && ('ontouchstart' in window || 'maxTouchPoints' in window.navigator && window.navigator.maxTouchPoints > 0 || window.DocumentTouch && document instanceof window.DocumentTouch),\n  supportsIePointer: typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome: typeof navigator !== 'undefined' && /Chrome/i.test(navigator && navigator.userAgent)\n};\n\n/* eslint-disable no-lonely-if */\n\nfunction updateGeometry(i) {\n  var element = i.element;\n  var roundedScrollTop = Math.floor(element.scrollTop);\n  var rect = element.getBoundingClientRect();\n  i.containerWidth = Math.floor(rect.width);\n  i.containerHeight = Math.floor(rect.height);\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('x')).forEach(function (el) {\n      return remove(el);\n    });\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('y')).forEach(function (el) {\n      return remove(el);\n    });\n    element.appendChild(i.scrollbarYRail);\n  }\n  if (!i.settings.suppressScrollX && i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(i, toInt(i.railXWidth * i.containerWidth / i.contentWidth));\n    i.scrollbarXLeft = toInt((i.negativeScrollAdjustment + element.scrollLeft) * (i.railXWidth - i.scrollbarXWidth) / (i.contentWidth - i.containerWidth));\n  } else {\n    i.scrollbarXActive = false;\n  }\n  if (!i.settings.suppressScrollY && i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(i, toInt(i.railYHeight * i.containerHeight / i.contentHeight));\n    i.scrollbarYTop = toInt(roundedScrollTop * (i.railYHeight - i.scrollbarYHeight) / (i.contentHeight - i.containerHeight));\n  } else {\n    i.scrollbarYActive = false;\n  }\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n  updateCss(element, i);\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\nfunction updateCss(element, i) {\n  var xRailOffset = {\n    width: i.railXWidth\n  };\n  var roundedScrollTop = Math.floor(element.scrollTop);\n  if (i.isRtl) {\n    xRailOffset.left = i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth - i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  set(i.scrollbarXRail, xRailOffset);\n  var yRailOffset = {\n    top: roundedScrollTop,\n    height: i.railYHeight\n  };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right = i.contentWidth - (i.negativeScrollAdjustment + element.scrollLeft) - i.scrollbarYRight - i.scrollbarYOuterWidth - 9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left = i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth * 2 - i.contentWidth - i.scrollbarYLeft - i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  set(i.scrollbarYRail, yRailOffset);\n  set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth\n  });\n  set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth\n  });\n}\n\n/* eslint-disable */\n\nfunction clickRail(i) {\n  // const element = i.element;\n\n  i.event.bind(i.scrollbarY, 'mousedown', function (e) {\n    return e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarYRail, 'mousedown', function (e) {\n    var positionTop = e.pageY - window.pageYOffset - i.scrollbarYRail.getBoundingClientRect().top;\n    var direction = positionTop > i.scrollbarYTop ? 1 : -1;\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n    e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarX, 'mousedown', function (e) {\n    return e.stopPropagation();\n  });\n  i.event.bind(i.scrollbarXRail, 'mousedown', function (e) {\n    var positionLeft = e.pageX - window.pageXOffset - i.scrollbarXRail.getBoundingClientRect().left;\n    var direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n    e.stopPropagation();\n  });\n}\nvar activeSlider = null; // Variable to track the currently active slider\n\nfunction setupScrollHandlers(i) {\n  bindMouseScrollHandler(i, ['containerHeight', 'contentHeight', 'pageY', 'railYHeight', 'scrollbarY', 'scrollbarYHeight', 'scrollTop', 'y', 'scrollbarYRail']);\n  bindMouseScrollHandler(i, ['containerWidth', 'contentWidth', 'pageX', 'railXWidth', 'scrollbarX', 'scrollbarXWidth', 'scrollLeft', 'x', 'scrollbarXRail']);\n}\nfunction bindMouseScrollHandler(i, ref) {\n  var containerDimension = ref[0];\n  var contentDimension = ref[1];\n  var pageAxis = ref[2];\n  var railDimension = ref[3];\n  var scrollbarAxis = ref[4];\n  var scrollbarDimension = ref[5];\n  var scrollAxis = ref[6];\n  var axis = ref[7];\n  var scrollbarRail = ref[8];\n  var element = i.element;\n  var startingScrollPosition = null;\n  var startingMousePagePosition = null;\n  var scrollBy = null;\n  function moveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageAxis] = e.touches[0][\"page\" + axis.toUpperCase()];\n    }\n\n    // Only move if the active slider is the one we started with\n    if (activeSlider === scrollbarAxis) {\n      element[scrollAxis] = startingScrollPosition + scrollBy * (e[pageAxis] - startingMousePagePosition);\n      addScrollingClass(i, axis);\n      updateGeometry(i);\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n  function endHandler() {\n    removeScrollingClass(i, axis);\n    i[scrollbarRail].classList.remove(cls.state.clicking);\n    document.removeEventListener('mousemove', moveHandler);\n    document.removeEventListener('mouseup', endHandler);\n    document.removeEventListener('touchmove', moveHandler);\n    document.removeEventListener('touchend', endHandler);\n    activeSlider = null; // Reset active slider when interaction ends\n  }\n  function bindMoves(e) {\n    if (activeSlider === null) {\n      // Only bind if no slider is currently active\n      activeSlider = scrollbarAxis; // Set current slider as active\n\n      startingScrollPosition = element[scrollAxis];\n      if (e.touches) {\n        e[pageAxis] = e.touches[0][\"page\" + axis.toUpperCase()];\n      }\n      startingMousePagePosition = e[pageAxis];\n      scrollBy = (i[contentDimension] - i[containerDimension]) / (i[railDimension] - i[scrollbarDimension]);\n      if (!e.touches) {\n        document.addEventListener('mousemove', moveHandler);\n        document.addEventListener('mouseup', endHandler);\n      } else {\n        document.addEventListener('touchmove', moveHandler, {\n          passive: false\n        });\n        document.addEventListener('touchend', endHandler);\n      }\n      i[scrollbarRail].classList.add(cls.state.clicking);\n    }\n    e.stopPropagation();\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  i[scrollbarAxis].addEventListener('mousedown', bindMoves);\n  i[scrollbarAxis].addEventListener('touchstart', bindMoves);\n}\n\n/* eslint-disable */\n\nfunction keyboard(i) {\n  var element = i.element;\n  var elementHovered = function () {\n    return matches(element, ':hover');\n  };\n  var scrollbarFocused = function () {\n    return matches(i.scrollbarX, ':focus') || matches(i.scrollbarY, ':focus');\n  };\n  function shouldPreventDefault(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (scrollTop === 0 && deltaY > 0 || scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    var scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (scrollLeft === 0 && deltaX < 0 || scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n  i.event.bind(i.ownerDocument, 'keydown', function (e) {\n    if (e.isDefaultPrevented && e.isDefaultPrevented() || e.defaultPrevented) {\n      return;\n    }\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n    var activeElement = document.activeElement ? document.activeElement : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n    var deltaX = 0;\n    var deltaY = 0;\n    switch (e.which) {\n      case 37:\n        // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38:\n        // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39:\n        // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40:\n        // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32:\n        // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33:\n        // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34:\n        // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36:\n        // home\n        deltaY = i.contentHeight;\n        break;\n      case 35:\n        // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\n\n/* eslint-disable */\n\nfunction wheel(i) {\n  var element = i.element;\n  function shouldPreventDefault(deltaX, deltaY) {\n    var roundedScrollTop = Math.floor(element.scrollTop);\n    var isTop = element.scrollTop === 0;\n    var isBottom = roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    var isLeft = element.scrollLeft === 0;\n    var isRight = element.scrollLeft + element.offsetWidth === element.scrollWidth;\n    var hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n  function getDeltaFromEvent(e) {\n    var deltaX = e.deltaX;\n    var deltaY = -1 * e.deltaY;\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = -1 * e.wheelDeltaX / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n    if (!element.contains(target)) {\n      return false;\n    }\n    var cursor = target;\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (cursor.scrollTop > 0 && deltaY < 0 || cursor.scrollTop < maxScrollTop && deltaY > 0) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (cursor.scrollLeft > 0 && deltaX < 0 || cursor.scrollLeft < maxScrollLeft && deltaX > 0) {\n            return true;\n          }\n        }\n      }\n      cursor = cursor.parentNode;\n    }\n    return false;\n  }\n  function mousewheelHandler(e) {\n    var ref = getDeltaFromEvent(e);\n    var deltaX = ref[0];\n    var deltaY = ref[1];\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n    var shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n    updateGeometry(i);\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\nfunction touch(i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n  var element = i.element;\n  var state = {\n    startOffset: {},\n    startTime: 0,\n    speed: {},\n    easingLoop: null\n  };\n  function shouldPrevent(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    var scrollLeft = element.scrollLeft;\n    var magnitudeX = Math.abs(deltaX);\n    var magnitudeY = Math.abs(deltaY);\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight || deltaY > 0 && scrollTop === 0) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth || deltaX > 0 && scrollLeft === 0) {\n        return true;\n      }\n    }\n    return true;\n  }\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n    updateGeometry(i);\n  }\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    }\n    // Maybe IE pointer\n    return e;\n  }\n  function shouldHandle(e) {\n    if (e.target === i.scrollbarX || e.target === i.scrollbarY) {\n      return false;\n    }\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (e.pointerType && e.pointerType !== 'mouse' && e.pointerType !== e.MSPOINTER_TYPE_MOUSE) {\n      return true;\n    }\n    return false;\n  }\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n    var touch = getTouch(e);\n    state.startOffset.pageX = touch.pageX;\n    state.startOffset.pageY = touch.pageY;\n    state.startTime = new Date().getTime();\n    if (state.easingLoop !== null) {\n      clearInterval(state.easingLoop);\n    }\n  }\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n    var cursor = target;\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (cursor.scrollTop > 0 && deltaY < 0 || cursor.scrollTop < maxScrollTop && deltaY > 0) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (cursor.scrollLeft > 0 && deltaX < 0 || cursor.scrollLeft < maxScrollLeft && deltaX > 0) {\n            return true;\n          }\n        }\n      }\n      cursor = cursor.parentNode;\n    }\n    return false;\n  }\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      var touch = getTouch(e);\n      var currentOffset = {\n        pageX: touch.pageX,\n        pageY: touch.pageY\n      };\n      var differenceX = currentOffset.pageX - state.startOffset.pageX;\n      var differenceY = currentOffset.pageY - state.startOffset.pageY;\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n      applyTouchMove(differenceX, differenceY);\n      state.startOffset = currentOffset;\n      var currentTime = new Date().getTime();\n      var timeGap = currentTime - state.startTime;\n      if (timeGap > 0) {\n        state.speed.x = differenceX / timeGap;\n        state.speed.y = differenceY / timeGap;\n        state.startTime = currentTime;\n      }\n      if (shouldPrevent(differenceX, differenceY)) {\n        // Prevent the default behavior if the event is cancelable\n        if (e.cancelable) {\n          e.preventDefault();\n        }\n      }\n    }\n  }\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(state.easingLoop);\n      state.easingLoop = setInterval(function () {\n        if (i.isInitialized) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n        if (!state.speed.x && !state.speed.y) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n        if (Math.abs(state.speed.x) < 0.01 && Math.abs(state.speed.y) < 0.01) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n        applyTouchMove(state.speed.x * 30, state.speed.y * 30);\n        state.speed.x *= 0.8;\n        state.speed.y *= 0.8;\n      }, 10);\n    }\n  }\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\n\n/* eslint-disable */\n\nvar defaultSettings = function () {\n  return {\n    handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n    maxScrollbarLength: null,\n    minScrollbarLength: null,\n    scrollingThreshold: 1000,\n    scrollXMarginOffset: 0,\n    scrollYMarginOffset: 0,\n    suppressScrollX: false,\n    suppressScrollY: false,\n    swipeEasing: true,\n    useBothWheelAxes: false,\n    wheelPropagation: true,\n    wheelSpeed: 1\n  };\n};\nvar handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': setupScrollHandlers,\n  keyboard: keyboard,\n  wheel: wheel,\n  touch: touch\n};\nvar PerfectScrollbar = function PerfectScrollbar(element, userSettings) {\n  var this$1 = this;\n  if (userSettings === void 0) userSettings = {};\n  if (typeof element === 'string') {\n    element = document.querySelector(element);\n  }\n  if (!element || !element.nodeName) {\n    throw new Error('no element is specified to initialize PerfectScrollbar');\n  }\n  this.element = element;\n  element.classList.add(cls.main);\n  this.settings = defaultSettings();\n  for (var key in userSettings) {\n    this.settings[key] = userSettings[key];\n  }\n  this.containerWidth = null;\n  this.containerHeight = null;\n  this.contentWidth = null;\n  this.contentHeight = null;\n  var focus = function () {\n    return element.classList.add(cls.state.focus);\n  };\n  var blur = function () {\n    return element.classList.remove(cls.state.focus);\n  };\n  this.isRtl = get(element).direction === 'rtl';\n  if (this.isRtl === true) {\n    element.classList.add(cls.rtl);\n  }\n  this.isNegativeScroll = function () {\n    var originalScrollLeft = element.scrollLeft;\n    var result = null;\n    element.scrollLeft = -1;\n    result = element.scrollLeft < 0;\n    element.scrollLeft = originalScrollLeft;\n    return result;\n  }();\n  this.negativeScrollAdjustment = this.isNegativeScroll ? element.scrollWidth - element.clientWidth : 0;\n  this.event = new EventManager();\n  this.ownerDocument = element.ownerDocument || document;\n  this.scrollbarXRail = div(cls.element.rail('x'));\n  element.appendChild(this.scrollbarXRail);\n  this.scrollbarX = div(cls.element.thumb('x'));\n  this.scrollbarXRail.appendChild(this.scrollbarX);\n  this.scrollbarX.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarX, 'focus', focus);\n  this.event.bind(this.scrollbarX, 'blur', blur);\n  this.scrollbarXActive = null;\n  this.scrollbarXWidth = null;\n  this.scrollbarXLeft = null;\n  var railXStyle = get(this.scrollbarXRail);\n  this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n  if (isNaN(this.scrollbarXBottom)) {\n    this.isScrollbarXUsingBottom = false;\n    this.scrollbarXTop = toInt(railXStyle.top);\n  } else {\n    this.isScrollbarXUsingBottom = true;\n  }\n  this.railBorderXWidth = toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n  // Set rail to display:block to calculate margins\n  set(this.scrollbarXRail, {\n    display: 'block'\n  });\n  this.railXMarginWidth = toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n  set(this.scrollbarXRail, {\n    display: ''\n  });\n  this.railXWidth = null;\n  this.railXRatio = null;\n  this.scrollbarYRail = div(cls.element.rail('y'));\n  element.appendChild(this.scrollbarYRail);\n  this.scrollbarY = div(cls.element.thumb('y'));\n  this.scrollbarYRail.appendChild(this.scrollbarY);\n  this.scrollbarY.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarY, 'focus', focus);\n  this.event.bind(this.scrollbarY, 'blur', blur);\n  this.scrollbarYActive = null;\n  this.scrollbarYHeight = null;\n  this.scrollbarYTop = null;\n  var railYStyle = get(this.scrollbarYRail);\n  this.scrollbarYRight = parseInt(railYStyle.right, 10);\n  if (isNaN(this.scrollbarYRight)) {\n    this.isScrollbarYUsingRight = false;\n    this.scrollbarYLeft = toInt(railYStyle.left);\n  } else {\n    this.isScrollbarYUsingRight = true;\n  }\n  this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n  this.railBorderYWidth = toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n  set(this.scrollbarYRail, {\n    display: 'block'\n  });\n  this.railYMarginHeight = toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n  set(this.scrollbarYRail, {\n    display: ''\n  });\n  this.railYHeight = null;\n  this.railYRatio = null;\n  this.reach = {\n    x: element.scrollLeft <= 0 ? 'start' : element.scrollLeft >= this.contentWidth - this.containerWidth ? 'end' : null,\n    y: element.scrollTop <= 0 ? 'start' : element.scrollTop >= this.contentHeight - this.containerHeight ? 'end' : null\n  };\n  this.isAlive = true;\n  this.settings.handlers.forEach(function (handlerName) {\n    return handlers[handlerName](this$1);\n  });\n  this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n  this.lastScrollLeft = element.scrollLeft; // for onScroll only\n  this.event.bind(this.element, 'scroll', function (e) {\n    return this$1.onScroll(e);\n  });\n  updateGeometry(this);\n};\nPerfectScrollbar.prototype.update = function update() {\n  if (!this.isAlive) {\n    return;\n  }\n\n  // Recalcuate negative scrollLeft adjustment\n  this.negativeScrollAdjustment = this.isNegativeScroll ? this.element.scrollWidth - this.element.clientWidth : 0;\n\n  // Recalculate rail margins\n  set(this.scrollbarXRail, {\n    display: 'block'\n  });\n  set(this.scrollbarYRail, {\n    display: 'block'\n  });\n  this.railXMarginWidth = toInt(get(this.scrollbarXRail).marginLeft) + toInt(get(this.scrollbarXRail).marginRight);\n  this.railYMarginHeight = toInt(get(this.scrollbarYRail).marginTop) + toInt(get(this.scrollbarYRail).marginBottom);\n\n  // Hide scrollbars not to affect scrollWidth and scrollHeight\n  set(this.scrollbarXRail, {\n    display: 'none'\n  });\n  set(this.scrollbarYRail, {\n    display: 'none'\n  });\n  updateGeometry(this);\n  processScrollDiff(this, 'top', 0, false, true);\n  processScrollDiff(this, 'left', 0, false, true);\n  set(this.scrollbarXRail, {\n    display: ''\n  });\n  set(this.scrollbarYRail, {\n    display: ''\n  });\n};\nPerfectScrollbar.prototype.onScroll = function onScroll(e) {\n  if (!this.isAlive) {\n    return;\n  }\n  updateGeometry(this);\n  processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n  processScrollDiff(this, 'left', this.element.scrollLeft - this.lastScrollLeft);\n  this.lastScrollTop = Math.floor(this.element.scrollTop);\n  this.lastScrollLeft = this.element.scrollLeft;\n};\nPerfectScrollbar.prototype.destroy = function destroy() {\n  if (!this.isAlive) {\n    return;\n  }\n  this.event.unbindAll();\n  remove(this.scrollbarX);\n  remove(this.scrollbarY);\n  remove(this.scrollbarXRail);\n  remove(this.scrollbarYRail);\n  this.removePsClasses();\n\n  // unset elements\n  this.element = null;\n  this.scrollbarX = null;\n  this.scrollbarY = null;\n  this.scrollbarXRail = null;\n  this.scrollbarYRail = null;\n  this.isAlive = false;\n};\nPerfectScrollbar.prototype.removePsClasses = function removePsClasses() {\n  this.element.className = this.element.className.split(' ').filter(function (name) {\n    return !name.match(/^ps([-_].+|)$/);\n  }).join(' ');\n};\nexport default PerfectScrollbar;", "map": {"version": 3, "names": ["get", "element", "getComputedStyle", "set", "obj", "key", "val", "style", "div", "className", "document", "createElement", "elMatches", "Element", "prototype", "matches", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "query", "Error", "call", "remove", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "query<PERSON><PERSON><PERSON><PERSON>", "selector", "Array", "filter", "children", "child", "cls", "main", "rtl", "thumb", "x", "rail", "consuming", "state", "focus", "clicking", "active", "scrolling", "scrollingClassTimeout", "y", "addScrollingClass", "i", "classList", "contains", "clearTimeout", "add", "removeScrollingClass", "setTimeout", "isAlive", "settings", "scrollingT<PERSON>eshold", "setScrollingClassInstantly", "EventElement", "handlers", "prototypeAccessors", "isEmpty", "configurable", "bind", "eventName", "handler", "push", "addEventListener", "unbind", "target", "this$1", "removeEventListener", "unbindAll", "name", "Object", "keys", "every", "length", "defineProperties", "EventManager", "eventElements", "eventElement", "ee", "splice", "indexOf", "for<PERSON>ach", "e", "once", "once<PERSON><PERSON><PERSON>", "evt", "createEvent", "window", "CustomEvent", "initCustomEvent", "undefined", "processScrollDiff", "axis", "diff", "useScrollingClass", "forceFireReachEvent", "fields", "processScrollDiff$1", "ref", "contentHeight", "containerHeight", "scrollTop", "up", "down", "reach", "dispatchEvent", "toInt", "parseInt", "isEditable", "el", "outerWidth", "styles", "width", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "env", "isWebKit", "documentElement", "supportsTouch", "navigator", "maxTouchPoints", "DocumentTouch", "supportsIePointer", "msMaxTouchPoints", "isChrome", "test", "userAgent", "updateGeometry", "roundedScrollTop", "Math", "floor", "rect", "getBoundingClientRect", "containerWidth", "height", "contentWidth", "scrollWidth", "scrollHeight", "scrollbarXRail", "append<PERSON><PERSON><PERSON>", "scrollbarYRail", "suppressScrollX", "scrollXMarginOffset", "scrollbarXActive", "railXWidth", "railXMarginWidth", "railXRatio", "scrollbarXWidth", "getThumbSize", "scrollbarXLeft", "negativeScrollAdjustment", "scrollLeft", "suppressScrollY", "scrollYMarginOffset", "scrollbarYActive", "railYHeight", "railYMarginHeight", "railYRatio", "scrollbarYHeight", "scrollbarYTop", "updateCss", "isRtl", "thumbSize", "minScrollbar<PERSON><PERSON>th", "max", "maxS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "min", "xRailOffset", "left", "isScrollbarXUsingBottom", "bottom", "scrollbarXBottom", "top", "scrollbarXTop", "yRailOffset", "isScrollbarYUsingRight", "right", "scrollbarYRight", "scrollbarYOuterWidth", "scrollbarYLeft", "scrollbarX", "railBorderXWidth", "scrollbarY", "railBorderYWidth", "clickRail", "event", "stopPropagation", "positionTop", "pageY", "pageYOffset", "direction", "positionLeft", "pageX", "pageXOffset", "activeSlider", "setupScrollHandlers", "bindMouseScrollHandler", "containerDimension", "contentDimension", "pageAxis", "railDimension", "scrollbarAxis", "scrollbarDimension", "scrollAxis", "scrollbarRail", "startingScrollPosition", "startingMousePagePosition", "scrollBy", "<PERSON><PERSON><PERSON><PERSON>", "touches", "toUpperCase", "preventDefault", "end<PERSON><PERSON><PERSON>", "bindMoves", "passive", "cancelable", "keyboard", "elementHovered", "scrollbarFocused", "shouldPreventDefault", "deltaX", "deltaY", "wheelPropagation", "ownerDocument", "isDefaultPrevented", "defaultPrevented", "activeElement", "tagName", "contentDocument", "shadowRoot", "which", "metaKey", "altKey", "shift<PERSON>ey", "wheel", "isTop", "isBottom", "offsetHeight", "isLeft", "isRight", "offsetWidth", "hitsBound", "abs", "getDeltaFromEvent", "wheelDeltaX", "wheelDeltaY", "deltaMode", "wheelDelta", "shouldBeConsumedByChild", "querySelector", "cursor", "overflowY", "match", "maxScrollTop", "clientHeight", "overflowX", "maxScrollLeft", "clientWidth", "mousewheelHandler", "shouldPrevent", "useBothWheelAxes", "wheelSpeed", "ctrl<PERSON>ey", "onwheel", "onmousew<PERSON><PERSON>", "touch", "startOffset", "startTime", "speed", "easingLoop", "magnitudeX", "magnitudeY", "scrollY", "applyTouchMove", "differenceX", "differenceY", "getTouch", "targetTouches", "<PERSON><PERSON><PERSON><PERSON>", "pointerType", "buttons", "MSPOINTER_TYPE_MOUSE", "touchStart", "Date", "getTime", "clearInterval", "touchMove", "currentOffset", "currentTime", "timeGap", "touchEnd", "swipeEasing", "setInterval", "isInitialized", "PointerEvent", "MSPointerEvent", "defaultSettings", "PerfectScrollbar", "userSettings", "nodeName", "blur", "isNegativeScroll", "originalScrollLeft", "result", "setAttribute", "railXStyle", "isNaN", "display", "marginLeft", "marginRight", "railYStyle", "borderTopWidth", "borderBottomWidth", "marginTop", "marginBottom", "handler<PERSON>ame", "lastScrollTop", "lastScrollLeft", "onScroll", "update", "destroy", "removePsClasses", "split", "join"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/perfect-scrollbar/dist/perfect-scrollbar.esm.js"], "sourcesContent": ["/*!\n * perfect-scrollbar v1.5.6\n * Copyright 2024 <PERSON><PERSON><PERSON>, MDBootstrap and Contributors\n * Licensed under MIT\n */\n\nfunction get(element) {\n  return getComputedStyle(element);\n}\n\nfunction set(element, obj) {\n  for (var key in obj) {\n    var val = obj[key];\n    if (typeof val === 'number') {\n      val = val + \"px\";\n    }\n    element.style[key] = val;\n  }\n  return element;\n}\n\nfunction div(className) {\n  var div = document.createElement('div');\n  div.className = className;\n  return div;\n}\n\nvar elMatches =\n  typeof Element !== 'undefined' &&\n  (Element.prototype.matches ||\n    Element.prototype.webkitMatchesSelector ||\n    Element.prototype.mozMatchesSelector ||\n    Element.prototype.msMatchesSelector);\n\nfunction matches(element, query) {\n  if (!elMatches) {\n    throw new Error('No element matching method supported');\n  }\n\n  return elMatches.call(element, query);\n}\n\nfunction remove(element) {\n  if (element.remove) {\n    element.remove();\n  } else {\n    if (element.parentNode) {\n      element.parentNode.removeChild(element);\n    }\n  }\n}\n\nfunction queryChildren(element, selector) {\n  return Array.prototype.filter.call(element.children, function (child) { return matches(child, selector); }\n  );\n}\n\nvar cls = {\n  main: 'ps',\n  rtl: 'ps__rtl',\n  element: {\n    thumb: function (x) { return (\"ps__thumb-\" + x); },\n    rail: function (x) { return (\"ps__rail-\" + x); },\n    consuming: 'ps__child--consume',\n  },\n  state: {\n    focus: 'ps--focus',\n    clicking: 'ps--clicking',\n    active: function (x) { return (\"ps--active-\" + x); },\n    scrolling: function (x) { return (\"ps--scrolling-\" + x); },\n  },\n};\n\n/*\n * Helper methods\n */\nvar scrollingClassTimeout = { x: null, y: null };\n\nfunction addScrollingClass(i, x) {\n  var classList = i.element.classList;\n  var className = cls.state.scrolling(x);\n\n  if (classList.contains(className)) {\n    clearTimeout(scrollingClassTimeout[x]);\n  } else {\n    classList.add(className);\n  }\n}\n\nfunction removeScrollingClass(i, x) {\n  scrollingClassTimeout[x] = setTimeout(\n    function () { return i.isAlive && i.element.classList.remove(cls.state.scrolling(x)); },\n    i.settings.scrollingThreshold\n  );\n}\n\nfunction setScrollingClassInstantly(i, x) {\n  addScrollingClass(i, x);\n  removeScrollingClass(i, x);\n}\n\nvar EventElement = function EventElement(element) {\n  this.element = element;\n  this.handlers = {};\n};\n\nvar prototypeAccessors = { isEmpty: { configurable: true } };\n\nEventElement.prototype.bind = function bind (eventName, handler) {\n  if (typeof this.handlers[eventName] === 'undefined') {\n    this.handlers[eventName] = [];\n  }\n  this.handlers[eventName].push(handler);\n  this.element.addEventListener(eventName, handler, false);\n};\n\nEventElement.prototype.unbind = function unbind (eventName, target) {\n    var this$1 = this;\n\n  this.handlers[eventName] = this.handlers[eventName].filter(function (handler) {\n    if (target && handler !== target) {\n      return true;\n    }\n    this$1.element.removeEventListener(eventName, handler, false);\n    return false;\n  });\n};\n\nEventElement.prototype.unbindAll = function unbindAll () {\n  for (var name in this.handlers) {\n    this.unbind(name);\n  }\n};\n\nprototypeAccessors.isEmpty.get = function () {\n    var this$1 = this;\n\n  return Object.keys(this.handlers).every(\n    function (key) { return this$1.handlers[key].length === 0; }\n  );\n};\n\nObject.defineProperties( EventElement.prototype, prototypeAccessors );\n\nvar EventManager = function EventManager() {\n  this.eventElements = [];\n};\n\nEventManager.prototype.eventElement = function eventElement (element) {\n  var ee = this.eventElements.filter(function (ee) { return ee.element === element; })[0];\n  if (!ee) {\n    ee = new EventElement(element);\n    this.eventElements.push(ee);\n  }\n  return ee;\n};\n\nEventManager.prototype.bind = function bind (element, eventName, handler) {\n  this.eventElement(element).bind(eventName, handler);\n};\n\nEventManager.prototype.unbind = function unbind (element, eventName, handler) {\n  var ee = this.eventElement(element);\n  ee.unbind(eventName, handler);\n\n  if (ee.isEmpty) {\n    // remove\n    this.eventElements.splice(this.eventElements.indexOf(ee), 1);\n  }\n};\n\nEventManager.prototype.unbindAll = function unbindAll () {\n  this.eventElements.forEach(function (e) { return e.unbindAll(); });\n  this.eventElements = [];\n};\n\nEventManager.prototype.once = function once (element, eventName, handler) {\n  var ee = this.eventElement(element);\n  var onceHandler = function (evt) {\n    ee.unbind(eventName, onceHandler);\n    handler(evt);\n  };\n  ee.bind(eventName, onceHandler);\n};\n\nfunction createEvent(name) {\n  if (typeof window.CustomEvent === 'function') {\n    return new CustomEvent(name);\n  }\n\n  var evt = document.createEvent('CustomEvent');\n  evt.initCustomEvent(name, false, false, undefined);\n  return evt;\n}\n\nfunction processScrollDiff (i, axis, diff, useScrollingClass, forceFireReachEvent) {\n  if ( useScrollingClass === void 0 ) useScrollingClass = true;\n  if ( forceFireReachEvent === void 0 ) forceFireReachEvent = false;\n\n  var fields;\n  if (axis === 'top') {\n    fields = ['contentHeight', 'containerHeight', 'scrollTop', 'y', 'up', 'down'];\n  } else if (axis === 'left') {\n    fields = ['contentWidth', 'containerWidth', 'scrollLeft', 'x', 'left', 'right'];\n  } else {\n    throw new Error('A proper axis should be provided');\n  }\n\n  processScrollDiff$1(i, diff, fields, useScrollingClass, forceFireReachEvent);\n}\n\nfunction processScrollDiff$1(\n  i,\n  diff,\n  ref,\n  useScrollingClass,\n  forceFireReachEvent\n) {\n  var contentHeight = ref[0];\n  var containerHeight = ref[1];\n  var scrollTop = ref[2];\n  var y = ref[3];\n  var up = ref[4];\n  var down = ref[5];\n  if ( useScrollingClass === void 0 ) useScrollingClass = true;\n  if ( forceFireReachEvent === void 0 ) forceFireReachEvent = false;\n\n  var element = i.element;\n\n  // reset reach\n  i.reach[y] = null;\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] < 1) {\n    i.reach[y] = 'start';\n  }\n\n  // 1 for subpixel rounding\n  if (element[scrollTop] > i[contentHeight] - i[containerHeight] - 1) {\n    i.reach[y] = 'end';\n  }\n\n  if (diff) {\n    element.dispatchEvent(createEvent((\"ps-scroll-\" + y)));\n\n    if (diff < 0) {\n      element.dispatchEvent(createEvent((\"ps-scroll-\" + up)));\n    } else if (diff > 0) {\n      element.dispatchEvent(createEvent((\"ps-scroll-\" + down)));\n    }\n\n    if (useScrollingClass) {\n      setScrollingClassInstantly(i, y);\n    }\n  }\n\n  if (i.reach[y] && (diff || forceFireReachEvent)) {\n    element.dispatchEvent(createEvent((\"ps-\" + y + \"-reach-\" + (i.reach[y]))));\n  }\n}\n\nfunction toInt(x) {\n  return parseInt(x, 10) || 0;\n}\n\nfunction isEditable(el) {\n  return (\n    matches(el, 'input,[contenteditable]') ||\n    matches(el, 'select,[contenteditable]') ||\n    matches(el, 'textarea,[contenteditable]') ||\n    matches(el, 'button,[contenteditable]')\n  );\n}\n\nfunction outerWidth(element) {\n  var styles = get(element);\n  return (\n    toInt(styles.width) +\n    toInt(styles.paddingLeft) +\n    toInt(styles.paddingRight) +\n    toInt(styles.borderLeftWidth) +\n    toInt(styles.borderRightWidth)\n  );\n}\n\nvar env = {\n  isWebKit:\n    typeof document !== 'undefined' &&\n    'WebkitAppearance' in document.documentElement.style,\n  supportsTouch:\n    typeof window !== 'undefined' &&\n    ('ontouchstart' in window ||\n      ('maxTouchPoints' in window.navigator &&\n        window.navigator.maxTouchPoints > 0) ||\n      (window.DocumentTouch && document instanceof window.DocumentTouch)),\n  supportsIePointer:\n    typeof navigator !== 'undefined' && navigator.msMaxTouchPoints,\n  isChrome:\n    typeof navigator !== 'undefined' &&\n    /Chrome/i.test(navigator && navigator.userAgent),\n};\n\n/* eslint-disable no-lonely-if */\n\nfunction updateGeometry (i) {\n  var element = i.element;\n  var roundedScrollTop = Math.floor(element.scrollTop);\n  var rect = element.getBoundingClientRect();\n\n  i.containerWidth = Math.floor(rect.width);\n  i.containerHeight = Math.floor(rect.height);\n\n  i.contentWidth = element.scrollWidth;\n  i.contentHeight = element.scrollHeight;\n\n  if (!element.contains(i.scrollbarXRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('x')).forEach(function (el) { return remove(el); });\n    element.appendChild(i.scrollbarXRail);\n  }\n  if (!element.contains(i.scrollbarYRail)) {\n    // clean up and append\n    queryChildren(element, cls.element.rail('y')).forEach(function (el) { return remove(el); });\n    element.appendChild(i.scrollbarYRail);\n  }\n\n  if (\n    !i.settings.suppressScrollX &&\n    i.containerWidth + i.settings.scrollXMarginOffset < i.contentWidth\n  ) {\n    i.scrollbarXActive = true;\n    i.railXWidth = i.containerWidth - i.railXMarginWidth;\n    i.railXRatio = i.containerWidth / i.railXWidth;\n    i.scrollbarXWidth = getThumbSize(i, toInt((i.railXWidth * i.containerWidth) / i.contentWidth));\n    i.scrollbarXLeft = toInt(\n      ((i.negativeScrollAdjustment + element.scrollLeft) * (i.railXWidth - i.scrollbarXWidth)) /\n        (i.contentWidth - i.containerWidth)\n    );\n  } else {\n    i.scrollbarXActive = false;\n  }\n\n  if (\n    !i.settings.suppressScrollY &&\n    i.containerHeight + i.settings.scrollYMarginOffset < i.contentHeight\n  ) {\n    i.scrollbarYActive = true;\n    i.railYHeight = i.containerHeight - i.railYMarginHeight;\n    i.railYRatio = i.containerHeight / i.railYHeight;\n    i.scrollbarYHeight = getThumbSize(\n      i,\n      toInt((i.railYHeight * i.containerHeight) / i.contentHeight)\n    );\n    i.scrollbarYTop = toInt(\n      (roundedScrollTop * (i.railYHeight - i.scrollbarYHeight)) /\n        (i.contentHeight - i.containerHeight)\n    );\n  } else {\n    i.scrollbarYActive = false;\n  }\n\n  if (i.scrollbarXLeft >= i.railXWidth - i.scrollbarXWidth) {\n    i.scrollbarXLeft = i.railXWidth - i.scrollbarXWidth;\n  }\n  if (i.scrollbarYTop >= i.railYHeight - i.scrollbarYHeight) {\n    i.scrollbarYTop = i.railYHeight - i.scrollbarYHeight;\n  }\n\n  updateCss(element, i);\n\n  if (i.scrollbarXActive) {\n    element.classList.add(cls.state.active('x'));\n  } else {\n    element.classList.remove(cls.state.active('x'));\n    i.scrollbarXWidth = 0;\n    i.scrollbarXLeft = 0;\n    element.scrollLeft = i.isRtl === true ? i.contentWidth : 0;\n  }\n  if (i.scrollbarYActive) {\n    element.classList.add(cls.state.active('y'));\n  } else {\n    element.classList.remove(cls.state.active('y'));\n    i.scrollbarYHeight = 0;\n    i.scrollbarYTop = 0;\n    element.scrollTop = 0;\n  }\n}\n\nfunction getThumbSize(i, thumbSize) {\n  if (i.settings.minScrollbarLength) {\n    thumbSize = Math.max(thumbSize, i.settings.minScrollbarLength);\n  }\n  if (i.settings.maxScrollbarLength) {\n    thumbSize = Math.min(thumbSize, i.settings.maxScrollbarLength);\n  }\n  return thumbSize;\n}\n\nfunction updateCss(element, i) {\n  var xRailOffset = { width: i.railXWidth };\n  var roundedScrollTop = Math.floor(element.scrollTop);\n\n  if (i.isRtl) {\n    xRailOffset.left =\n      i.negativeScrollAdjustment + element.scrollLeft + i.containerWidth - i.contentWidth;\n  } else {\n    xRailOffset.left = element.scrollLeft;\n  }\n  if (i.isScrollbarXUsingBottom) {\n    xRailOffset.bottom = i.scrollbarXBottom - roundedScrollTop;\n  } else {\n    xRailOffset.top = i.scrollbarXTop + roundedScrollTop;\n  }\n  set(i.scrollbarXRail, xRailOffset);\n\n  var yRailOffset = { top: roundedScrollTop, height: i.railYHeight };\n  if (i.isScrollbarYUsingRight) {\n    if (i.isRtl) {\n      yRailOffset.right =\n        i.contentWidth -\n        (i.negativeScrollAdjustment + element.scrollLeft) -\n        i.scrollbarYRight -\n        i.scrollbarYOuterWidth -\n        9;\n    } else {\n      yRailOffset.right = i.scrollbarYRight - element.scrollLeft;\n    }\n  } else {\n    if (i.isRtl) {\n      yRailOffset.left =\n        i.negativeScrollAdjustment +\n        element.scrollLeft +\n        i.containerWidth * 2 -\n        i.contentWidth -\n        i.scrollbarYLeft -\n        i.scrollbarYOuterWidth;\n    } else {\n      yRailOffset.left = i.scrollbarYLeft + element.scrollLeft;\n    }\n  }\n  set(i.scrollbarYRail, yRailOffset);\n\n  set(i.scrollbarX, {\n    left: i.scrollbarXLeft,\n    width: i.scrollbarXWidth - i.railBorderXWidth,\n  });\n  set(i.scrollbarY, {\n    top: i.scrollbarYTop,\n    height: i.scrollbarYHeight - i.railBorderYWidth,\n  });\n}\n\n/* eslint-disable */\n\nfunction clickRail (i) {\n  // const element = i.element;\n\n  i.event.bind(i.scrollbarY, 'mousedown', function (e) { return e.stopPropagation(); });\n  i.event.bind(i.scrollbarYRail, 'mousedown', function (e) {\n    var positionTop = e.pageY - window.pageYOffset - i.scrollbarYRail.getBoundingClientRect().top;\n    var direction = positionTop > i.scrollbarYTop ? 1 : -1;\n\n    i.element.scrollTop += direction * i.containerHeight;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n\n  i.event.bind(i.scrollbarX, 'mousedown', function (e) { return e.stopPropagation(); });\n  i.event.bind(i.scrollbarXRail, 'mousedown', function (e) {\n    var positionLeft =\n      e.pageX - window.pageXOffset - i.scrollbarXRail.getBoundingClientRect().left;\n    var direction = positionLeft > i.scrollbarXLeft ? 1 : -1;\n\n    i.element.scrollLeft += direction * i.containerWidth;\n    updateGeometry(i);\n\n    e.stopPropagation();\n  });\n}\n\nvar activeSlider = null; // Variable to track the currently active slider\n\nfunction setupScrollHandlers(i) {\n  bindMouseScrollHandler(i, [\n    'containerHeight',\n    'contentHeight',\n    'pageY',\n    'railYHeight',\n    'scrollbarY',\n    'scrollbarYHeight',\n    'scrollTop',\n    'y',\n    'scrollbarYRail' ]);\n\n  bindMouseScrollHandler(i, [\n    'containerWidth',\n    'contentWidth',\n    'pageX',\n    'railXWidth',\n    'scrollbarX',\n    'scrollbarXWidth',\n    'scrollLeft',\n    'x',\n    'scrollbarXRail' ]);\n}\n\nfunction bindMouseScrollHandler(\n  i,\n  ref\n) {\n  var containerDimension = ref[0];\n  var contentDimension = ref[1];\n  var pageAxis = ref[2];\n  var railDimension = ref[3];\n  var scrollbarAxis = ref[4];\n  var scrollbarDimension = ref[5];\n  var scrollAxis = ref[6];\n  var axis = ref[7];\n  var scrollbarRail = ref[8];\n\n  var element = i.element;\n  var startingScrollPosition = null;\n  var startingMousePagePosition = null;\n  var scrollBy = null;\n\n  function moveHandler(e) {\n    if (e.touches && e.touches[0]) {\n      e[pageAxis] = e.touches[0][(\"page\" + (axis.toUpperCase()))];\n    }\n\n    // Only move if the active slider is the one we started with\n    if (activeSlider === scrollbarAxis) {\n      element[scrollAxis] =\n        startingScrollPosition + scrollBy * (e[pageAxis] - startingMousePagePosition);\n      addScrollingClass(i, axis);\n      updateGeometry(i);\n\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  function endHandler() {\n    removeScrollingClass(i, axis);\n    i[scrollbarRail].classList.remove(cls.state.clicking);\n    document.removeEventListener('mousemove', moveHandler);\n    document.removeEventListener('mouseup', endHandler);\n    document.removeEventListener('touchmove', moveHandler);\n    document.removeEventListener('touchend', endHandler);\n    activeSlider = null; // Reset active slider when interaction ends\n  }\n\n  function bindMoves(e) {\n    if (activeSlider === null) {\n      // Only bind if no slider is currently active\n      activeSlider = scrollbarAxis; // Set current slider as active\n\n      startingScrollPosition = element[scrollAxis];\n      if (e.touches) {\n        e[pageAxis] = e.touches[0][(\"page\" + (axis.toUpperCase()))];\n      }\n      startingMousePagePosition = e[pageAxis];\n      scrollBy =\n        (i[contentDimension] - i[containerDimension]) / (i[railDimension] - i[scrollbarDimension]);\n\n      if (!e.touches) {\n        document.addEventListener('mousemove', moveHandler);\n        document.addEventListener('mouseup', endHandler);\n      } else {\n        document.addEventListener('touchmove', moveHandler, { passive: false });\n        document.addEventListener('touchend', endHandler);\n      }\n\n      i[scrollbarRail].classList.add(cls.state.clicking);\n    }\n\n    e.stopPropagation();\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  i[scrollbarAxis].addEventListener('mousedown', bindMoves);\n  i[scrollbarAxis].addEventListener('touchstart', bindMoves);\n}\n\n/* eslint-disable */\n\nfunction keyboard (i) {\n  var element = i.element;\n\n  var elementHovered = function () { return matches(element, ':hover'); };\n  var scrollbarFocused = function () { return matches(i.scrollbarX, ':focus') || matches(i.scrollbarY, ':focus'); };\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    if (deltaX === 0) {\n      if (!i.scrollbarYActive) {\n        return false;\n      }\n      if (\n        (scrollTop === 0 && deltaY > 0) ||\n        (scrollTop >= i.contentHeight - i.containerHeight && deltaY < 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n\n    var scrollLeft = element.scrollLeft;\n    if (deltaY === 0) {\n      if (!i.scrollbarXActive) {\n        return false;\n      }\n      if (\n        (scrollLeft === 0 && deltaX < 0) ||\n        (scrollLeft >= i.contentWidth - i.containerWidth && deltaX > 0)\n      ) {\n        return !i.settings.wheelPropagation;\n      }\n    }\n    return true;\n  }\n\n  i.event.bind(i.ownerDocument, 'keydown', function (e) {\n    if ((e.isDefaultPrevented && e.isDefaultPrevented()) || e.defaultPrevented) {\n      return;\n    }\n\n    if (!elementHovered() && !scrollbarFocused()) {\n      return;\n    }\n\n    var activeElement = document.activeElement\n      ? document.activeElement\n      : i.ownerDocument.activeElement;\n    if (activeElement) {\n      if (activeElement.tagName === 'IFRAME') {\n        activeElement = activeElement.contentDocument.activeElement;\n      } else {\n        // go deeper if element is a webcomponent\n        while (activeElement.shadowRoot) {\n          activeElement = activeElement.shadowRoot.activeElement;\n        }\n      }\n      if (isEditable(activeElement)) {\n        return;\n      }\n    }\n\n    var deltaX = 0;\n    var deltaY = 0;\n\n    switch (e.which) {\n      case 37: // left\n        if (e.metaKey) {\n          deltaX = -i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = -i.containerWidth;\n        } else {\n          deltaX = -30;\n        }\n        break;\n      case 38: // up\n        if (e.metaKey) {\n          deltaY = i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = 30;\n        }\n        break;\n      case 39: // right\n        if (e.metaKey) {\n          deltaX = i.contentWidth;\n        } else if (e.altKey) {\n          deltaX = i.containerWidth;\n        } else {\n          deltaX = 30;\n        }\n        break;\n      case 40: // down\n        if (e.metaKey) {\n          deltaY = -i.contentHeight;\n        } else if (e.altKey) {\n          deltaY = -i.containerHeight;\n        } else {\n          deltaY = -30;\n        }\n        break;\n      case 32: // space bar\n        if (e.shiftKey) {\n          deltaY = i.containerHeight;\n        } else {\n          deltaY = -i.containerHeight;\n        }\n        break;\n      case 33: // page up\n        deltaY = i.containerHeight;\n        break;\n      case 34: // page down\n        deltaY = -i.containerHeight;\n        break;\n      case 36: // home\n        deltaY = i.contentHeight;\n        break;\n      case 35: // end\n        deltaY = -i.contentHeight;\n        break;\n      default:\n        return;\n    }\n\n    if (i.settings.suppressScrollX && deltaX !== 0) {\n      return;\n    }\n    if (i.settings.suppressScrollY && deltaY !== 0) {\n      return;\n    }\n\n    element.scrollTop -= deltaY;\n    element.scrollLeft += deltaX;\n    updateGeometry(i);\n\n    if (shouldPreventDefault(deltaX, deltaY)) {\n      e.preventDefault();\n    }\n  });\n}\n\n/* eslint-disable */\n\nfunction wheel (i) {\n  var element = i.element;\n\n  function shouldPreventDefault(deltaX, deltaY) {\n    var roundedScrollTop = Math.floor(element.scrollTop);\n    var isTop = element.scrollTop === 0;\n    var isBottom = roundedScrollTop + element.offsetHeight === element.scrollHeight;\n    var isLeft = element.scrollLeft === 0;\n    var isRight = element.scrollLeft + element.offsetWidth === element.scrollWidth;\n\n    var hitsBound;\n\n    // pick axis with primary direction\n    if (Math.abs(deltaY) > Math.abs(deltaX)) {\n      hitsBound = isTop || isBottom;\n    } else {\n      hitsBound = isLeft || isRight;\n    }\n\n    return hitsBound ? !i.settings.wheelPropagation : true;\n  }\n\n  function getDeltaFromEvent(e) {\n    var deltaX = e.deltaX;\n    var deltaY = -1 * e.deltaY;\n\n    if (typeof deltaX === 'undefined' || typeof deltaY === 'undefined') {\n      // OS X Safari\n      deltaX = (-1 * e.wheelDeltaX) / 6;\n      deltaY = e.wheelDeltaY / 6;\n    }\n\n    if (e.deltaMode && e.deltaMode === 1) {\n      // Firefox in deltaMode 1: Line scrolling\n      deltaX *= 10;\n      deltaY *= 10;\n    }\n\n    if (deltaX !== deltaX && deltaY !== deltaY /* NaN checks */) {\n      // IE in some mouse drivers\n      deltaX = 0;\n      deltaY = e.wheelDelta;\n    }\n\n    if (e.shiftKey) {\n      // reverse axis with shift key\n      return [-deltaY, -deltaX];\n    }\n    return [deltaX, deltaY];\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    // FIXME: this is a workaround for <select> issue in FF and IE #571\n    if (!env.isWebKit && element.querySelector('select:focus')) {\n      return true;\n    }\n\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    var cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function mousewheelHandler(e) {\n    var ref = getDeltaFromEvent(e);\n    var deltaX = ref[0];\n    var deltaY = ref[1];\n\n    if (shouldBeConsumedByChild(e.target, deltaX, deltaY)) {\n      return;\n    }\n\n    var shouldPrevent = false;\n    if (!i.settings.useBothWheelAxes) {\n      // deltaX will only be used for horizontal scrolling and deltaY will\n      // only be used for vertical scrolling - this is the default\n      element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      element.scrollLeft += deltaX * i.settings.wheelSpeed;\n    } else if (i.scrollbarYActive && !i.scrollbarXActive) {\n      // only vertical scrollbar is active and useBothWheelAxes option is\n      // active, so let's scroll vertical bar using both mouse wheel axes\n      if (deltaY) {\n        element.scrollTop -= deltaY * i.settings.wheelSpeed;\n      } else {\n        element.scrollTop += deltaX * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    } else if (i.scrollbarXActive && !i.scrollbarYActive) {\n      // useBothWheelAxes and only horizontal bar is active, so use both\n      // wheel axes for horizontal bar\n      if (deltaX) {\n        element.scrollLeft += deltaX * i.settings.wheelSpeed;\n      } else {\n        element.scrollLeft -= deltaY * i.settings.wheelSpeed;\n      }\n      shouldPrevent = true;\n    }\n\n    updateGeometry(i);\n\n    shouldPrevent = shouldPrevent || shouldPreventDefault(deltaX, deltaY);\n    if (shouldPrevent && !e.ctrlKey) {\n      e.stopPropagation();\n      e.preventDefault();\n    }\n  }\n\n  if (typeof window.onwheel !== 'undefined') {\n    i.event.bind(element, 'wheel', mousewheelHandler);\n  } else if (typeof window.onmousewheel !== 'undefined') {\n    i.event.bind(element, 'mousewheel', mousewheelHandler);\n  }\n}\n\nfunction touch (i) {\n  if (!env.supportsTouch && !env.supportsIePointer) {\n    return;\n  }\n\n  var element = i.element;\n\n  var state = {\n    startOffset: {},\n    startTime: 0,\n    speed: {},\n    easingLoop: null,\n  };\n\n  function shouldPrevent(deltaX, deltaY) {\n    var scrollTop = Math.floor(element.scrollTop);\n    var scrollLeft = element.scrollLeft;\n    var magnitudeX = Math.abs(deltaX);\n    var magnitudeY = Math.abs(deltaY);\n\n    if (magnitudeY > magnitudeX) {\n      // user is perhaps trying to swipe up/down the page\n\n      if (\n        (deltaY < 0 && scrollTop === i.contentHeight - i.containerHeight) ||\n        (deltaY > 0 && scrollTop === 0)\n      ) {\n        // set prevent for mobile Chrome refresh\n        return window.scrollY === 0 && deltaY > 0 && env.isChrome;\n      }\n    } else if (magnitudeX > magnitudeY) {\n      // user is perhaps trying to swipe left/right across the page\n\n      if (\n        (deltaX < 0 && scrollLeft === i.contentWidth - i.containerWidth) ||\n        (deltaX > 0 && scrollLeft === 0)\n      ) {\n        return true;\n      }\n    }\n\n    return true;\n  }\n\n  function applyTouchMove(differenceX, differenceY) {\n    element.scrollTop -= differenceY;\n    element.scrollLeft -= differenceX;\n\n    updateGeometry(i);\n  }\n\n  function getTouch(e) {\n    if (e.targetTouches) {\n      return e.targetTouches[0];\n    }\n    // Maybe IE pointer\n    return e;\n  }\n\n  function shouldHandle(e) {\n    if (e.target === i.scrollbarX || e.target === i.scrollbarY) {\n      return false;\n    }\n    if (e.pointerType && e.pointerType === 'pen' && e.buttons === 0) {\n      return false;\n    }\n    if (e.targetTouches && e.targetTouches.length === 1) {\n      return true;\n    }\n    if (e.pointerType && e.pointerType !== 'mouse' && e.pointerType !== e.MSPOINTER_TYPE_MOUSE) {\n      return true;\n    }\n    return false;\n  }\n\n  function touchStart(e) {\n    if (!shouldHandle(e)) {\n      return;\n    }\n\n    var touch = getTouch(e);\n\n    state.startOffset.pageX = touch.pageX;\n    state.startOffset.pageY = touch.pageY;\n\n    state.startTime = new Date().getTime();\n\n    if (state.easingLoop !== null) {\n      clearInterval(state.easingLoop);\n    }\n  }\n\n  function shouldBeConsumedByChild(target, deltaX, deltaY) {\n    if (!element.contains(target)) {\n      return false;\n    }\n\n    var cursor = target;\n\n    while (cursor && cursor !== element) {\n      if (cursor.classList.contains(cls.element.consuming)) {\n        return true;\n      }\n\n      var style = get(cursor);\n\n      // if deltaY && vertical scrollable\n      if (deltaY && style.overflowY.match(/(scroll|auto)/)) {\n        var maxScrollTop = cursor.scrollHeight - cursor.clientHeight;\n        if (maxScrollTop > 0) {\n          if (\n            (cursor.scrollTop > 0 && deltaY < 0) ||\n            (cursor.scrollTop < maxScrollTop && deltaY > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n      // if deltaX && horizontal scrollable\n      if (deltaX && style.overflowX.match(/(scroll|auto)/)) {\n        var maxScrollLeft = cursor.scrollWidth - cursor.clientWidth;\n        if (maxScrollLeft > 0) {\n          if (\n            (cursor.scrollLeft > 0 && deltaX < 0) ||\n            (cursor.scrollLeft < maxScrollLeft && deltaX > 0)\n          ) {\n            return true;\n          }\n        }\n      }\n\n      cursor = cursor.parentNode;\n    }\n\n    return false;\n  }\n\n  function touchMove(e) {\n    if (shouldHandle(e)) {\n      var touch = getTouch(e);\n\n      var currentOffset = { pageX: touch.pageX, pageY: touch.pageY };\n\n      var differenceX = currentOffset.pageX - state.startOffset.pageX;\n      var differenceY = currentOffset.pageY - state.startOffset.pageY;\n\n      if (shouldBeConsumedByChild(e.target, differenceX, differenceY)) {\n        return;\n      }\n\n      applyTouchMove(differenceX, differenceY);\n      state.startOffset = currentOffset;\n\n      var currentTime = new Date().getTime();\n\n      var timeGap = currentTime - state.startTime;\n      if (timeGap > 0) {\n        state.speed.x = differenceX / timeGap;\n        state.speed.y = differenceY / timeGap;\n        state.startTime = currentTime;\n      }\n\n      if (shouldPrevent(differenceX, differenceY)) {\n        // Prevent the default behavior if the event is cancelable\n        if (e.cancelable) {\n          e.preventDefault();\n        }\n      }\n    }\n  }\n\n  function touchEnd() {\n    if (i.settings.swipeEasing) {\n      clearInterval(state.easingLoop);\n      state.easingLoop = setInterval(function () {\n        if (i.isInitialized) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        if (!state.speed.x && !state.speed.y) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        if (Math.abs(state.speed.x) < 0.01 && Math.abs(state.speed.y) < 0.01) {\n          clearInterval(state.easingLoop);\n          return;\n        }\n\n        applyTouchMove(state.speed.x * 30, state.speed.y * 30);\n\n        state.speed.x *= 0.8;\n        state.speed.y *= 0.8;\n      }, 10);\n    }\n  }\n\n  if (env.supportsTouch) {\n    i.event.bind(element, 'touchstart', touchStart);\n    i.event.bind(element, 'touchmove', touchMove);\n    i.event.bind(element, 'touchend', touchEnd);\n  } else if (env.supportsIePointer) {\n    if (window.PointerEvent) {\n      i.event.bind(element, 'pointerdown', touchStart);\n      i.event.bind(element, 'pointermove', touchMove);\n      i.event.bind(element, 'pointerup', touchEnd);\n    } else if (window.MSPointerEvent) {\n      i.event.bind(element, 'MSPointerDown', touchStart);\n      i.event.bind(element, 'MSPointerMove', touchMove);\n      i.event.bind(element, 'MSPointerUp', touchEnd);\n    }\n  }\n}\n\n/* eslint-disable */\n\nvar defaultSettings = function () { return ({\n  handlers: ['click-rail', 'drag-thumb', 'keyboard', 'wheel', 'touch'],\n  maxScrollbarLength: null,\n  minScrollbarLength: null,\n  scrollingThreshold: 1000,\n  scrollXMarginOffset: 0,\n  scrollYMarginOffset: 0,\n  suppressScrollX: false,\n  suppressScrollY: false,\n  swipeEasing: true,\n  useBothWheelAxes: false,\n  wheelPropagation: true,\n  wheelSpeed: 1,\n}); };\n\nvar handlers = {\n  'click-rail': clickRail,\n  'drag-thumb': setupScrollHandlers,\n  keyboard: keyboard,\n  wheel: wheel,\n  touch: touch,\n};\n\nvar PerfectScrollbar = function PerfectScrollbar(element, userSettings) {\n  var this$1 = this;\n  if ( userSettings === void 0 ) userSettings = {};\n\n  if (typeof element === 'string') {\n    element = document.querySelector(element);\n  }\n\n  if (!element || !element.nodeName) {\n    throw new Error('no element is specified to initialize PerfectScrollbar');\n  }\n\n  this.element = element;\n\n  element.classList.add(cls.main);\n\n  this.settings = defaultSettings();\n  for (var key in userSettings) {\n    this.settings[key] = userSettings[key];\n  }\n\n  this.containerWidth = null;\n  this.containerHeight = null;\n  this.contentWidth = null;\n  this.contentHeight = null;\n\n  var focus = function () { return element.classList.add(cls.state.focus); };\n  var blur = function () { return element.classList.remove(cls.state.focus); };\n\n  this.isRtl = get(element).direction === 'rtl';\n  if (this.isRtl === true) {\n    element.classList.add(cls.rtl);\n  }\n  this.isNegativeScroll = (function () {\n    var originalScrollLeft = element.scrollLeft;\n    var result = null;\n    element.scrollLeft = -1;\n    result = element.scrollLeft < 0;\n    element.scrollLeft = originalScrollLeft;\n    return result;\n  })();\n  this.negativeScrollAdjustment = this.isNegativeScroll\n    ? element.scrollWidth - element.clientWidth\n    : 0;\n  this.event = new EventManager();\n  this.ownerDocument = element.ownerDocument || document;\n\n  this.scrollbarXRail = div(cls.element.rail('x'));\n  element.appendChild(this.scrollbarXRail);\n  this.scrollbarX = div(cls.element.thumb('x'));\n  this.scrollbarXRail.appendChild(this.scrollbarX);\n  this.scrollbarX.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarX, 'focus', focus);\n  this.event.bind(this.scrollbarX, 'blur', blur);\n  this.scrollbarXActive = null;\n  this.scrollbarXWidth = null;\n  this.scrollbarXLeft = null;\n  var railXStyle = get(this.scrollbarXRail);\n  this.scrollbarXBottom = parseInt(railXStyle.bottom, 10);\n  if (isNaN(this.scrollbarXBottom)) {\n    this.isScrollbarXUsingBottom = false;\n    this.scrollbarXTop = toInt(railXStyle.top);\n  } else {\n    this.isScrollbarXUsingBottom = true;\n  }\n  this.railBorderXWidth = toInt(railXStyle.borderLeftWidth) + toInt(railXStyle.borderRightWidth);\n  // Set rail to display:block to calculate margins\n  set(this.scrollbarXRail, { display: 'block' });\n  this.railXMarginWidth = toInt(railXStyle.marginLeft) + toInt(railXStyle.marginRight);\n  set(this.scrollbarXRail, { display: '' });\n  this.railXWidth = null;\n  this.railXRatio = null;\n\n  this.scrollbarYRail = div(cls.element.rail('y'));\n  element.appendChild(this.scrollbarYRail);\n  this.scrollbarY = div(cls.element.thumb('y'));\n  this.scrollbarYRail.appendChild(this.scrollbarY);\n  this.scrollbarY.setAttribute('tabindex', 0);\n  this.event.bind(this.scrollbarY, 'focus', focus);\n  this.event.bind(this.scrollbarY, 'blur', blur);\n  this.scrollbarYActive = null;\n  this.scrollbarYHeight = null;\n  this.scrollbarYTop = null;\n  var railYStyle = get(this.scrollbarYRail);\n  this.scrollbarYRight = parseInt(railYStyle.right, 10);\n  if (isNaN(this.scrollbarYRight)) {\n    this.isScrollbarYUsingRight = false;\n    this.scrollbarYLeft = toInt(railYStyle.left);\n  } else {\n    this.isScrollbarYUsingRight = true;\n  }\n  this.scrollbarYOuterWidth = this.isRtl ? outerWidth(this.scrollbarY) : null;\n  this.railBorderYWidth = toInt(railYStyle.borderTopWidth) + toInt(railYStyle.borderBottomWidth);\n  set(this.scrollbarYRail, { display: 'block' });\n  this.railYMarginHeight = toInt(railYStyle.marginTop) + toInt(railYStyle.marginBottom);\n  set(this.scrollbarYRail, { display: '' });\n  this.railYHeight = null;\n  this.railYRatio = null;\n\n  this.reach = {\n    x:\n      element.scrollLeft <= 0\n        ? 'start'\n        : element.scrollLeft >= this.contentWidth - this.containerWidth\n        ? 'end'\n        : null,\n    y:\n      element.scrollTop <= 0\n        ? 'start'\n        : element.scrollTop >= this.contentHeight - this.containerHeight\n        ? 'end'\n        : null,\n  };\n\n  this.isAlive = true;\n\n  this.settings.handlers.forEach(function (handlerName) { return handlers[handlerName](this$1); });\n\n  this.lastScrollTop = Math.floor(element.scrollTop); // for onScroll only\n  this.lastScrollLeft = element.scrollLeft; // for onScroll only\n  this.event.bind(this.element, 'scroll', function (e) { return this$1.onScroll(e); });\n  updateGeometry(this);\n};\n\nPerfectScrollbar.prototype.update = function update () {\n  if (!this.isAlive) {\n    return;\n  }\n\n  // Recalcuate negative scrollLeft adjustment\n  this.negativeScrollAdjustment = this.isNegativeScroll\n    ? this.element.scrollWidth - this.element.clientWidth\n    : 0;\n\n  // Recalculate rail margins\n  set(this.scrollbarXRail, { display: 'block' });\n  set(this.scrollbarYRail, { display: 'block' });\n  this.railXMarginWidth =\n    toInt(get(this.scrollbarXRail).marginLeft) +\n    toInt(get(this.scrollbarXRail).marginRight);\n  this.railYMarginHeight =\n    toInt(get(this.scrollbarYRail).marginTop) +\n    toInt(get(this.scrollbarYRail).marginBottom);\n\n  // Hide scrollbars not to affect scrollWidth and scrollHeight\n  set(this.scrollbarXRail, { display: 'none' });\n  set(this.scrollbarYRail, { display: 'none' });\n\n  updateGeometry(this);\n\n  processScrollDiff(this, 'top', 0, false, true);\n  processScrollDiff(this, 'left', 0, false, true);\n\n  set(this.scrollbarXRail, { display: '' });\n  set(this.scrollbarYRail, { display: '' });\n};\n\nPerfectScrollbar.prototype.onScroll = function onScroll (e) {\n  if (!this.isAlive) {\n    return;\n  }\n\n  updateGeometry(this);\n  processScrollDiff(this, 'top', this.element.scrollTop - this.lastScrollTop);\n  processScrollDiff(this, 'left', this.element.scrollLeft - this.lastScrollLeft);\n\n  this.lastScrollTop = Math.floor(this.element.scrollTop);\n  this.lastScrollLeft = this.element.scrollLeft;\n};\n\nPerfectScrollbar.prototype.destroy = function destroy () {\n  if (!this.isAlive) {\n    return;\n  }\n\n  this.event.unbindAll();\n  remove(this.scrollbarX);\n  remove(this.scrollbarY);\n  remove(this.scrollbarXRail);\n  remove(this.scrollbarYRail);\n  this.removePsClasses();\n\n  // unset elements\n  this.element = null;\n  this.scrollbarX = null;\n  this.scrollbarY = null;\n  this.scrollbarXRail = null;\n  this.scrollbarYRail = null;\n\n  this.isAlive = false;\n};\n\nPerfectScrollbar.prototype.removePsClasses = function removePsClasses () {\n  this.element.className = this.element.className\n    .split(' ')\n    .filter(function (name) { return !name.match(/^ps([-_].+|)$/); })\n    .join(' ');\n};\n\nexport default PerfectScrollbar;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,GAAGA,CAACC,OAAO,EAAE;EACpB,OAAOC,gBAAgB,CAACD,OAAO,CAAC;AAClC;AAEA,SAASE,GAAGA,CAACF,OAAO,EAAEG,GAAG,EAAE;EACzB,KAAK,IAAIC,GAAG,IAAID,GAAG,EAAE;IACnB,IAAIE,GAAG,GAAGF,GAAG,CAACC,GAAG,CAAC;IAClB,IAAI,OAAOC,GAAG,KAAK,QAAQ,EAAE;MAC3BA,GAAG,GAAGA,GAAG,GAAG,IAAI;IAClB;IACAL,OAAO,CAACM,KAAK,CAACF,GAAG,CAAC,GAAGC,GAAG;EAC1B;EACA,OAAOL,OAAO;AAChB;AAEA,SAASO,GAAGA,CAACC,SAAS,EAAE;EACtB,IAAID,GAAG,GAAGE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACvCH,GAAG,CAACC,SAAS,GAAGA,SAAS;EACzB,OAAOD,GAAG;AACZ;AAEA,IAAII,SAAS,GACX,OAAOC,OAAO,KAAK,WAAW,KAC7BA,OAAO,CAACC,SAAS,CAACC,OAAO,IACxBF,OAAO,CAACC,SAAS,CAACE,qBAAqB,IACvCH,OAAO,CAACC,SAAS,CAACG,kBAAkB,IACpCJ,OAAO,CAACC,SAAS,CAACI,iBAAiB,CAAC;AAExC,SAASH,OAAOA,CAACd,OAAO,EAAEkB,KAAK,EAAE;EAC/B,IAAI,CAACP,SAAS,EAAE;IACd,MAAM,IAAIQ,KAAK,CAAC,sCAAsC,CAAC;EACzD;EAEA,OAAOR,SAAS,CAACS,IAAI,CAACpB,OAAO,EAAEkB,KAAK,CAAC;AACvC;AAEA,SAASG,MAAMA,CAACrB,OAAO,EAAE;EACvB,IAAIA,OAAO,CAACqB,MAAM,EAAE;IAClBrB,OAAO,CAACqB,MAAM,CAAC,CAAC;EAClB,CAAC,MAAM;IACL,IAAIrB,OAAO,CAACsB,UAAU,EAAE;MACtBtB,OAAO,CAACsB,UAAU,CAACC,WAAW,CAACvB,OAAO,CAAC;IACzC;EACF;AACF;AAEA,SAASwB,aAAaA,CAACxB,OAAO,EAAEyB,QAAQ,EAAE;EACxC,OAAOC,KAAK,CAACb,SAAS,CAACc,MAAM,CAACP,IAAI,CAACpB,OAAO,CAAC4B,QAAQ,EAAE,UAAUC,KAAK,EAAE;IAAE,OAAOf,OAAO,CAACe,KAAK,EAAEJ,QAAQ,CAAC;EAAE,CACzG,CAAC;AACH;AAEA,IAAIK,GAAG,GAAG;EACRC,IAAI,EAAE,IAAI;EACVC,GAAG,EAAE,SAAS;EACdhC,OAAO,EAAE;IACPiC,KAAK,EAAE,SAAAA,CAAUC,CAAC,EAAE;MAAE,OAAQ,YAAY,GAAGA,CAAC;IAAG,CAAC;IAClDC,IAAI,EAAE,SAAAA,CAAUD,CAAC,EAAE;MAAE,OAAQ,WAAW,GAAGA,CAAC;IAAG,CAAC;IAChDE,SAAS,EAAE;EACb,CAAC;EACDC,KAAK,EAAE;IACLC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,cAAc;IACxBC,MAAM,EAAE,SAAAA,CAAUN,CAAC,EAAE;MAAE,OAAQ,aAAa,GAAGA,CAAC;IAAG,CAAC;IACpDO,SAAS,EAAE,SAAAA,CAAUP,CAAC,EAAE;MAAE,OAAQ,gBAAgB,GAAGA,CAAC;IAAG;EAC3D;AACF,CAAC;;AAED;AACA;AACA;AACA,IAAIQ,qBAAqB,GAAG;EAAER,CAAC,EAAE,IAAI;EAAES,CAAC,EAAE;AAAK,CAAC;AAEhD,SAASC,iBAAiBA,CAACC,CAAC,EAAEX,CAAC,EAAE;EAC/B,IAAIY,SAAS,GAAGD,CAAC,CAAC7C,OAAO,CAAC8C,SAAS;EACnC,IAAItC,SAAS,GAAGsB,GAAG,CAACO,KAAK,CAACI,SAAS,CAACP,CAAC,CAAC;EAEtC,IAAIY,SAAS,CAACC,QAAQ,CAACvC,SAAS,CAAC,EAAE;IACjCwC,YAAY,CAACN,qBAAqB,CAACR,CAAC,CAAC,CAAC;EACxC,CAAC,MAAM;IACLY,SAAS,CAACG,GAAG,CAACzC,SAAS,CAAC;EAC1B;AACF;AAEA,SAAS0C,oBAAoBA,CAACL,CAAC,EAAEX,CAAC,EAAE;EAClCQ,qBAAqB,CAACR,CAAC,CAAC,GAAGiB,UAAU,CACnC,YAAY;IAAE,OAAON,CAAC,CAACO,OAAO,IAAIP,CAAC,CAAC7C,OAAO,CAAC8C,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACI,SAAS,CAACP,CAAC,CAAC,CAAC;EAAE,CAAC,EACvFW,CAAC,CAACQ,QAAQ,CAACC,kBACb,CAAC;AACH;AAEA,SAASC,0BAA0BA,CAACV,CAAC,EAAEX,CAAC,EAAE;EACxCU,iBAAiB,CAACC,CAAC,EAAEX,CAAC,CAAC;EACvBgB,oBAAoB,CAACL,CAAC,EAAEX,CAAC,CAAC;AAC5B;AAEA,IAAIsB,YAAY,GAAG,SAASA,YAAYA,CAACxD,OAAO,EAAE;EAChD,IAAI,CAACA,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACyD,QAAQ,GAAG,CAAC,CAAC;AACpB,CAAC;AAED,IAAIC,kBAAkB,GAAG;EAAEC,OAAO,EAAE;IAAEC,YAAY,EAAE;EAAK;AAAE,CAAC;AAE5DJ,YAAY,CAAC3C,SAAS,CAACgD,IAAI,GAAG,SAASA,IAAIA,CAAEC,SAAS,EAAEC,OAAO,EAAE;EAC/D,IAAI,OAAO,IAAI,CAACN,QAAQ,CAACK,SAAS,CAAC,KAAK,WAAW,EAAE;IACnD,IAAI,CAACL,QAAQ,CAACK,SAAS,CAAC,GAAG,EAAE;EAC/B;EACA,IAAI,CAACL,QAAQ,CAACK,SAAS,CAAC,CAACE,IAAI,CAACD,OAAO,CAAC;EACtC,IAAI,CAAC/D,OAAO,CAACiE,gBAAgB,CAACH,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;AAC1D,CAAC;AAEDP,YAAY,CAAC3C,SAAS,CAACqD,MAAM,GAAG,SAASA,MAAMA,CAAEJ,SAAS,EAAEK,MAAM,EAAE;EAChE,IAAIC,MAAM,GAAG,IAAI;EAEnB,IAAI,CAACX,QAAQ,CAACK,SAAS,CAAC,GAAG,IAAI,CAACL,QAAQ,CAACK,SAAS,CAAC,CAACnC,MAAM,CAAC,UAAUoC,OAAO,EAAE;IAC5E,IAAII,MAAM,IAAIJ,OAAO,KAAKI,MAAM,EAAE;MAChC,OAAO,IAAI;IACb;IACAC,MAAM,CAACpE,OAAO,CAACqE,mBAAmB,CAACP,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;IAC7D,OAAO,KAAK;EACd,CAAC,CAAC;AACJ,CAAC;AAEDP,YAAY,CAAC3C,SAAS,CAACyD,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACvD,KAAK,IAAIC,IAAI,IAAI,IAAI,CAACd,QAAQ,EAAE;IAC9B,IAAI,CAACS,MAAM,CAACK,IAAI,CAAC;EACnB;AACF,CAAC;AAEDb,kBAAkB,CAACC,OAAO,CAAC5D,GAAG,GAAG,YAAY;EACzC,IAAIqE,MAAM,GAAG,IAAI;EAEnB,OAAOI,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChB,QAAQ,CAAC,CAACiB,KAAK,CACrC,UAAUtE,GAAG,EAAE;IAAE,OAAOgE,MAAM,CAACX,QAAQ,CAACrD,GAAG,CAAC,CAACuE,MAAM,KAAK,CAAC;EAAE,CAC7D,CAAC;AACH,CAAC;AAEDH,MAAM,CAACI,gBAAgB,CAAEpB,YAAY,CAAC3C,SAAS,EAAE6C,kBAAmB,CAAC;AAErE,IAAImB,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EACzC,IAAI,CAACC,aAAa,GAAG,EAAE;AACzB,CAAC;AAEDD,YAAY,CAAChE,SAAS,CAACkE,YAAY,GAAG,SAASA,YAAYA,CAAE/E,OAAO,EAAE;EACpE,IAAIgF,EAAE,GAAG,IAAI,CAACF,aAAa,CAACnD,MAAM,CAAC,UAAUqD,EAAE,EAAE;IAAE,OAAOA,EAAE,CAAChF,OAAO,KAAKA,OAAO;EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvF,IAAI,CAACgF,EAAE,EAAE;IACPA,EAAE,GAAG,IAAIxB,YAAY,CAACxD,OAAO,CAAC;IAC9B,IAAI,CAAC8E,aAAa,CAACd,IAAI,CAACgB,EAAE,CAAC;EAC7B;EACA,OAAOA,EAAE;AACX,CAAC;AAEDH,YAAY,CAAChE,SAAS,CAACgD,IAAI,GAAG,SAASA,IAAIA,CAAE7D,OAAO,EAAE8D,SAAS,EAAEC,OAAO,EAAE;EACxE,IAAI,CAACgB,YAAY,CAAC/E,OAAO,CAAC,CAAC6D,IAAI,CAACC,SAAS,EAAEC,OAAO,CAAC;AACrD,CAAC;AAEDc,YAAY,CAAChE,SAAS,CAACqD,MAAM,GAAG,SAASA,MAAMA,CAAElE,OAAO,EAAE8D,SAAS,EAAEC,OAAO,EAAE;EAC5E,IAAIiB,EAAE,GAAG,IAAI,CAACD,YAAY,CAAC/E,OAAO,CAAC;EACnCgF,EAAE,CAACd,MAAM,CAACJ,SAAS,EAAEC,OAAO,CAAC;EAE7B,IAAIiB,EAAE,CAACrB,OAAO,EAAE;IACd;IACA,IAAI,CAACmB,aAAa,CAACG,MAAM,CAAC,IAAI,CAACH,aAAa,CAACI,OAAO,CAACF,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9D;AACF,CAAC;AAEDH,YAAY,CAAChE,SAAS,CAACyD,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACvD,IAAI,CAACQ,aAAa,CAACK,OAAO,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACd,SAAS,CAAC,CAAC;EAAE,CAAC,CAAC;EAClE,IAAI,CAACQ,aAAa,GAAG,EAAE;AACzB,CAAC;AAEDD,YAAY,CAAChE,SAAS,CAACwE,IAAI,GAAG,SAASA,IAAIA,CAAErF,OAAO,EAAE8D,SAAS,EAAEC,OAAO,EAAE;EACxE,IAAIiB,EAAE,GAAG,IAAI,CAACD,YAAY,CAAC/E,OAAO,CAAC;EACnC,IAAIsF,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAE;IAC/BP,EAAE,CAACd,MAAM,CAACJ,SAAS,EAAEwB,WAAW,CAAC;IACjCvB,OAAO,CAACwB,GAAG,CAAC;EACd,CAAC;EACDP,EAAE,CAACnB,IAAI,CAACC,SAAS,EAAEwB,WAAW,CAAC;AACjC,CAAC;AAED,SAASE,WAAWA,CAACjB,IAAI,EAAE;EACzB,IAAI,OAAOkB,MAAM,CAACC,WAAW,KAAK,UAAU,EAAE;IAC5C,OAAO,IAAIA,WAAW,CAACnB,IAAI,CAAC;EAC9B;EAEA,IAAIgB,GAAG,GAAG9E,QAAQ,CAAC+E,WAAW,CAAC,aAAa,CAAC;EAC7CD,GAAG,CAACI,eAAe,CAACpB,IAAI,EAAE,KAAK,EAAE,KAAK,EAAEqB,SAAS,CAAC;EAClD,OAAOL,GAAG;AACZ;AAEA,SAASM,iBAAiBA,CAAEhD,CAAC,EAAEiD,IAAI,EAAEC,IAAI,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAE;EACjF,IAAKD,iBAAiB,KAAK,KAAK,CAAC,EAAGA,iBAAiB,GAAG,IAAI;EAC5D,IAAKC,mBAAmB,KAAK,KAAK,CAAC,EAAGA,mBAAmB,GAAG,KAAK;EAEjE,IAAIC,MAAM;EACV,IAAIJ,IAAI,KAAK,KAAK,EAAE;IAClBI,MAAM,GAAG,CAAC,eAAe,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC;EAC/E,CAAC,MAAM,IAAIJ,IAAI,KAAK,MAAM,EAAE;IAC1BI,MAAM,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC;EACjF,CAAC,MAAM;IACL,MAAM,IAAI/E,KAAK,CAAC,kCAAkC,CAAC;EACrD;EAEAgF,mBAAmB,CAACtD,CAAC,EAAEkD,IAAI,EAAEG,MAAM,EAAEF,iBAAiB,EAAEC,mBAAmB,CAAC;AAC9E;AAEA,SAASE,mBAAmBA,CAC1BtD,CAAC,EACDkD,IAAI,EACJK,GAAG,EACHJ,iBAAiB,EACjBC,mBAAmB,EACnB;EACA,IAAII,aAAa,GAAGD,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAIE,eAAe,GAAGF,GAAG,CAAC,CAAC,CAAC;EAC5B,IAAIG,SAAS,GAAGH,GAAG,CAAC,CAAC,CAAC;EACtB,IAAIzD,CAAC,GAAGyD,GAAG,CAAC,CAAC,CAAC;EACd,IAAII,EAAE,GAAGJ,GAAG,CAAC,CAAC,CAAC;EACf,IAAIK,IAAI,GAAGL,GAAG,CAAC,CAAC,CAAC;EACjB,IAAKJ,iBAAiB,KAAK,KAAK,CAAC,EAAGA,iBAAiB,GAAG,IAAI;EAC5D,IAAKC,mBAAmB,KAAK,KAAK,CAAC,EAAGA,mBAAmB,GAAG,KAAK;EAEjE,IAAIjG,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;;EAEvB;EACA6C,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAC,GAAG,IAAI;;EAEjB;EACA,IAAI3C,OAAO,CAACuG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC1B1D,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAC,GAAG,OAAO;EACtB;;EAEA;EACA,IAAI3C,OAAO,CAACuG,SAAS,CAAC,GAAG1D,CAAC,CAACwD,aAAa,CAAC,GAAGxD,CAAC,CAACyD,eAAe,CAAC,GAAG,CAAC,EAAE;IAClEzD,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAC,GAAG,KAAK;EACpB;EAEA,IAAIoD,IAAI,EAAE;IACR/F,OAAO,CAAC2G,aAAa,CAACnB,WAAW,CAAE,YAAY,GAAG7C,CAAE,CAAC,CAAC;IAEtD,IAAIoD,IAAI,GAAG,CAAC,EAAE;MACZ/F,OAAO,CAAC2G,aAAa,CAACnB,WAAW,CAAE,YAAY,GAAGgB,EAAG,CAAC,CAAC;IACzD,CAAC,MAAM,IAAIT,IAAI,GAAG,CAAC,EAAE;MACnB/F,OAAO,CAAC2G,aAAa,CAACnB,WAAW,CAAE,YAAY,GAAGiB,IAAK,CAAC,CAAC;IAC3D;IAEA,IAAIT,iBAAiB,EAAE;MACrBzC,0BAA0B,CAACV,CAAC,EAAEF,CAAC,CAAC;IAClC;EACF;EAEA,IAAIE,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAC,KAAKoD,IAAI,IAAIE,mBAAmB,CAAC,EAAE;IAC/CjG,OAAO,CAAC2G,aAAa,CAACnB,WAAW,CAAE,KAAK,GAAG7C,CAAC,GAAG,SAAS,GAAIE,CAAC,CAAC6D,KAAK,CAAC/D,CAAC,CAAG,CAAC,CAAC;EAC5E;AACF;AAEA,SAASiE,KAAKA,CAAC1E,CAAC,EAAE;EAChB,OAAO2E,QAAQ,CAAC3E,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;AAC7B;AAEA,SAAS4E,UAAUA,CAACC,EAAE,EAAE;EACtB,OACEjG,OAAO,CAACiG,EAAE,EAAE,yBAAyB,CAAC,IACtCjG,OAAO,CAACiG,EAAE,EAAE,0BAA0B,CAAC,IACvCjG,OAAO,CAACiG,EAAE,EAAE,4BAA4B,CAAC,IACzCjG,OAAO,CAACiG,EAAE,EAAE,0BAA0B,CAAC;AAE3C;AAEA,SAASC,UAAUA,CAAChH,OAAO,EAAE;EAC3B,IAAIiH,MAAM,GAAGlH,GAAG,CAACC,OAAO,CAAC;EACzB,OACE4G,KAAK,CAACK,MAAM,CAACC,KAAK,CAAC,GACnBN,KAAK,CAACK,MAAM,CAACE,WAAW,CAAC,GACzBP,KAAK,CAACK,MAAM,CAACG,YAAY,CAAC,GAC1BR,KAAK,CAACK,MAAM,CAACI,eAAe,CAAC,GAC7BT,KAAK,CAACK,MAAM,CAACK,gBAAgB,CAAC;AAElC;AAEA,IAAIC,GAAG,GAAG;EACRC,QAAQ,EACN,OAAO/G,QAAQ,KAAK,WAAW,IAC/B,kBAAkB,IAAIA,QAAQ,CAACgH,eAAe,CAACnH,KAAK;EACtDoH,aAAa,EACX,OAAOjC,MAAM,KAAK,WAAW,KAC5B,cAAc,IAAIA,MAAM,IACtB,gBAAgB,IAAIA,MAAM,CAACkC,SAAS,IACnClC,MAAM,CAACkC,SAAS,CAACC,cAAc,GAAG,CAAE,IACrCnC,MAAM,CAACoC,aAAa,IAAIpH,QAAQ,YAAYgF,MAAM,CAACoC,aAAc,CAAC;EACvEC,iBAAiB,EACf,OAAOH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACI,gBAAgB;EAChEC,QAAQ,EACN,OAAOL,SAAS,KAAK,WAAW,IAChC,SAAS,CAACM,IAAI,CAACN,SAAS,IAAIA,SAAS,CAACO,SAAS;AACnD,CAAC;;AAED;;AAEA,SAASC,cAAcA,CAAEtF,CAAC,EAAE;EAC1B,IAAI7C,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EACvB,IAAIoI,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;EACpD,IAAIgC,IAAI,GAAGvI,OAAO,CAACwI,qBAAqB,CAAC,CAAC;EAE1C3F,CAAC,CAAC4F,cAAc,GAAGJ,IAAI,CAACC,KAAK,CAACC,IAAI,CAACrB,KAAK,CAAC;EACzCrE,CAAC,CAACyD,eAAe,GAAG+B,IAAI,CAACC,KAAK,CAACC,IAAI,CAACG,MAAM,CAAC;EAE3C7F,CAAC,CAAC8F,YAAY,GAAG3I,OAAO,CAAC4I,WAAW;EACpC/F,CAAC,CAACwD,aAAa,GAAGrG,OAAO,CAAC6I,YAAY;EAEtC,IAAI,CAAC7I,OAAO,CAAC+C,QAAQ,CAACF,CAAC,CAACiG,cAAc,CAAC,EAAE;IACvC;IACAtH,aAAa,CAACxB,OAAO,EAAE8B,GAAG,CAAC9B,OAAO,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACgD,OAAO,CAAC,UAAU4B,EAAE,EAAE;MAAE,OAAO1F,MAAM,CAAC0F,EAAE,CAAC;IAAE,CAAC,CAAC;IAC3F/G,OAAO,CAAC+I,WAAW,CAAClG,CAAC,CAACiG,cAAc,CAAC;EACvC;EACA,IAAI,CAAC9I,OAAO,CAAC+C,QAAQ,CAACF,CAAC,CAACmG,cAAc,CAAC,EAAE;IACvC;IACAxH,aAAa,CAACxB,OAAO,EAAE8B,GAAG,CAAC9B,OAAO,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACgD,OAAO,CAAC,UAAU4B,EAAE,EAAE;MAAE,OAAO1F,MAAM,CAAC0F,EAAE,CAAC;IAAE,CAAC,CAAC;IAC3F/G,OAAO,CAAC+I,WAAW,CAAClG,CAAC,CAACmG,cAAc,CAAC;EACvC;EAEA,IACE,CAACnG,CAAC,CAACQ,QAAQ,CAAC4F,eAAe,IAC3BpG,CAAC,CAAC4F,cAAc,GAAG5F,CAAC,CAACQ,QAAQ,CAAC6F,mBAAmB,GAAGrG,CAAC,CAAC8F,YAAY,EAClE;IACA9F,CAAC,CAACsG,gBAAgB,GAAG,IAAI;IACzBtG,CAAC,CAACuG,UAAU,GAAGvG,CAAC,CAAC4F,cAAc,GAAG5F,CAAC,CAACwG,gBAAgB;IACpDxG,CAAC,CAACyG,UAAU,GAAGzG,CAAC,CAAC4F,cAAc,GAAG5F,CAAC,CAACuG,UAAU;IAC9CvG,CAAC,CAAC0G,eAAe,GAAGC,YAAY,CAAC3G,CAAC,EAAE+D,KAAK,CAAE/D,CAAC,CAACuG,UAAU,GAAGvG,CAAC,CAAC4F,cAAc,GAAI5F,CAAC,CAAC8F,YAAY,CAAC,CAAC;IAC9F9F,CAAC,CAAC4G,cAAc,GAAG7C,KAAK,CACrB,CAAC/D,CAAC,CAAC6G,wBAAwB,GAAG1J,OAAO,CAAC2J,UAAU,KAAK9G,CAAC,CAACuG,UAAU,GAAGvG,CAAC,CAAC0G,eAAe,CAAC,IACpF1G,CAAC,CAAC8F,YAAY,GAAG9F,CAAC,CAAC4F,cAAc,CACtC,CAAC;EACH,CAAC,MAAM;IACL5F,CAAC,CAACsG,gBAAgB,GAAG,KAAK;EAC5B;EAEA,IACE,CAACtG,CAAC,CAACQ,QAAQ,CAACuG,eAAe,IAC3B/G,CAAC,CAACyD,eAAe,GAAGzD,CAAC,CAACQ,QAAQ,CAACwG,mBAAmB,GAAGhH,CAAC,CAACwD,aAAa,EACpE;IACAxD,CAAC,CAACiH,gBAAgB,GAAG,IAAI;IACzBjH,CAAC,CAACkH,WAAW,GAAGlH,CAAC,CAACyD,eAAe,GAAGzD,CAAC,CAACmH,iBAAiB;IACvDnH,CAAC,CAACoH,UAAU,GAAGpH,CAAC,CAACyD,eAAe,GAAGzD,CAAC,CAACkH,WAAW;IAChDlH,CAAC,CAACqH,gBAAgB,GAAGV,YAAY,CAC/B3G,CAAC,EACD+D,KAAK,CAAE/D,CAAC,CAACkH,WAAW,GAAGlH,CAAC,CAACyD,eAAe,GAAIzD,CAAC,CAACwD,aAAa,CAC7D,CAAC;IACDxD,CAAC,CAACsH,aAAa,GAAGvD,KAAK,CACpBwB,gBAAgB,IAAIvF,CAAC,CAACkH,WAAW,GAAGlH,CAAC,CAACqH,gBAAgB,CAAC,IACrDrH,CAAC,CAACwD,aAAa,GAAGxD,CAAC,CAACyD,eAAe,CACxC,CAAC;EACH,CAAC,MAAM;IACLzD,CAAC,CAACiH,gBAAgB,GAAG,KAAK;EAC5B;EAEA,IAAIjH,CAAC,CAAC4G,cAAc,IAAI5G,CAAC,CAACuG,UAAU,GAAGvG,CAAC,CAAC0G,eAAe,EAAE;IACxD1G,CAAC,CAAC4G,cAAc,GAAG5G,CAAC,CAACuG,UAAU,GAAGvG,CAAC,CAAC0G,eAAe;EACrD;EACA,IAAI1G,CAAC,CAACsH,aAAa,IAAItH,CAAC,CAACkH,WAAW,GAAGlH,CAAC,CAACqH,gBAAgB,EAAE;IACzDrH,CAAC,CAACsH,aAAa,GAAGtH,CAAC,CAACkH,WAAW,GAAGlH,CAAC,CAACqH,gBAAgB;EACtD;EAEAE,SAAS,CAACpK,OAAO,EAAE6C,CAAC,CAAC;EAErB,IAAIA,CAAC,CAACsG,gBAAgB,EAAE;IACtBnJ,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACO,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;EAC9C,CAAC,MAAM;IACLxC,OAAO,CAAC8C,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/CK,CAAC,CAAC0G,eAAe,GAAG,CAAC;IACrB1G,CAAC,CAAC4G,cAAc,GAAG,CAAC;IACpBzJ,OAAO,CAAC2J,UAAU,GAAG9G,CAAC,CAACwH,KAAK,KAAK,IAAI,GAAGxH,CAAC,CAAC8F,YAAY,GAAG,CAAC;EAC5D;EACA,IAAI9F,CAAC,CAACiH,gBAAgB,EAAE;IACtB9J,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACO,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;EAC9C,CAAC,MAAM;IACLxC,OAAO,CAAC8C,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACG,MAAM,CAAC,GAAG,CAAC,CAAC;IAC/CK,CAAC,CAACqH,gBAAgB,GAAG,CAAC;IACtBrH,CAAC,CAACsH,aAAa,GAAG,CAAC;IACnBnK,OAAO,CAACuG,SAAS,GAAG,CAAC;EACvB;AACF;AAEA,SAASiD,YAAYA,CAAC3G,CAAC,EAAEyH,SAAS,EAAE;EAClC,IAAIzH,CAAC,CAACQ,QAAQ,CAACkH,kBAAkB,EAAE;IACjCD,SAAS,GAAGjC,IAAI,CAACmC,GAAG,CAACF,SAAS,EAAEzH,CAAC,CAACQ,QAAQ,CAACkH,kBAAkB,CAAC;EAChE;EACA,IAAI1H,CAAC,CAACQ,QAAQ,CAACoH,kBAAkB,EAAE;IACjCH,SAAS,GAAGjC,IAAI,CAACqC,GAAG,CAACJ,SAAS,EAAEzH,CAAC,CAACQ,QAAQ,CAACoH,kBAAkB,CAAC;EAChE;EACA,OAAOH,SAAS;AAClB;AAEA,SAASF,SAASA,CAACpK,OAAO,EAAE6C,CAAC,EAAE;EAC7B,IAAI8H,WAAW,GAAG;IAAEzD,KAAK,EAAErE,CAAC,CAACuG;EAAW,CAAC;EACzC,IAAIhB,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;EAEpD,IAAI1D,CAAC,CAACwH,KAAK,EAAE;IACXM,WAAW,CAACC,IAAI,GACd/H,CAAC,CAAC6G,wBAAwB,GAAG1J,OAAO,CAAC2J,UAAU,GAAG9G,CAAC,CAAC4F,cAAc,GAAG5F,CAAC,CAAC8F,YAAY;EACvF,CAAC,MAAM;IACLgC,WAAW,CAACC,IAAI,GAAG5K,OAAO,CAAC2J,UAAU;EACvC;EACA,IAAI9G,CAAC,CAACgI,uBAAuB,EAAE;IAC7BF,WAAW,CAACG,MAAM,GAAGjI,CAAC,CAACkI,gBAAgB,GAAG3C,gBAAgB;EAC5D,CAAC,MAAM;IACLuC,WAAW,CAACK,GAAG,GAAGnI,CAAC,CAACoI,aAAa,GAAG7C,gBAAgB;EACtD;EACAlI,GAAG,CAAC2C,CAAC,CAACiG,cAAc,EAAE6B,WAAW,CAAC;EAElC,IAAIO,WAAW,GAAG;IAAEF,GAAG,EAAE5C,gBAAgB;IAAEM,MAAM,EAAE7F,CAAC,CAACkH;EAAY,CAAC;EAClE,IAAIlH,CAAC,CAACsI,sBAAsB,EAAE;IAC5B,IAAItI,CAAC,CAACwH,KAAK,EAAE;MACXa,WAAW,CAACE,KAAK,GACfvI,CAAC,CAAC8F,YAAY,IACb9F,CAAC,CAAC6G,wBAAwB,GAAG1J,OAAO,CAAC2J,UAAU,CAAC,GACjD9G,CAAC,CAACwI,eAAe,GACjBxI,CAAC,CAACyI,oBAAoB,GACtB,CAAC;IACL,CAAC,MAAM;MACLJ,WAAW,CAACE,KAAK,GAAGvI,CAAC,CAACwI,eAAe,GAAGrL,OAAO,CAAC2J,UAAU;IAC5D;EACF,CAAC,MAAM;IACL,IAAI9G,CAAC,CAACwH,KAAK,EAAE;MACXa,WAAW,CAACN,IAAI,GACd/H,CAAC,CAAC6G,wBAAwB,GAC1B1J,OAAO,CAAC2J,UAAU,GAClB9G,CAAC,CAAC4F,cAAc,GAAG,CAAC,GACpB5F,CAAC,CAAC8F,YAAY,GACd9F,CAAC,CAAC0I,cAAc,GAChB1I,CAAC,CAACyI,oBAAoB;IAC1B,CAAC,MAAM;MACLJ,WAAW,CAACN,IAAI,GAAG/H,CAAC,CAAC0I,cAAc,GAAGvL,OAAO,CAAC2J,UAAU;IAC1D;EACF;EACAzJ,GAAG,CAAC2C,CAAC,CAACmG,cAAc,EAAEkC,WAAW,CAAC;EAElChL,GAAG,CAAC2C,CAAC,CAAC2I,UAAU,EAAE;IAChBZ,IAAI,EAAE/H,CAAC,CAAC4G,cAAc;IACtBvC,KAAK,EAAErE,CAAC,CAAC0G,eAAe,GAAG1G,CAAC,CAAC4I;EAC/B,CAAC,CAAC;EACFvL,GAAG,CAAC2C,CAAC,CAAC6I,UAAU,EAAE;IAChBV,GAAG,EAAEnI,CAAC,CAACsH,aAAa;IACpBzB,MAAM,EAAE7F,CAAC,CAACqH,gBAAgB,GAAGrH,CAAC,CAAC8I;EACjC,CAAC,CAAC;AACJ;;AAEA;;AAEA,SAASC,SAASA,CAAE/I,CAAC,EAAE;EACrB;;EAEAA,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAChB,CAAC,CAAC6I,UAAU,EAAE,WAAW,EAAE,UAAUtG,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC0G,eAAe,CAAC,CAAC;EAAE,CAAC,CAAC;EACrFjJ,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAChB,CAAC,CAACmG,cAAc,EAAE,WAAW,EAAE,UAAU5D,CAAC,EAAE;IACvD,IAAI2G,WAAW,GAAG3G,CAAC,CAAC4G,KAAK,GAAGvG,MAAM,CAACwG,WAAW,GAAGpJ,CAAC,CAACmG,cAAc,CAACR,qBAAqB,CAAC,CAAC,CAACwC,GAAG;IAC7F,IAAIkB,SAAS,GAAGH,WAAW,GAAGlJ,CAAC,CAACsH,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;IAEtDtH,CAAC,CAAC7C,OAAO,CAACuG,SAAS,IAAI2F,SAAS,GAAGrJ,CAAC,CAACyD,eAAe;IACpD6B,cAAc,CAACtF,CAAC,CAAC;IAEjBuC,CAAC,CAAC0G,eAAe,CAAC,CAAC;EACrB,CAAC,CAAC;EAEFjJ,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAChB,CAAC,CAAC2I,UAAU,EAAE,WAAW,EAAE,UAAUpG,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC0G,eAAe,CAAC,CAAC;EAAE,CAAC,CAAC;EACrFjJ,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAChB,CAAC,CAACiG,cAAc,EAAE,WAAW,EAAE,UAAU1D,CAAC,EAAE;IACvD,IAAI+G,YAAY,GACd/G,CAAC,CAACgH,KAAK,GAAG3G,MAAM,CAAC4G,WAAW,GAAGxJ,CAAC,CAACiG,cAAc,CAACN,qBAAqB,CAAC,CAAC,CAACoC,IAAI;IAC9E,IAAIsB,SAAS,GAAGC,YAAY,GAAGtJ,CAAC,CAAC4G,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;IAExD5G,CAAC,CAAC7C,OAAO,CAAC2J,UAAU,IAAIuC,SAAS,GAAGrJ,CAAC,CAAC4F,cAAc;IACpDN,cAAc,CAACtF,CAAC,CAAC;IAEjBuC,CAAC,CAAC0G,eAAe,CAAC,CAAC;EACrB,CAAC,CAAC;AACJ;AAEA,IAAIQ,YAAY,GAAG,IAAI,CAAC,CAAC;;AAEzB,SAASC,mBAAmBA,CAAC1J,CAAC,EAAE;EAC9B2J,sBAAsB,CAAC3J,CAAC,EAAE,CACxB,iBAAiB,EACjB,eAAe,EACf,OAAO,EACP,aAAa,EACb,YAAY,EACZ,kBAAkB,EAClB,WAAW,EACX,GAAG,EACH,gBAAgB,CAAE,CAAC;EAErB2J,sBAAsB,CAAC3J,CAAC,EAAE,CACxB,gBAAgB,EAChB,cAAc,EACd,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,iBAAiB,EACjB,YAAY,EACZ,GAAG,EACH,gBAAgB,CAAE,CAAC;AACvB;AAEA,SAAS2J,sBAAsBA,CAC7B3J,CAAC,EACDuD,GAAG,EACH;EACA,IAAIqG,kBAAkB,GAAGrG,GAAG,CAAC,CAAC,CAAC;EAC/B,IAAIsG,gBAAgB,GAAGtG,GAAG,CAAC,CAAC,CAAC;EAC7B,IAAIuG,QAAQ,GAAGvG,GAAG,CAAC,CAAC,CAAC;EACrB,IAAIwG,aAAa,GAAGxG,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAIyG,aAAa,GAAGzG,GAAG,CAAC,CAAC,CAAC;EAC1B,IAAI0G,kBAAkB,GAAG1G,GAAG,CAAC,CAAC,CAAC;EAC/B,IAAI2G,UAAU,GAAG3G,GAAG,CAAC,CAAC,CAAC;EACvB,IAAIN,IAAI,GAAGM,GAAG,CAAC,CAAC,CAAC;EACjB,IAAI4G,aAAa,GAAG5G,GAAG,CAAC,CAAC,CAAC;EAE1B,IAAIpG,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EACvB,IAAIiN,sBAAsB,GAAG,IAAI;EACjC,IAAIC,yBAAyB,GAAG,IAAI;EACpC,IAAIC,QAAQ,GAAG,IAAI;EAEnB,SAASC,WAAWA,CAAChI,CAAC,EAAE;IACtB,IAAIA,CAAC,CAACiI,OAAO,IAAIjI,CAAC,CAACiI,OAAO,CAAC,CAAC,CAAC,EAAE;MAC7BjI,CAAC,CAACuH,QAAQ,CAAC,GAAGvH,CAAC,CAACiI,OAAO,CAAC,CAAC,CAAC,CAAE,MAAM,GAAIvH,IAAI,CAACwH,WAAW,CAAC,CAAE,CAAE;IAC7D;;IAEA;IACA,IAAIhB,YAAY,KAAKO,aAAa,EAAE;MAClC7M,OAAO,CAAC+M,UAAU,CAAC,GACjBE,sBAAsB,GAAGE,QAAQ,IAAI/H,CAAC,CAACuH,QAAQ,CAAC,GAAGO,yBAAyB,CAAC;MAC/EtK,iBAAiB,CAACC,CAAC,EAAEiD,IAAI,CAAC;MAC1BqC,cAAc,CAACtF,CAAC,CAAC;MAEjBuC,CAAC,CAAC0G,eAAe,CAAC,CAAC;MACnB1G,CAAC,CAACmI,cAAc,CAAC,CAAC;IACpB;EACF;EAEA,SAASC,UAAUA,CAAA,EAAG;IACpBtK,oBAAoB,CAACL,CAAC,EAAEiD,IAAI,CAAC;IAC7BjD,CAAC,CAACmK,aAAa,CAAC,CAAClK,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACE,QAAQ,CAAC;IACrD9B,QAAQ,CAAC4D,mBAAmB,CAAC,WAAW,EAAE+I,WAAW,CAAC;IACtD3M,QAAQ,CAAC4D,mBAAmB,CAAC,SAAS,EAAEmJ,UAAU,CAAC;IACnD/M,QAAQ,CAAC4D,mBAAmB,CAAC,WAAW,EAAE+I,WAAW,CAAC;IACtD3M,QAAQ,CAAC4D,mBAAmB,CAAC,UAAU,EAAEmJ,UAAU,CAAC;IACpDlB,YAAY,GAAG,IAAI,CAAC,CAAC;EACvB;EAEA,SAASmB,SAASA,CAACrI,CAAC,EAAE;IACpB,IAAIkH,YAAY,KAAK,IAAI,EAAE;MACzB;MACAA,YAAY,GAAGO,aAAa,CAAC,CAAC;;MAE9BI,sBAAsB,GAAGjN,OAAO,CAAC+M,UAAU,CAAC;MAC5C,IAAI3H,CAAC,CAACiI,OAAO,EAAE;QACbjI,CAAC,CAACuH,QAAQ,CAAC,GAAGvH,CAAC,CAACiI,OAAO,CAAC,CAAC,CAAC,CAAE,MAAM,GAAIvH,IAAI,CAACwH,WAAW,CAAC,CAAE,CAAE;MAC7D;MACAJ,yBAAyB,GAAG9H,CAAC,CAACuH,QAAQ,CAAC;MACvCQ,QAAQ,GACN,CAACtK,CAAC,CAAC6J,gBAAgB,CAAC,GAAG7J,CAAC,CAAC4J,kBAAkB,CAAC,KAAK5J,CAAC,CAAC+J,aAAa,CAAC,GAAG/J,CAAC,CAACiK,kBAAkB,CAAC,CAAC;MAE5F,IAAI,CAAC1H,CAAC,CAACiI,OAAO,EAAE;QACd5M,QAAQ,CAACwD,gBAAgB,CAAC,WAAW,EAAEmJ,WAAW,CAAC;QACnD3M,QAAQ,CAACwD,gBAAgB,CAAC,SAAS,EAAEuJ,UAAU,CAAC;MAClD,CAAC,MAAM;QACL/M,QAAQ,CAACwD,gBAAgB,CAAC,WAAW,EAAEmJ,WAAW,EAAE;UAAEM,OAAO,EAAE;QAAM,CAAC,CAAC;QACvEjN,QAAQ,CAACwD,gBAAgB,CAAC,UAAU,EAAEuJ,UAAU,CAAC;MACnD;MAEA3K,CAAC,CAACmK,aAAa,CAAC,CAAClK,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACO,KAAK,CAACE,QAAQ,CAAC;IACpD;IAEA6C,CAAC,CAAC0G,eAAe,CAAC,CAAC;IACnB,IAAI1G,CAAC,CAACuI,UAAU,EAAE;MAChBvI,CAAC,CAACmI,cAAc,CAAC,CAAC;IACpB;EACF;EAEA1K,CAAC,CAACgK,aAAa,CAAC,CAAC5I,gBAAgB,CAAC,WAAW,EAAEwJ,SAAS,CAAC;EACzD5K,CAAC,CAACgK,aAAa,CAAC,CAAC5I,gBAAgB,CAAC,YAAY,EAAEwJ,SAAS,CAAC;AAC5D;;AAEA;;AAEA,SAASG,QAAQA,CAAE/K,CAAC,EAAE;EACpB,IAAI7C,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EAEvB,IAAI6N,cAAc,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAO/M,OAAO,CAACd,OAAO,EAAE,QAAQ,CAAC;EAAE,CAAC;EACvE,IAAI8N,gBAAgB,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOhN,OAAO,CAAC+B,CAAC,CAAC2I,UAAU,EAAE,QAAQ,CAAC,IAAI1K,OAAO,CAAC+B,CAAC,CAAC6I,UAAU,EAAE,QAAQ,CAAC;EAAE,CAAC;EAEjH,SAASqC,oBAAoBA,CAACC,MAAM,EAAEC,MAAM,EAAE;IAC5C,IAAI1H,SAAS,GAAG8B,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;IAC7C,IAAIyH,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,CAACnL,CAAC,CAACiH,gBAAgB,EAAE;QACvB,OAAO,KAAK;MACd;MACA,IACGvD,SAAS,KAAK,CAAC,IAAI0H,MAAM,GAAG,CAAC,IAC7B1H,SAAS,IAAI1D,CAAC,CAACwD,aAAa,GAAGxD,CAAC,CAACyD,eAAe,IAAI2H,MAAM,GAAG,CAAE,EAChE;QACA,OAAO,CAACpL,CAAC,CAACQ,QAAQ,CAAC6K,gBAAgB;MACrC;IACF;IAEA,IAAIvE,UAAU,GAAG3J,OAAO,CAAC2J,UAAU;IACnC,IAAIsE,MAAM,KAAK,CAAC,EAAE;MAChB,IAAI,CAACpL,CAAC,CAACsG,gBAAgB,EAAE;QACvB,OAAO,KAAK;MACd;MACA,IACGQ,UAAU,KAAK,CAAC,IAAIqE,MAAM,GAAG,CAAC,IAC9BrE,UAAU,IAAI9G,CAAC,CAAC8F,YAAY,GAAG9F,CAAC,CAAC4F,cAAc,IAAIuF,MAAM,GAAG,CAAE,EAC/D;QACA,OAAO,CAACnL,CAAC,CAACQ,QAAQ,CAAC6K,gBAAgB;MACrC;IACF;IACA,OAAO,IAAI;EACb;EAEArL,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAChB,CAAC,CAACsL,aAAa,EAAE,SAAS,EAAE,UAAU/I,CAAC,EAAE;IACpD,IAAKA,CAAC,CAACgJ,kBAAkB,IAAIhJ,CAAC,CAACgJ,kBAAkB,CAAC,CAAC,IAAKhJ,CAAC,CAACiJ,gBAAgB,EAAE;MAC1E;IACF;IAEA,IAAI,CAACR,cAAc,CAAC,CAAC,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAAE;MAC5C;IACF;IAEA,IAAIQ,aAAa,GAAG7N,QAAQ,CAAC6N,aAAa,GACtC7N,QAAQ,CAAC6N,aAAa,GACtBzL,CAAC,CAACsL,aAAa,CAACG,aAAa;IACjC,IAAIA,aAAa,EAAE;MACjB,IAAIA,aAAa,CAACC,OAAO,KAAK,QAAQ,EAAE;QACtCD,aAAa,GAAGA,aAAa,CAACE,eAAe,CAACF,aAAa;MAC7D,CAAC,MAAM;QACL;QACA,OAAOA,aAAa,CAACG,UAAU,EAAE;UAC/BH,aAAa,GAAGA,aAAa,CAACG,UAAU,CAACH,aAAa;QACxD;MACF;MACA,IAAIxH,UAAU,CAACwH,aAAa,CAAC,EAAE;QAC7B;MACF;IACF;IAEA,IAAIN,MAAM,GAAG,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC;IAEd,QAAQ7I,CAAC,CAACsJ,KAAK;MACb,KAAK,EAAE;QAAE;QACP,IAAItJ,CAAC,CAACuJ,OAAO,EAAE;UACbX,MAAM,GAAG,CAACnL,CAAC,CAAC8F,YAAY;QAC1B,CAAC,MAAM,IAAIvD,CAAC,CAACwJ,MAAM,EAAE;UACnBZ,MAAM,GAAG,CAACnL,CAAC,CAAC4F,cAAc;QAC5B,CAAC,MAAM;UACLuF,MAAM,GAAG,CAAC,EAAE;QACd;QACA;MACF,KAAK,EAAE;QAAE;QACP,IAAI5I,CAAC,CAACuJ,OAAO,EAAE;UACbV,MAAM,GAAGpL,CAAC,CAACwD,aAAa;QAC1B,CAAC,MAAM,IAAIjB,CAAC,CAACwJ,MAAM,EAAE;UACnBX,MAAM,GAAGpL,CAAC,CAACyD,eAAe;QAC5B,CAAC,MAAM;UACL2H,MAAM,GAAG,EAAE;QACb;QACA;MACF,KAAK,EAAE;QAAE;QACP,IAAI7I,CAAC,CAACuJ,OAAO,EAAE;UACbX,MAAM,GAAGnL,CAAC,CAAC8F,YAAY;QACzB,CAAC,MAAM,IAAIvD,CAAC,CAACwJ,MAAM,EAAE;UACnBZ,MAAM,GAAGnL,CAAC,CAAC4F,cAAc;QAC3B,CAAC,MAAM;UACLuF,MAAM,GAAG,EAAE;QACb;QACA;MACF,KAAK,EAAE;QAAE;QACP,IAAI5I,CAAC,CAACuJ,OAAO,EAAE;UACbV,MAAM,GAAG,CAACpL,CAAC,CAACwD,aAAa;QAC3B,CAAC,MAAM,IAAIjB,CAAC,CAACwJ,MAAM,EAAE;UACnBX,MAAM,GAAG,CAACpL,CAAC,CAACyD,eAAe;QAC7B,CAAC,MAAM;UACL2H,MAAM,GAAG,CAAC,EAAE;QACd;QACA;MACF,KAAK,EAAE;QAAE;QACP,IAAI7I,CAAC,CAACyJ,QAAQ,EAAE;UACdZ,MAAM,GAAGpL,CAAC,CAACyD,eAAe;QAC5B,CAAC,MAAM;UACL2H,MAAM,GAAG,CAACpL,CAAC,CAACyD,eAAe;QAC7B;QACA;MACF,KAAK,EAAE;QAAE;QACP2H,MAAM,GAAGpL,CAAC,CAACyD,eAAe;QAC1B;MACF,KAAK,EAAE;QAAE;QACP2H,MAAM,GAAG,CAACpL,CAAC,CAACyD,eAAe;QAC3B;MACF,KAAK,EAAE;QAAE;QACP2H,MAAM,GAAGpL,CAAC,CAACwD,aAAa;QACxB;MACF,KAAK,EAAE;QAAE;QACP4H,MAAM,GAAG,CAACpL,CAAC,CAACwD,aAAa;QACzB;MACF;QACE;IACJ;IAEA,IAAIxD,CAAC,CAACQ,QAAQ,CAAC4F,eAAe,IAAI+E,MAAM,KAAK,CAAC,EAAE;MAC9C;IACF;IACA,IAAInL,CAAC,CAACQ,QAAQ,CAACuG,eAAe,IAAIqE,MAAM,KAAK,CAAC,EAAE;MAC9C;IACF;IAEAjO,OAAO,CAACuG,SAAS,IAAI0H,MAAM;IAC3BjO,OAAO,CAAC2J,UAAU,IAAIqE,MAAM;IAC5B7F,cAAc,CAACtF,CAAC,CAAC;IAEjB,IAAIkL,oBAAoB,CAACC,MAAM,EAAEC,MAAM,CAAC,EAAE;MACxC7I,CAAC,CAACmI,cAAc,CAAC,CAAC;IACpB;EACF,CAAC,CAAC;AACJ;;AAEA;;AAEA,SAASuB,KAAKA,CAAEjM,CAAC,EAAE;EACjB,IAAI7C,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EAEvB,SAAS+N,oBAAoBA,CAACC,MAAM,EAAEC,MAAM,EAAE;IAC5C,IAAI7F,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;IACpD,IAAIwI,KAAK,GAAG/O,OAAO,CAACuG,SAAS,KAAK,CAAC;IACnC,IAAIyI,QAAQ,GAAG5G,gBAAgB,GAAGpI,OAAO,CAACiP,YAAY,KAAKjP,OAAO,CAAC6I,YAAY;IAC/E,IAAIqG,MAAM,GAAGlP,OAAO,CAAC2J,UAAU,KAAK,CAAC;IACrC,IAAIwF,OAAO,GAAGnP,OAAO,CAAC2J,UAAU,GAAG3J,OAAO,CAACoP,WAAW,KAAKpP,OAAO,CAAC4I,WAAW;IAE9E,IAAIyG,SAAS;;IAEb;IACA,IAAIhH,IAAI,CAACiH,GAAG,CAACrB,MAAM,CAAC,GAAG5F,IAAI,CAACiH,GAAG,CAACtB,MAAM,CAAC,EAAE;MACvCqB,SAAS,GAAGN,KAAK,IAAIC,QAAQ;IAC/B,CAAC,MAAM;MACLK,SAAS,GAAGH,MAAM,IAAIC,OAAO;IAC/B;IAEA,OAAOE,SAAS,GAAG,CAACxM,CAAC,CAACQ,QAAQ,CAAC6K,gBAAgB,GAAG,IAAI;EACxD;EAEA,SAASqB,iBAAiBA,CAACnK,CAAC,EAAE;IAC5B,IAAI4I,MAAM,GAAG5I,CAAC,CAAC4I,MAAM;IACrB,IAAIC,MAAM,GAAG,CAAC,CAAC,GAAG7I,CAAC,CAAC6I,MAAM;IAE1B,IAAI,OAAOD,MAAM,KAAK,WAAW,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MAClE;MACAD,MAAM,GAAI,CAAC,CAAC,GAAG5I,CAAC,CAACoK,WAAW,GAAI,CAAC;MACjCvB,MAAM,GAAG7I,CAAC,CAACqK,WAAW,GAAG,CAAC;IAC5B;IAEA,IAAIrK,CAAC,CAACsK,SAAS,IAAItK,CAAC,CAACsK,SAAS,KAAK,CAAC,EAAE;MACpC;MACA1B,MAAM,IAAI,EAAE;MACZC,MAAM,IAAI,EAAE;IACd;IAEA,IAAID,MAAM,KAAKA,MAAM,IAAIC,MAAM,KAAKA,MAAM,CAAC,kBAAkB;MAC3D;MACAD,MAAM,GAAG,CAAC;MACVC,MAAM,GAAG7I,CAAC,CAACuK,UAAU;IACvB;IAEA,IAAIvK,CAAC,CAACyJ,QAAQ,EAAE;MACd;MACA,OAAO,CAAC,CAACZ,MAAM,EAAE,CAACD,MAAM,CAAC;IAC3B;IACA,OAAO,CAACA,MAAM,EAAEC,MAAM,CAAC;EACzB;EAEA,SAAS2B,uBAAuBA,CAACzL,MAAM,EAAE6J,MAAM,EAAEC,MAAM,EAAE;IACvD;IACA,IAAI,CAAC1G,GAAG,CAACC,QAAQ,IAAIxH,OAAO,CAAC6P,aAAa,CAAC,cAAc,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IAEA,IAAI,CAAC7P,OAAO,CAAC+C,QAAQ,CAACoB,MAAM,CAAC,EAAE;MAC7B,OAAO,KAAK;IACd;IAEA,IAAI2L,MAAM,GAAG3L,MAAM;IAEnB,OAAO2L,MAAM,IAAIA,MAAM,KAAK9P,OAAO,EAAE;MACnC,IAAI8P,MAAM,CAAChN,SAAS,CAACC,QAAQ,CAACjB,GAAG,CAAC9B,OAAO,CAACoC,SAAS,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MAEA,IAAI9B,KAAK,GAAGP,GAAG,CAAC+P,MAAM,CAAC;;MAEvB;MACA,IAAI7B,MAAM,IAAI3N,KAAK,CAACyP,SAAS,CAACC,KAAK,CAAC,eAAe,CAAC,EAAE;QACpD,IAAIC,YAAY,GAAGH,MAAM,CAACjH,YAAY,GAAGiH,MAAM,CAACI,YAAY;QAC5D,IAAID,YAAY,GAAG,CAAC,EAAE;UACpB,IACGH,MAAM,CAACvJ,SAAS,GAAG,CAAC,IAAI0H,MAAM,GAAG,CAAC,IAClC6B,MAAM,CAACvJ,SAAS,GAAG0J,YAAY,IAAIhC,MAAM,GAAG,CAAE,EAC/C;YACA,OAAO,IAAI;UACb;QACF;MACF;MACA;MACA,IAAID,MAAM,IAAI1N,KAAK,CAAC6P,SAAS,CAACH,KAAK,CAAC,eAAe,CAAC,EAAE;QACpD,IAAII,aAAa,GAAGN,MAAM,CAAClH,WAAW,GAAGkH,MAAM,CAACO,WAAW;QAC3D,IAAID,aAAa,GAAG,CAAC,EAAE;UACrB,IACGN,MAAM,CAACnG,UAAU,GAAG,CAAC,IAAIqE,MAAM,GAAG,CAAC,IACnC8B,MAAM,CAACnG,UAAU,GAAGyG,aAAa,IAAIpC,MAAM,GAAG,CAAE,EACjD;YACA,OAAO,IAAI;UACb;QACF;MACF;MAEA8B,MAAM,GAAGA,MAAM,CAACxO,UAAU;IAC5B;IAEA,OAAO,KAAK;EACd;EAEA,SAASgP,iBAAiBA,CAAClL,CAAC,EAAE;IAC5B,IAAIgB,GAAG,GAAGmJ,iBAAiB,CAACnK,CAAC,CAAC;IAC9B,IAAI4I,MAAM,GAAG5H,GAAG,CAAC,CAAC,CAAC;IACnB,IAAI6H,MAAM,GAAG7H,GAAG,CAAC,CAAC,CAAC;IAEnB,IAAIwJ,uBAAuB,CAACxK,CAAC,CAACjB,MAAM,EAAE6J,MAAM,EAAEC,MAAM,CAAC,EAAE;MACrD;IACF;IAEA,IAAIsC,aAAa,GAAG,KAAK;IACzB,IAAI,CAAC1N,CAAC,CAACQ,QAAQ,CAACmN,gBAAgB,EAAE;MAChC;MACA;MACAxQ,OAAO,CAACuG,SAAS,IAAI0H,MAAM,GAAGpL,CAAC,CAACQ,QAAQ,CAACoN,UAAU;MACnDzQ,OAAO,CAAC2J,UAAU,IAAIqE,MAAM,GAAGnL,CAAC,CAACQ,QAAQ,CAACoN,UAAU;IACtD,CAAC,MAAM,IAAI5N,CAAC,CAACiH,gBAAgB,IAAI,CAACjH,CAAC,CAACsG,gBAAgB,EAAE;MACpD;MACA;MACA,IAAI8E,MAAM,EAAE;QACVjO,OAAO,CAACuG,SAAS,IAAI0H,MAAM,GAAGpL,CAAC,CAACQ,QAAQ,CAACoN,UAAU;MACrD,CAAC,MAAM;QACLzQ,OAAO,CAACuG,SAAS,IAAIyH,MAAM,GAAGnL,CAAC,CAACQ,QAAQ,CAACoN,UAAU;MACrD;MACAF,aAAa,GAAG,IAAI;IACtB,CAAC,MAAM,IAAI1N,CAAC,CAACsG,gBAAgB,IAAI,CAACtG,CAAC,CAACiH,gBAAgB,EAAE;MACpD;MACA;MACA,IAAIkE,MAAM,EAAE;QACVhO,OAAO,CAAC2J,UAAU,IAAIqE,MAAM,GAAGnL,CAAC,CAACQ,QAAQ,CAACoN,UAAU;MACtD,CAAC,MAAM;QACLzQ,OAAO,CAAC2J,UAAU,IAAIsE,MAAM,GAAGpL,CAAC,CAACQ,QAAQ,CAACoN,UAAU;MACtD;MACAF,aAAa,GAAG,IAAI;IACtB;IAEApI,cAAc,CAACtF,CAAC,CAAC;IAEjB0N,aAAa,GAAGA,aAAa,IAAIxC,oBAAoB,CAACC,MAAM,EAAEC,MAAM,CAAC;IACrE,IAAIsC,aAAa,IAAI,CAACnL,CAAC,CAACsL,OAAO,EAAE;MAC/BtL,CAAC,CAAC0G,eAAe,CAAC,CAAC;MACnB1G,CAAC,CAACmI,cAAc,CAAC,CAAC;IACpB;EACF;EAEA,IAAI,OAAO9H,MAAM,CAACkL,OAAO,KAAK,WAAW,EAAE;IACzC9N,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,OAAO,EAAEsQ,iBAAiB,CAAC;EACnD,CAAC,MAAM,IAAI,OAAO7K,MAAM,CAACmL,YAAY,KAAK,WAAW,EAAE;IACrD/N,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,YAAY,EAAEsQ,iBAAiB,CAAC;EACxD;AACF;AAEA,SAASO,KAAKA,CAAEhO,CAAC,EAAE;EACjB,IAAI,CAAC0E,GAAG,CAACG,aAAa,IAAI,CAACH,GAAG,CAACO,iBAAiB,EAAE;IAChD;EACF;EAEA,IAAI9H,OAAO,GAAG6C,CAAC,CAAC7C,OAAO;EAEvB,IAAIqC,KAAK,GAAG;IACVyO,WAAW,EAAE,CAAC,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,KAAK,EAAE,CAAC,CAAC;IACTC,UAAU,EAAE;EACd,CAAC;EAED,SAASV,aAAaA,CAACvC,MAAM,EAAEC,MAAM,EAAE;IACrC,IAAI1H,SAAS,GAAG8B,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC;IAC7C,IAAIoD,UAAU,GAAG3J,OAAO,CAAC2J,UAAU;IACnC,IAAIuH,UAAU,GAAG7I,IAAI,CAACiH,GAAG,CAACtB,MAAM,CAAC;IACjC,IAAImD,UAAU,GAAG9I,IAAI,CAACiH,GAAG,CAACrB,MAAM,CAAC;IAEjC,IAAIkD,UAAU,GAAGD,UAAU,EAAE;MAC3B;;MAEA,IACGjD,MAAM,GAAG,CAAC,IAAI1H,SAAS,KAAK1D,CAAC,CAACwD,aAAa,GAAGxD,CAAC,CAACyD,eAAe,IAC/D2H,MAAM,GAAG,CAAC,IAAI1H,SAAS,KAAK,CAAE,EAC/B;QACA;QACA,OAAOd,MAAM,CAAC2L,OAAO,KAAK,CAAC,IAAInD,MAAM,GAAG,CAAC,IAAI1G,GAAG,CAACS,QAAQ;MAC3D;IACF,CAAC,MAAM,IAAIkJ,UAAU,GAAGC,UAAU,EAAE;MAClC;;MAEA,IACGnD,MAAM,GAAG,CAAC,IAAIrE,UAAU,KAAK9G,CAAC,CAAC8F,YAAY,GAAG9F,CAAC,CAAC4F,cAAc,IAC9DuF,MAAM,GAAG,CAAC,IAAIrE,UAAU,KAAK,CAAE,EAChC;QACA,OAAO,IAAI;MACb;IACF;IAEA,OAAO,IAAI;EACb;EAEA,SAAS0H,cAAcA,CAACC,WAAW,EAAEC,WAAW,EAAE;IAChDvR,OAAO,CAACuG,SAAS,IAAIgL,WAAW;IAChCvR,OAAO,CAAC2J,UAAU,IAAI2H,WAAW;IAEjCnJ,cAAc,CAACtF,CAAC,CAAC;EACnB;EAEA,SAAS2O,QAAQA,CAACpM,CAAC,EAAE;IACnB,IAAIA,CAAC,CAACqM,aAAa,EAAE;MACnB,OAAOrM,CAAC,CAACqM,aAAa,CAAC,CAAC,CAAC;IAC3B;IACA;IACA,OAAOrM,CAAC;EACV;EAEA,SAASsM,YAAYA,CAACtM,CAAC,EAAE;IACvB,IAAIA,CAAC,CAACjB,MAAM,KAAKtB,CAAC,CAAC2I,UAAU,IAAIpG,CAAC,CAACjB,MAAM,KAAKtB,CAAC,CAAC6I,UAAU,EAAE;MAC1D,OAAO,KAAK;IACd;IACA,IAAItG,CAAC,CAACuM,WAAW,IAAIvM,CAAC,CAACuM,WAAW,KAAK,KAAK,IAAIvM,CAAC,CAACwM,OAAO,KAAK,CAAC,EAAE;MAC/D,OAAO,KAAK;IACd;IACA,IAAIxM,CAAC,CAACqM,aAAa,IAAIrM,CAAC,CAACqM,aAAa,CAAC9M,MAAM,KAAK,CAAC,EAAE;MACnD,OAAO,IAAI;IACb;IACA,IAAIS,CAAC,CAACuM,WAAW,IAAIvM,CAAC,CAACuM,WAAW,KAAK,OAAO,IAAIvM,CAAC,CAACuM,WAAW,KAAKvM,CAAC,CAACyM,oBAAoB,EAAE;MAC1F,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEA,SAASC,UAAUA,CAAC1M,CAAC,EAAE;IACrB,IAAI,CAACsM,YAAY,CAACtM,CAAC,CAAC,EAAE;MACpB;IACF;IAEA,IAAIyL,KAAK,GAAGW,QAAQ,CAACpM,CAAC,CAAC;IAEvB/C,KAAK,CAACyO,WAAW,CAAC1E,KAAK,GAAGyE,KAAK,CAACzE,KAAK;IACrC/J,KAAK,CAACyO,WAAW,CAAC9E,KAAK,GAAG6E,KAAK,CAAC7E,KAAK;IAErC3J,KAAK,CAAC0O,SAAS,GAAG,IAAIgB,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAEtC,IAAI3P,KAAK,CAAC4O,UAAU,KAAK,IAAI,EAAE;MAC7BgB,aAAa,CAAC5P,KAAK,CAAC4O,UAAU,CAAC;IACjC;EACF;EAEA,SAASrB,uBAAuBA,CAACzL,MAAM,EAAE6J,MAAM,EAAEC,MAAM,EAAE;IACvD,IAAI,CAACjO,OAAO,CAAC+C,QAAQ,CAACoB,MAAM,CAAC,EAAE;MAC7B,OAAO,KAAK;IACd;IAEA,IAAI2L,MAAM,GAAG3L,MAAM;IAEnB,OAAO2L,MAAM,IAAIA,MAAM,KAAK9P,OAAO,EAAE;MACnC,IAAI8P,MAAM,CAAChN,SAAS,CAACC,QAAQ,CAACjB,GAAG,CAAC9B,OAAO,CAACoC,SAAS,CAAC,EAAE;QACpD,OAAO,IAAI;MACb;MAEA,IAAI9B,KAAK,GAAGP,GAAG,CAAC+P,MAAM,CAAC;;MAEvB;MACA,IAAI7B,MAAM,IAAI3N,KAAK,CAACyP,SAAS,CAACC,KAAK,CAAC,eAAe,CAAC,EAAE;QACpD,IAAIC,YAAY,GAAGH,MAAM,CAACjH,YAAY,GAAGiH,MAAM,CAACI,YAAY;QAC5D,IAAID,YAAY,GAAG,CAAC,EAAE;UACpB,IACGH,MAAM,CAACvJ,SAAS,GAAG,CAAC,IAAI0H,MAAM,GAAG,CAAC,IAClC6B,MAAM,CAACvJ,SAAS,GAAG0J,YAAY,IAAIhC,MAAM,GAAG,CAAE,EAC/C;YACA,OAAO,IAAI;UACb;QACF;MACF;MACA;MACA,IAAID,MAAM,IAAI1N,KAAK,CAAC6P,SAAS,CAACH,KAAK,CAAC,eAAe,CAAC,EAAE;QACpD,IAAII,aAAa,GAAGN,MAAM,CAAClH,WAAW,GAAGkH,MAAM,CAACO,WAAW;QAC3D,IAAID,aAAa,GAAG,CAAC,EAAE;UACrB,IACGN,MAAM,CAACnG,UAAU,GAAG,CAAC,IAAIqE,MAAM,GAAG,CAAC,IACnC8B,MAAM,CAACnG,UAAU,GAAGyG,aAAa,IAAIpC,MAAM,GAAG,CAAE,EACjD;YACA,OAAO,IAAI;UACb;QACF;MACF;MAEA8B,MAAM,GAAGA,MAAM,CAACxO,UAAU;IAC5B;IAEA,OAAO,KAAK;EACd;EAEA,SAAS4Q,SAASA,CAAC9M,CAAC,EAAE;IACpB,IAAIsM,YAAY,CAACtM,CAAC,CAAC,EAAE;MACnB,IAAIyL,KAAK,GAAGW,QAAQ,CAACpM,CAAC,CAAC;MAEvB,IAAI+M,aAAa,GAAG;QAAE/F,KAAK,EAAEyE,KAAK,CAACzE,KAAK;QAAEJ,KAAK,EAAE6E,KAAK,CAAC7E;MAAM,CAAC;MAE9D,IAAIsF,WAAW,GAAGa,aAAa,CAAC/F,KAAK,GAAG/J,KAAK,CAACyO,WAAW,CAAC1E,KAAK;MAC/D,IAAImF,WAAW,GAAGY,aAAa,CAACnG,KAAK,GAAG3J,KAAK,CAACyO,WAAW,CAAC9E,KAAK;MAE/D,IAAI4D,uBAAuB,CAACxK,CAAC,CAACjB,MAAM,EAAEmN,WAAW,EAAEC,WAAW,CAAC,EAAE;QAC/D;MACF;MAEAF,cAAc,CAACC,WAAW,EAAEC,WAAW,CAAC;MACxClP,KAAK,CAACyO,WAAW,GAAGqB,aAAa;MAEjC,IAAIC,WAAW,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAEtC,IAAIK,OAAO,GAAGD,WAAW,GAAG/P,KAAK,CAAC0O,SAAS;MAC3C,IAAIsB,OAAO,GAAG,CAAC,EAAE;QACfhQ,KAAK,CAAC2O,KAAK,CAAC9O,CAAC,GAAGoP,WAAW,GAAGe,OAAO;QACrChQ,KAAK,CAAC2O,KAAK,CAACrO,CAAC,GAAG4O,WAAW,GAAGc,OAAO;QACrChQ,KAAK,CAAC0O,SAAS,GAAGqB,WAAW;MAC/B;MAEA,IAAI7B,aAAa,CAACe,WAAW,EAAEC,WAAW,CAAC,EAAE;QAC3C;QACA,IAAInM,CAAC,CAACuI,UAAU,EAAE;UAChBvI,CAAC,CAACmI,cAAc,CAAC,CAAC;QACpB;MACF;IACF;EACF;EAEA,SAAS+E,QAAQA,CAAA,EAAG;IAClB,IAAIzP,CAAC,CAACQ,QAAQ,CAACkP,WAAW,EAAE;MAC1BN,aAAa,CAAC5P,KAAK,CAAC4O,UAAU,CAAC;MAC/B5O,KAAK,CAAC4O,UAAU,GAAGuB,WAAW,CAAC,YAAY;QACzC,IAAI3P,CAAC,CAAC4P,aAAa,EAAE;UACnBR,aAAa,CAAC5P,KAAK,CAAC4O,UAAU,CAAC;UAC/B;QACF;QAEA,IAAI,CAAC5O,KAAK,CAAC2O,KAAK,CAAC9O,CAAC,IAAI,CAACG,KAAK,CAAC2O,KAAK,CAACrO,CAAC,EAAE;UACpCsP,aAAa,CAAC5P,KAAK,CAAC4O,UAAU,CAAC;UAC/B;QACF;QAEA,IAAI5I,IAAI,CAACiH,GAAG,CAACjN,KAAK,CAAC2O,KAAK,CAAC9O,CAAC,CAAC,GAAG,IAAI,IAAImG,IAAI,CAACiH,GAAG,CAACjN,KAAK,CAAC2O,KAAK,CAACrO,CAAC,CAAC,GAAG,IAAI,EAAE;UACpEsP,aAAa,CAAC5P,KAAK,CAAC4O,UAAU,CAAC;UAC/B;QACF;QAEAI,cAAc,CAAChP,KAAK,CAAC2O,KAAK,CAAC9O,CAAC,GAAG,EAAE,EAAEG,KAAK,CAAC2O,KAAK,CAACrO,CAAC,GAAG,EAAE,CAAC;QAEtDN,KAAK,CAAC2O,KAAK,CAAC9O,CAAC,IAAI,GAAG;QACpBG,KAAK,CAAC2O,KAAK,CAACrO,CAAC,IAAI,GAAG;MACtB,CAAC,EAAE,EAAE,CAAC;IACR;EACF;EAEA,IAAI4E,GAAG,CAACG,aAAa,EAAE;IACrB7E,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,YAAY,EAAE8R,UAAU,CAAC;IAC/CjP,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,WAAW,EAAEkS,SAAS,CAAC;IAC7CrP,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,UAAU,EAAEsS,QAAQ,CAAC;EAC7C,CAAC,MAAM,IAAI/K,GAAG,CAACO,iBAAiB,EAAE;IAChC,IAAIrC,MAAM,CAACiN,YAAY,EAAE;MACvB7P,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,aAAa,EAAE8R,UAAU,CAAC;MAChDjP,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,aAAa,EAAEkS,SAAS,CAAC;MAC/CrP,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,WAAW,EAAEsS,QAAQ,CAAC;IAC9C,CAAC,MAAM,IAAI7M,MAAM,CAACkN,cAAc,EAAE;MAChC9P,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,eAAe,EAAE8R,UAAU,CAAC;MAClDjP,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,eAAe,EAAEkS,SAAS,CAAC;MACjDrP,CAAC,CAACgJ,KAAK,CAAChI,IAAI,CAAC7D,OAAO,EAAE,aAAa,EAAEsS,QAAQ,CAAC;IAChD;EACF;AACF;;AAEA;;AAEA,IAAIM,eAAe,GAAG,SAAAA,CAAA,EAAY;EAAE,OAAQ;IAC1CnP,QAAQ,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;IACpEgH,kBAAkB,EAAE,IAAI;IACxBF,kBAAkB,EAAE,IAAI;IACxBjH,kBAAkB,EAAE,IAAI;IACxB4F,mBAAmB,EAAE,CAAC;IACtBW,mBAAmB,EAAE,CAAC;IACtBZ,eAAe,EAAE,KAAK;IACtBW,eAAe,EAAE,KAAK;IACtB2I,WAAW,EAAE,IAAI;IACjB/B,gBAAgB,EAAE,KAAK;IACvBtC,gBAAgB,EAAE,IAAI;IACtBuC,UAAU,EAAE;EACd,CAAC;AAAG,CAAC;AAEL,IAAIhN,QAAQ,GAAG;EACb,YAAY,EAAEmI,SAAS;EACvB,YAAY,EAAEW,mBAAmB;EACjCqB,QAAQ,EAAEA,QAAQ;EAClBkB,KAAK,EAAEA,KAAK;EACZ+B,KAAK,EAAEA;AACT,CAAC;AAED,IAAIgC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC7S,OAAO,EAAE8S,YAAY,EAAE;EACtE,IAAI1O,MAAM,GAAG,IAAI;EACjB,IAAK0O,YAAY,KAAK,KAAK,CAAC,EAAGA,YAAY,GAAG,CAAC,CAAC;EAEhD,IAAI,OAAO9S,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAGS,QAAQ,CAACoP,aAAa,CAAC7P,OAAO,CAAC;EAC3C;EAEA,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAC+S,QAAQ,EAAE;IACjC,MAAM,IAAI5R,KAAK,CAAC,wDAAwD,CAAC;EAC3E;EAEA,IAAI,CAACnB,OAAO,GAAGA,OAAO;EAEtBA,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACC,IAAI,CAAC;EAE/B,IAAI,CAACsB,QAAQ,GAAGuP,eAAe,CAAC,CAAC;EACjC,KAAK,IAAIxS,GAAG,IAAI0S,YAAY,EAAE;IAC5B,IAAI,CAACzP,QAAQ,CAACjD,GAAG,CAAC,GAAG0S,YAAY,CAAC1S,GAAG,CAAC;EACxC;EAEA,IAAI,CAACqI,cAAc,GAAG,IAAI;EAC1B,IAAI,CAACnC,eAAe,GAAG,IAAI;EAC3B,IAAI,CAACqC,YAAY,GAAG,IAAI;EACxB,IAAI,CAACtC,aAAa,GAAG,IAAI;EAEzB,IAAI/D,KAAK,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOtC,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACO,KAAK,CAACC,KAAK,CAAC;EAAE,CAAC;EAC1E,IAAI0Q,IAAI,GAAG,SAAAA,CAAA,EAAY;IAAE,OAAOhT,OAAO,CAAC8C,SAAS,CAACzB,MAAM,CAACS,GAAG,CAACO,KAAK,CAACC,KAAK,CAAC;EAAE,CAAC;EAE5E,IAAI,CAAC+H,KAAK,GAAGtK,GAAG,CAACC,OAAO,CAAC,CAACkM,SAAS,KAAK,KAAK;EAC7C,IAAI,IAAI,CAAC7B,KAAK,KAAK,IAAI,EAAE;IACvBrK,OAAO,CAAC8C,SAAS,CAACG,GAAG,CAACnB,GAAG,CAACE,GAAG,CAAC;EAChC;EACA,IAAI,CAACiR,gBAAgB,GAAI,YAAY;IACnC,IAAIC,kBAAkB,GAAGlT,OAAO,CAAC2J,UAAU;IAC3C,IAAIwJ,MAAM,GAAG,IAAI;IACjBnT,OAAO,CAAC2J,UAAU,GAAG,CAAC,CAAC;IACvBwJ,MAAM,GAAGnT,OAAO,CAAC2J,UAAU,GAAG,CAAC;IAC/B3J,OAAO,CAAC2J,UAAU,GAAGuJ,kBAAkB;IACvC,OAAOC,MAAM;EACf,CAAC,CAAE,CAAC;EACJ,IAAI,CAACzJ,wBAAwB,GAAG,IAAI,CAACuJ,gBAAgB,GACjDjT,OAAO,CAAC4I,WAAW,GAAG5I,OAAO,CAACqQ,WAAW,GACzC,CAAC;EACL,IAAI,CAACxE,KAAK,GAAG,IAAIhH,YAAY,CAAC,CAAC;EAC/B,IAAI,CAACsJ,aAAa,GAAGnO,OAAO,CAACmO,aAAa,IAAI1N,QAAQ;EAEtD,IAAI,CAACqI,cAAc,GAAGvI,GAAG,CAACuB,GAAG,CAAC9B,OAAO,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC;EAChDnC,OAAO,CAAC+I,WAAW,CAAC,IAAI,CAACD,cAAc,CAAC;EACxC,IAAI,CAAC0C,UAAU,GAAGjL,GAAG,CAACuB,GAAG,CAAC9B,OAAO,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC7C,IAAI,CAAC6G,cAAc,CAACC,WAAW,CAAC,IAAI,CAACyC,UAAU,CAAC;EAChD,IAAI,CAACA,UAAU,CAAC4H,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC;EAC3C,IAAI,CAACvH,KAAK,CAAChI,IAAI,CAAC,IAAI,CAAC2H,UAAU,EAAE,OAAO,EAAElJ,KAAK,CAAC;EAChD,IAAI,CAACuJ,KAAK,CAAChI,IAAI,CAAC,IAAI,CAAC2H,UAAU,EAAE,MAAM,EAAEwH,IAAI,CAAC;EAC9C,IAAI,CAAC7J,gBAAgB,GAAG,IAAI;EAC5B,IAAI,CAACI,eAAe,GAAG,IAAI;EAC3B,IAAI,CAACE,cAAc,GAAG,IAAI;EAC1B,IAAI4J,UAAU,GAAGtT,GAAG,CAAC,IAAI,CAAC+I,cAAc,CAAC;EACzC,IAAI,CAACiC,gBAAgB,GAAGlE,QAAQ,CAACwM,UAAU,CAACvI,MAAM,EAAE,EAAE,CAAC;EACvD,IAAIwI,KAAK,CAAC,IAAI,CAACvI,gBAAgB,CAAC,EAAE;IAChC,IAAI,CAACF,uBAAuB,GAAG,KAAK;IACpC,IAAI,CAACI,aAAa,GAAGrE,KAAK,CAACyM,UAAU,CAACrI,GAAG,CAAC;EAC5C,CAAC,MAAM;IACL,IAAI,CAACH,uBAAuB,GAAG,IAAI;EACrC;EACA,IAAI,CAACY,gBAAgB,GAAG7E,KAAK,CAACyM,UAAU,CAAChM,eAAe,CAAC,GAAGT,KAAK,CAACyM,UAAU,CAAC/L,gBAAgB,CAAC;EAC9F;EACApH,GAAG,CAAC,IAAI,CAAC4I,cAAc,EAAE;IAAEyK,OAAO,EAAE;EAAQ,CAAC,CAAC;EAC9C,IAAI,CAAClK,gBAAgB,GAAGzC,KAAK,CAACyM,UAAU,CAACG,UAAU,CAAC,GAAG5M,KAAK,CAACyM,UAAU,CAACI,WAAW,CAAC;EACpFvT,GAAG,CAAC,IAAI,CAAC4I,cAAc,EAAE;IAAEyK,OAAO,EAAE;EAAG,CAAC,CAAC;EACzC,IAAI,CAACnK,UAAU,GAAG,IAAI;EACtB,IAAI,CAACE,UAAU,GAAG,IAAI;EAEtB,IAAI,CAACN,cAAc,GAAGzI,GAAG,CAACuB,GAAG,CAAC9B,OAAO,CAACmC,IAAI,CAAC,GAAG,CAAC,CAAC;EAChDnC,OAAO,CAAC+I,WAAW,CAAC,IAAI,CAACC,cAAc,CAAC;EACxC,IAAI,CAAC0C,UAAU,GAAGnL,GAAG,CAACuB,GAAG,CAAC9B,OAAO,CAACiC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC7C,IAAI,CAAC+G,cAAc,CAACD,WAAW,CAAC,IAAI,CAAC2C,UAAU,CAAC;EAChD,IAAI,CAACA,UAAU,CAAC0H,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC;EAC3C,IAAI,CAACvH,KAAK,CAAChI,IAAI,CAAC,IAAI,CAAC6H,UAAU,EAAE,OAAO,EAAEpJ,KAAK,CAAC;EAChD,IAAI,CAACuJ,KAAK,CAAChI,IAAI,CAAC,IAAI,CAAC6H,UAAU,EAAE,MAAM,EAAEsH,IAAI,CAAC;EAC9C,IAAI,CAAClJ,gBAAgB,GAAG,IAAI;EAC5B,IAAI,CAACI,gBAAgB,GAAG,IAAI;EAC5B,IAAI,CAACC,aAAa,GAAG,IAAI;EACzB,IAAIuJ,UAAU,GAAG3T,GAAG,CAAC,IAAI,CAACiJ,cAAc,CAAC;EACzC,IAAI,CAACqC,eAAe,GAAGxE,QAAQ,CAAC6M,UAAU,CAACtI,KAAK,EAAE,EAAE,CAAC;EACrD,IAAIkI,KAAK,CAAC,IAAI,CAACjI,eAAe,CAAC,EAAE;IAC/B,IAAI,CAACF,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACI,cAAc,GAAG3E,KAAK,CAAC8M,UAAU,CAAC9I,IAAI,CAAC;EAC9C,CAAC,MAAM;IACL,IAAI,CAACO,sBAAsB,GAAG,IAAI;EACpC;EACA,IAAI,CAACG,oBAAoB,GAAG,IAAI,CAACjB,KAAK,GAAGrD,UAAU,CAAC,IAAI,CAAC0E,UAAU,CAAC,GAAG,IAAI;EAC3E,IAAI,CAACC,gBAAgB,GAAG/E,KAAK,CAAC8M,UAAU,CAACC,cAAc,CAAC,GAAG/M,KAAK,CAAC8M,UAAU,CAACE,iBAAiB,CAAC;EAC9F1T,GAAG,CAAC,IAAI,CAAC8I,cAAc,EAAE;IAAEuK,OAAO,EAAE;EAAQ,CAAC,CAAC;EAC9C,IAAI,CAACvJ,iBAAiB,GAAGpD,KAAK,CAAC8M,UAAU,CAACG,SAAS,CAAC,GAAGjN,KAAK,CAAC8M,UAAU,CAACI,YAAY,CAAC;EACrF5T,GAAG,CAAC,IAAI,CAAC8I,cAAc,EAAE;IAAEuK,OAAO,EAAE;EAAG,CAAC,CAAC;EACzC,IAAI,CAACxJ,WAAW,GAAG,IAAI;EACvB,IAAI,CAACE,UAAU,GAAG,IAAI;EAEtB,IAAI,CAACvD,KAAK,GAAG;IACXxE,CAAC,EACClC,OAAO,CAAC2J,UAAU,IAAI,CAAC,GACnB,OAAO,GACP3J,OAAO,CAAC2J,UAAU,IAAI,IAAI,CAAChB,YAAY,GAAG,IAAI,CAACF,cAAc,GAC7D,KAAK,GACL,IAAI;IACV9F,CAAC,EACC3C,OAAO,CAACuG,SAAS,IAAI,CAAC,GAClB,OAAO,GACPvG,OAAO,CAACuG,SAAS,IAAI,IAAI,CAACF,aAAa,GAAG,IAAI,CAACC,eAAe,GAC9D,KAAK,GACL;EACR,CAAC;EAED,IAAI,CAAClD,OAAO,GAAG,IAAI;EAEnB,IAAI,CAACC,QAAQ,CAACI,QAAQ,CAAC0B,OAAO,CAAC,UAAU4O,WAAW,EAAE;IAAE,OAAOtQ,QAAQ,CAACsQ,WAAW,CAAC,CAAC3P,MAAM,CAAC;EAAE,CAAC,CAAC;EAEhG,IAAI,CAAC4P,aAAa,GAAG3L,IAAI,CAACC,KAAK,CAACtI,OAAO,CAACuG,SAAS,CAAC,CAAC,CAAC;EACpD,IAAI,CAAC0N,cAAc,GAAGjU,OAAO,CAAC2J,UAAU,CAAC,CAAC;EAC1C,IAAI,CAACkC,KAAK,CAAChI,IAAI,CAAC,IAAI,CAAC7D,OAAO,EAAE,QAAQ,EAAE,UAAUoF,CAAC,EAAE;IAAE,OAAOhB,MAAM,CAAC8P,QAAQ,CAAC9O,CAAC,CAAC;EAAE,CAAC,CAAC;EACpF+C,cAAc,CAAC,IAAI,CAAC;AACtB,CAAC;AAED0K,gBAAgB,CAAChS,SAAS,CAACsT,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAI;EACrD,IAAI,CAAC,IAAI,CAAC/Q,OAAO,EAAE;IACjB;EACF;;EAEA;EACA,IAAI,CAACsG,wBAAwB,GAAG,IAAI,CAACuJ,gBAAgB,GACjD,IAAI,CAACjT,OAAO,CAAC4I,WAAW,GAAG,IAAI,CAAC5I,OAAO,CAACqQ,WAAW,GACnD,CAAC;;EAEL;EACAnQ,GAAG,CAAC,IAAI,CAAC4I,cAAc,EAAE;IAAEyK,OAAO,EAAE;EAAQ,CAAC,CAAC;EAC9CrT,GAAG,CAAC,IAAI,CAAC8I,cAAc,EAAE;IAAEuK,OAAO,EAAE;EAAQ,CAAC,CAAC;EAC9C,IAAI,CAAClK,gBAAgB,GACnBzC,KAAK,CAAC7G,GAAG,CAAC,IAAI,CAAC+I,cAAc,CAAC,CAAC0K,UAAU,CAAC,GAC1C5M,KAAK,CAAC7G,GAAG,CAAC,IAAI,CAAC+I,cAAc,CAAC,CAAC2K,WAAW,CAAC;EAC7C,IAAI,CAACzJ,iBAAiB,GACpBpD,KAAK,CAAC7G,GAAG,CAAC,IAAI,CAACiJ,cAAc,CAAC,CAAC6K,SAAS,CAAC,GACzCjN,KAAK,CAAC7G,GAAG,CAAC,IAAI,CAACiJ,cAAc,CAAC,CAAC8K,YAAY,CAAC;;EAE9C;EACA5T,GAAG,CAAC,IAAI,CAAC4I,cAAc,EAAE;IAAEyK,OAAO,EAAE;EAAO,CAAC,CAAC;EAC7CrT,GAAG,CAAC,IAAI,CAAC8I,cAAc,EAAE;IAAEuK,OAAO,EAAE;EAAO,CAAC,CAAC;EAE7CpL,cAAc,CAAC,IAAI,CAAC;EAEpBtC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAC9CA,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;EAE/C3F,GAAG,CAAC,IAAI,CAAC4I,cAAc,EAAE;IAAEyK,OAAO,EAAE;EAAG,CAAC,CAAC;EACzCrT,GAAG,CAAC,IAAI,CAAC8I,cAAc,EAAE;IAAEuK,OAAO,EAAE;EAAG,CAAC,CAAC;AAC3C,CAAC;AAEDV,gBAAgB,CAAChS,SAAS,CAACqT,QAAQ,GAAG,SAASA,QAAQA,CAAE9O,CAAC,EAAE;EAC1D,IAAI,CAAC,IAAI,CAAChC,OAAO,EAAE;IACjB;EACF;EAEA+E,cAAc,CAAC,IAAI,CAAC;EACpBtC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC7F,OAAO,CAACuG,SAAS,GAAG,IAAI,CAACyN,aAAa,CAAC;EAC3EnO,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC7F,OAAO,CAAC2J,UAAU,GAAG,IAAI,CAACsK,cAAc,CAAC;EAE9E,IAAI,CAACD,aAAa,GAAG3L,IAAI,CAACC,KAAK,CAAC,IAAI,CAACtI,OAAO,CAACuG,SAAS,CAAC;EACvD,IAAI,CAAC0N,cAAc,GAAG,IAAI,CAACjU,OAAO,CAAC2J,UAAU;AAC/C,CAAC;AAEDkJ,gBAAgB,CAAChS,SAAS,CAACuT,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;EACvD,IAAI,CAAC,IAAI,CAAChR,OAAO,EAAE;IACjB;EACF;EAEA,IAAI,CAACyI,KAAK,CAACvH,SAAS,CAAC,CAAC;EACtBjD,MAAM,CAAC,IAAI,CAACmK,UAAU,CAAC;EACvBnK,MAAM,CAAC,IAAI,CAACqK,UAAU,CAAC;EACvBrK,MAAM,CAAC,IAAI,CAACyH,cAAc,CAAC;EAC3BzH,MAAM,CAAC,IAAI,CAAC2H,cAAc,CAAC;EAC3B,IAAI,CAACqL,eAAe,CAAC,CAAC;;EAEtB;EACA,IAAI,CAACrU,OAAO,GAAG,IAAI;EACnB,IAAI,CAACwL,UAAU,GAAG,IAAI;EACtB,IAAI,CAACE,UAAU,GAAG,IAAI;EACtB,IAAI,CAAC5C,cAAc,GAAG,IAAI;EAC1B,IAAI,CAACE,cAAc,GAAG,IAAI;EAE1B,IAAI,CAAC5F,OAAO,GAAG,KAAK;AACtB,CAAC;AAEDyP,gBAAgB,CAAChS,SAAS,CAACwT,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAI;EACvE,IAAI,CAACrU,OAAO,CAACQ,SAAS,GAAG,IAAI,CAACR,OAAO,CAACQ,SAAS,CAC5C8T,KAAK,CAAC,GAAG,CAAC,CACV3S,MAAM,CAAC,UAAU4C,IAAI,EAAE;IAAE,OAAO,CAACA,IAAI,CAACyL,KAAK,CAAC,eAAe,CAAC;EAAE,CAAC,CAAC,CAChEuE,IAAI,CAAC,GAAG,CAAC;AACd,CAAC;AAED,eAAe1B,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}