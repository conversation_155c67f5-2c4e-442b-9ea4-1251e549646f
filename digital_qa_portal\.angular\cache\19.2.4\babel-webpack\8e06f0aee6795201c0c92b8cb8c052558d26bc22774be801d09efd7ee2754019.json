{"ast": null, "code": "import { Transition, newId } from \"../transition/index.js\";\nimport schedule from \"../transition/schedule.js\";\nimport { easeCubicInOut } from \"d3-ease\";\nimport { now } from \"d3-timer\";\nvar defaultTiming = {\n  time: null,\n  // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: easeCubicInOut\n};\nfunction inherit(node, id) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id} not found`);\n    }\n  }\n  return timing;\n}\nexport default function (name) {\n  var id, timing;\n  if (name instanceof Transition) {\n    id = name._id, name = name._name;\n  } else {\n    id = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id, i, group, timing || inherit(node, id));\n      }\n    }\n  }\n  return new Transition(groups, this._parents, name, id);\n}", "map": {"version": 3, "names": ["Transition", "newId", "schedule", "easeCubicInOut", "now", "defaultTiming", "time", "delay", "duration", "ease", "inherit", "node", "id", "timing", "__transition", "parentNode", "Error", "name", "_id", "_name", "groups", "_groups", "m", "length", "j", "group", "n", "i", "_parents"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-transition/src/selection/transition.js"], "sourcesContent": ["import {Transition, newId} from \"../transition/index.js\";\nimport schedule from \"../transition/schedule.js\";\nimport {easeCubicInOut} from \"d3-ease\";\nimport {now} from \"d3-timer\";\n\nvar defaultTiming = {\n  time: null, // Set on use.\n  delay: 0,\n  duration: 250,\n  ease: easeCubicInOut\n};\n\nfunction inherit(node, id) {\n  var timing;\n  while (!(timing = node.__transition) || !(timing = timing[id])) {\n    if (!(node = node.parentNode)) {\n      throw new Error(`transition ${id} not found`);\n    }\n  }\n  return timing;\n}\n\nexport default function(name) {\n  var id,\n      timing;\n\n  if (name instanceof Transition) {\n    id = name._id, name = name._name;\n  } else {\n    id = newId(), (timing = defaultTiming).time = now(), name = name == null ? null : name + \"\";\n  }\n\n  for (var groups = this._groups, m = groups.length, j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        schedule(node, name, id, i, group, timing || inherit(node, id));\n      }\n    }\n  }\n\n  return new Transition(groups, this._parents, name, id);\n}\n"], "mappings": "AAAA,SAAQA,UAAU,EAAEC,KAAK,QAAO,wBAAwB;AACxD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAAQC,cAAc,QAAO,SAAS;AACtC,SAAQC,GAAG,QAAO,UAAU;AAE5B,IAAIC,aAAa,GAAG;EAClBC,IAAI,EAAE,IAAI;EAAE;EACZC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,GAAG;EACbC,IAAI,EAAEN;AACR,CAAC;AAED,SAASO,OAAOA,CAACC,IAAI,EAAEC,EAAE,EAAE;EACzB,IAAIC,MAAM;EACV,OAAO,EAAEA,MAAM,GAAGF,IAAI,CAACG,YAAY,CAAC,IAAI,EAAED,MAAM,GAAGA,MAAM,CAACD,EAAE,CAAC,CAAC,EAAE;IAC9D,IAAI,EAAED,IAAI,GAAGA,IAAI,CAACI,UAAU,CAAC,EAAE;MAC7B,MAAM,IAAIC,KAAK,CAAC,cAAcJ,EAAE,YAAY,CAAC;IAC/C;EACF;EACA,OAAOC,MAAM;AACf;AAEA,eAAe,UAASI,IAAI,EAAE;EAC5B,IAAIL,EAAE,EACFC,MAAM;EAEV,IAAII,IAAI,YAAYjB,UAAU,EAAE;IAC9BY,EAAE,GAAGK,IAAI,CAACC,GAAG,EAAED,IAAI,GAAGA,IAAI,CAACE,KAAK;EAClC,CAAC,MAAM;IACLP,EAAE,GAAGX,KAAK,CAAC,CAAC,EAAE,CAACY,MAAM,GAAGR,aAAa,EAAEC,IAAI,GAAGF,GAAG,CAAC,CAAC,EAAEa,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGA,IAAI,GAAG,EAAE;EAC7F;EAEA,KAAK,IAAIG,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;IACpE,KAAK,IAAIC,KAAK,GAAGL,MAAM,CAACI,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACF,MAAM,EAAEZ,IAAI,EAAEgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAE,EAAEC,CAAC,EAAE;MACrE,IAAIhB,IAAI,GAAGc,KAAK,CAACE,CAAC,CAAC,EAAE;QACnBzB,QAAQ,CAACS,IAAI,EAAEM,IAAI,EAAEL,EAAE,EAAEe,CAAC,EAAEF,KAAK,EAAEZ,MAAM,IAAIH,OAAO,CAACC,IAAI,EAAEC,EAAE,CAAC,CAAC;MACjE;IACF;EACF;EAEA,OAAO,IAAIZ,UAAU,CAACoB,MAAM,EAAE,IAAI,CAACQ,QAAQ,EAAEX,IAAI,EAAEL,EAAE,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}