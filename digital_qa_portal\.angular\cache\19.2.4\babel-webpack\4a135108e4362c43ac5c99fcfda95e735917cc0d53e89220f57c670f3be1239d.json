{"ast": null, "code": "function selection_cloneShallow() {\n  var clone = this.cloneNode(false),\n    parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true),\n    parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\nexport default function (deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}", "map": {"version": 3, "names": ["selection_cloneShallow", "clone", "cloneNode", "parent", "parentNode", "insertBefore", "nextS<PERSON>ling", "selection_cloneDeep", "deep", "select"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-selection/src/selection/clone.js"], "sourcesContent": ["function selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\n\nexport default function(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\n"], "mappings": "AAAA,SAASA,sBAAsBA,CAAA,EAAG;EAChC,IAAIC,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,KAAK,CAAC;IAAEC,MAAM,GAAG,IAAI,CAACC,UAAU;EAC3D,OAAOD,MAAM,GAAGA,MAAM,CAACE,YAAY,CAACJ,KAAK,EAAE,IAAI,CAACK,WAAW,CAAC,GAAGL,KAAK;AACtE;AAEA,SAASM,mBAAmBA,CAAA,EAAG;EAC7B,IAAIN,KAAK,GAAG,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC;IAAEC,MAAM,GAAG,IAAI,CAACC,UAAU;EAC1D,OAAOD,MAAM,GAAGA,MAAM,CAACE,YAAY,CAACJ,KAAK,EAAE,IAAI,CAACK,WAAW,CAAC,GAAGL,KAAK;AACtE;AAEA,eAAe,UAASO,IAAI,EAAE;EAC5B,OAAO,IAAI,CAACC,MAAM,CAACD,IAAI,GAAGD,mBAAmB,GAAGP,sBAAsB,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}