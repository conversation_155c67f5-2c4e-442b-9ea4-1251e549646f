import { environment, PARTNERS } from 'environments/environment';

export const ACL = {
    'madura': {
        ADMIN: {
            //admin role
            //top menu ACL
            HELP_CENTER: true,
            HOME: true,
            ADMIN: true,


            //ADMIN Menu

            // Auditor
            ADMIN_AUDITOR_MASTER: true,         // Main auditor menu access
            AUDITOR_CREATE: true,
            AUDITOR_UPDATE: true,
            AUDITOR_DELETE: true,
            AUDITOR_VIEW: true,
            AUDITOR_EXPORT: true,
            AUDITOR_IMPORT: true,
            AUDITOR_ACTIVATE: true,
            AUDITOR_DEACTIVATE: true,
            AUDITOR_ASSIGN_ROLE: true,
            AUDITOR_RESET_PASSWORD: true,
            AUDITOR_MANAGE_PERMISSIONS: true,
            AUDITOR_MANAGE_MFA: true,             // Manage MFA settings
            AUDITOR_MANAGE_PROFILE: true,         // Manage user profile
            AUDITOR_BULK_ACTIONS: true,           // Perform bulk actions on users

            // Technician
            ADMIN_TECHNICIAN_MASTER: true,       // Main technician menu access
            TECHNICIAN_CREATE: true,
            TECHNICIAN_UPDATE: true,
            TECHNICIAN_DELETE: true,
            TECHNICIAN_VIEW: true,
            TECHNICIAN_EXPORT: true,
            TECHNICIAN_IMPORT: true,
            TECHNICIAN_ACTIVATE: true,
            TECHNICIAN_DEACTIVATE: true,
            TECHNICIAN_ASSIGN_ROLE: true,
            TECHNICIAN_RESET_PASSWORD: true,
            TECHNICIAN_MANAGE_PERMISSIONS: true,
            TECHNICIAN_MANAGE_MFA: true,         // Manage MFA settings
            TECHNICIAN_MANAGE_PROFILE: true,     // Manage user profile
            TECHNICIAN_BULK_ACTIONS: true,       // Perform bulk actions on users

            //Brand
            ADMIN_BRAND_MASTER: true,
            ADMIN_BRAND_MASTER_CREATE: true,
            ADMIN_BRAND_MASTER_EDIT: true,
            ADMIN_BRAND_MASTER_VIEW: true,
            ADMIN_BRAND_MASTER_DELETE: true,

            //Category
            ADMIN_CATEGORY_MASTER: true,
            ADMIN_CATEGORY_MASTER_CREATE: true,
            ADMIN_CATEGORY_MASTER_EDIT: true,
            ADMIN_CATEGORY_MASTER_VIEW: true,
            ADMIN_CATEGORY_MASTER_DELETE: true,





            //Users
            ADMIN_USERS: true,                 // Main users menu access
            USER_MASTER: true,                 // Users list view access
            USER_CREATE: true,                 // Create new user
            USER_UPDATE: true,                 // Update existing user
            USER_DELETE: true,                 // Delete user
            USER_VIEW: true,                   // View user details
            USER_EXPORT: true,                 // Export users data
            USER_IMPORT: true,                 // Import users data
            USER_ACTIVATE: true,               // Activate user
            USER_DEACTIVATE: true,             // Deactivate user
            USER_ASSIGN_ROLE: true,            // Assign roles to users
            USER_RESET_PASSWORD: true,         // Reset user password
            USER_MANAGE_PERMISSIONS: true,     // Manage user permissions
            USER_MANAGE_MFA: true,             // Manage MFA settings
            USER_MANAGE_PROFILE: true,         // Manage user profile
            USER_BULK_ACTIONS: true,           // Perform bulk actions on users





            // Roles
            ADMIN_ROLE_MASTER: true,         // Main roles menu access
            ROLE_CREATE: true,
            ROLE_UPDATE: true,
            ROLE_DELETE: true,


        },
    }
}
