{"ast": null, "code": "export const ACL = {\n  'madura': {\n    ADMIN: {\n      //admin role\n      //top menu ACL\n      HELP_CENTER: true,\n      HOME: true,\n      ADMIN: true,\n      //ADMIN Menu\n      // Auditor\n      ADMIN_AUDITOR_MASTER: true,\n      // Main auditor menu access\n      AUDITOR_CREATE: true,\n      AUDITOR_UPDATE: true,\n      AUDITOR_DELETE: true,\n      AUDITOR_VIEW: true,\n      AUDITOR_EXPORT: true,\n      AUDITOR_IMPORT: true,\n      AUDITOR_ACTIVATE: true,\n      AUDITOR_DEACTIVATE: true,\n      AUDITOR_ASSIGN_ROLE: true,\n      AUDITOR_RESET_PASSWORD: true,\n      AUDITOR_MANAGE_PERMISSIONS: true,\n      AUDITOR_MANAGE_MFA: true,\n      // Manage MFA settings\n      AUDITOR_MANAGE_PROFILE: true,\n      // Manage user profile\n      AUDITOR_BULK_ACTIONS: true,\n      // Perform bulk actions on users\n      // Technician\n      ADMIN_TECHNICIAN_MASTER: true,\n      // Main technician menu access\n      TECHNICIAN_CREATE: true,\n      TECHNICIAN_UPDATE: true,\n      TECHNICIAN_DELETE: true,\n      TECHNICIAN_VIEW: true,\n      TECHNICIAN_EXPORT: true,\n      TECHNICIAN_IMPORT: true,\n      TECHNICIAN_ACTIVATE: true,\n      TECHNICIAN_DEACTIVATE: true,\n      TECHNICIAN_ASSIGN_ROLE: true,\n      TECHNICIAN_RESET_PASSWORD: true,\n      TECHNICIAN_MANAGE_PERMISSIONS: true,\n      TECHNICIAN_MANAGE_MFA: true,\n      // Manage MFA settings\n      TECHNICIAN_MANAGE_PROFILE: true,\n      // Manage user profile\n      TECHNICIAN_BULK_ACTIONS: true,\n      // Perform bulk actions on users\n      //Brand\n      ADMIN_BRAND_MASTER: true,\n      ADMIN_BRAND_MASTER_CREATE: true,\n      ADMIN_BRAND_MASTER_EDIT: true,\n      ADMIN_BRAND_MASTER_VIEW: true,\n      ADMIN_BRAND_MASTER_DELETE: true,\n      //Category\n      ADMIN_CATEGORY_MASTER: true,\n      ADMIN_CATEGORY_MASTER_CREATE: true,\n      ADMIN_CATEGORY_MASTER_EDIT: true,\n      ADMIN_CATEGORY_MASTER_VIEW: true,\n      ADMIN_CATEGORY_MASTER_DELETE: true,\n      //Users\n      ADMIN_USERS: true,\n      // Main users menu access\n      USER_MASTER: true,\n      // Users list view access\n      USER_CREATE: true,\n      // Create new user\n      USER_UPDATE: true,\n      // Update existing user\n      USER_DELETE: true,\n      // Delete user\n      USER_VIEW: true,\n      // View user details\n      USER_EXPORT: true,\n      // Export users data\n      USER_IMPORT: true,\n      // Import users data\n      USER_ACTIVATE: true,\n      // Activate user\n      USER_DEACTIVATE: true,\n      // Deactivate user\n      USER_ASSIGN_ROLE: true,\n      // Assign roles to users\n      USER_RESET_PASSWORD: true,\n      // Reset user password\n      USER_MANAGE_PERMISSIONS: true,\n      // Manage user permissions\n      USER_MANAGE_MFA: true,\n      // Manage MFA settings\n      USER_MANAGE_PROFILE: true,\n      // Manage user profile\n      USER_BULK_ACTIONS: true,\n      // Perform bulk actions on users\n      // Roles\n      ADMIN_ROLE_MASTER: true,\n      // Main roles menu access\n      ROLE_CREATE: true,\n      ROLE_UPDATE: true,\n      ROLE_DELETE: true\n    }\n  }\n};", "map": {"version": 3, "names": ["ACL", "ADMIN", "HELP_CENTER", "HOME", "ADMIN_AUDITOR_MASTER", "AUDITOR_CREATE", "AUDITOR_UPDATE", "AUDITOR_DELETE", "AUDITOR_VIEW", "AUDITOR_EXPORT", "AUDITOR_IMPORT", "AUDITOR_ACTIVATE", "AUDITOR_DEACTIVATE", "AUDITOR_ASSIGN_ROLE", "AUDITOR_RESET_PASSWORD", "AUDITOR_MANAGE_PERMISSIONS", "AUDITOR_MANAGE_MFA", "AUDITOR_MANAGE_PROFILE", "AUDITOR_BULK_ACTIONS", "ADMIN_TECHNICIAN_MASTER", "TECHNICIAN_CREATE", "TECHNICIAN_UPDATE", "TECHNICIAN_DELETE", "TECHNICIAN_VIEW", "TECHNICIAN_EXPORT", "TECHNICIAN_IMPORT", "TECHNICIAN_ACTIVATE", "TECHNICIAN_DEACTIVATE", "TECHNICIAN_ASSIGN_ROLE", "TECHNICIAN_RESET_PASSWORD", "TECHNICIAN_MANAGE_PERMISSIONS", "TECHNICIAN_MANAGE_MFA", "TECHNICIAN_MANAGE_PROFILE", "TECHNICIAN_BULK_ACTIONS", "ADMIN_BRAND_MASTER", "ADMIN_BRAND_MASTER_CREATE", "ADMIN_BRAND_MASTER_EDIT", "ADMIN_BRAND_MASTER_VIEW", "ADMIN_BRAND_MASTER_DELETE", "ADMIN_CATEGORY_MASTER", "ADMIN_CATEGORY_MASTER_CREATE", "ADMIN_CATEGORY_MASTER_EDIT", "ADMIN_CATEGORY_MASTER_VIEW", "ADMIN_CATEGORY_MASTER_DELETE", "ADMIN_USERS", "USER_MASTER", "USER_CREATE", "USER_UPDATE", "USER_DELETE", "USER_VIEW", "USER_EXPORT", "USER_IMPORT", "USER_ACTIVATE", "USER_DEACTIVATE", "USER_ASSIGN_ROLE", "USER_RESET_PASSWORD", "USER_MANAGE_PERMISSIONS", "USER_MANAGE_MFA", "USER_MANAGE_PROFILE", "USER_BULK_ACTIONS", "ADMIN_ROLE_MASTER", "ROLE_CREATE", "ROLE_UPDATE", "ROLE_DELETE"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\app\\core\\acl\\acl.ts"], "sourcesContent": ["import { environment, PARTNERS } from 'environments/environment';\r\n\r\nexport const ACL = {\r\n    'madura': {\r\n        ADMIN: {\r\n            //admin role\r\n            //top menu ACL\r\n            HELP_CENTER: true,\r\n            HOME: true,\r\n            ADMIN: true,\r\n\r\n\r\n            //ADMIN Menu\r\n\r\n            // Auditor\r\n            ADMIN_AUDITOR_MASTER: true,         // Main auditor menu access\r\n            AUDITOR_CREATE: true,\r\n            AUDITOR_UPDATE: true,\r\n            AUDITOR_DELETE: true,\r\n            AUDITOR_VIEW: true,\r\n            AUDITOR_EXPORT: true,\r\n            AUDITOR_IMPORT: true,\r\n            AUDITOR_ACTIVATE: true,\r\n            AUDITOR_DEACTIVATE: true,\r\n            AUDITOR_ASSIGN_ROLE: true,\r\n            AUDITOR_RESET_PASSWORD: true,\r\n            AUDITOR_MANAGE_PERMISSIONS: true,\r\n            AUDITOR_MANAGE_MFA: true,             // Manage MFA settings\r\n            AUDITOR_MANAGE_PROFILE: true,         // Manage user profile\r\n            AUDITOR_BULK_ACTIONS: true,           // Perform bulk actions on users\r\n\r\n            // Technician\r\n            ADMIN_TECHNICIAN_MASTER: true,       // Main technician menu access\r\n            TECHNICIAN_CREATE: true,\r\n            TECHNICIAN_UPDATE: true,\r\n            TECHNICIAN_DELETE: true,\r\n            TECHNICIAN_VIEW: true,\r\n            TECHNICIAN_EXPORT: true,\r\n            TECHNICIAN_IMPORT: true,\r\n            TECHNICIAN_ACTIVATE: true,\r\n            TECHNICIAN_DEACTIVATE: true,\r\n            TECHNICIAN_ASSIGN_ROLE: true,\r\n            TECHNICIAN_RESET_PASSWORD: true,\r\n            TECHNICIAN_MANAGE_PERMISSIONS: true,\r\n            TECHNICIAN_MANAGE_MFA: true,         // Manage MFA settings\r\n            TECHNICIAN_MANAGE_PROFILE: true,     // Manage user profile\r\n            TECHNICIAN_BULK_ACTIONS: true,       // Perform bulk actions on users\r\n\r\n            //Brand\r\n            ADMIN_BRAND_MASTER: true,\r\n            ADMIN_BRAND_MASTER_CREATE: true,\r\n            ADMIN_BRAND_MASTER_EDIT: true,\r\n            ADMIN_BRAND_MASTER_VIEW: true,\r\n            ADMIN_BRAND_MASTER_DELETE: true,\r\n\r\n            //Category\r\n            ADMIN_CATEGORY_MASTER: true,\r\n            ADMIN_CATEGORY_MASTER_CREATE: true,\r\n            ADMIN_CATEGORY_MASTER_EDIT: true,\r\n            ADMIN_CATEGORY_MASTER_VIEW: true,\r\n            ADMIN_CATEGORY_MASTER_DELETE: true,\r\n\r\n\r\n\r\n\r\n\r\n            //Users\r\n            ADMIN_USERS: true,                 // Main users menu access\r\n            USER_MASTER: true,                 // Users list view access\r\n            USER_CREATE: true,                 // Create new user\r\n            USER_UPDATE: true,                 // Update existing user\r\n            USER_DELETE: true,                 // Delete user\r\n            USER_VIEW: true,                   // View user details\r\n            USER_EXPORT: true,                 // Export users data\r\n            USER_IMPORT: true,                 // Import users data\r\n            USER_ACTIVATE: true,               // Activate user\r\n            USER_DEACTIVATE: true,             // Deactivate user\r\n            USER_ASSIGN_ROLE: true,            // Assign roles to users\r\n            USER_RESET_PASSWORD: true,         // Reset user password\r\n            USER_MANAGE_PERMISSIONS: true,     // Manage user permissions\r\n            USER_MANAGE_MFA: true,             // Manage MFA settings\r\n            USER_MANAGE_PROFILE: true,         // Manage user profile\r\n            USER_BULK_ACTIONS: true,           // Perform bulk actions on users\r\n\r\n\r\n\r\n\r\n\r\n            // Roles\r\n            ADMIN_ROLE_MASTER: true,         // Main roles menu access\r\n            ROLE_CREATE: true,\r\n            ROLE_UPDATE: true,\r\n            ROLE_DELETE: true,\r\n\r\n\r\n        },\r\n    }\r\n}\r\n"], "mappings": "AAEA,OAAO,MAAMA,GAAG,GAAG;EACf,QAAQ,EAAE;IACNC,KAAK,EAAE;MACH;MACA;MACAC,WAAW,EAAE,IAAI;MACjBC,IAAI,EAAE,IAAI;MACVF,KAAK,EAAE,IAAI;MAGX;MAEA;MACAG,oBAAoB,EAAE,IAAI;MAAU;MACpCC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,IAAI;MACpBC,YAAY,EAAE,IAAI;MAClBC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,IAAI;MACxBC,mBAAmB,EAAE,IAAI;MACzBC,sBAAsB,EAAE,IAAI;MAC5BC,0BAA0B,EAAE,IAAI;MAChCC,kBAAkB,EAAE,IAAI;MAAc;MACtCC,sBAAsB,EAAE,IAAI;MAAU;MACtCC,oBAAoB,EAAE,IAAI;MAAY;MAEtC;MACAC,uBAAuB,EAAE,IAAI;MAAQ;MACrCC,iBAAiB,EAAE,IAAI;MACvBC,iBAAiB,EAAE,IAAI;MACvBC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,IAAI;MACrBC,iBAAiB,EAAE,IAAI;MACvBC,iBAAiB,EAAE,IAAI;MACvBC,mBAAmB,EAAE,IAAI;MACzBC,qBAAqB,EAAE,IAAI;MAC3BC,sBAAsB,EAAE,IAAI;MAC5BC,yBAAyB,EAAE,IAAI;MAC/BC,6BAA6B,EAAE,IAAI;MACnCC,qBAAqB,EAAE,IAAI;MAAU;MACrCC,yBAAyB,EAAE,IAAI;MAAM;MACrCC,uBAAuB,EAAE,IAAI;MAAQ;MAErC;MACAC,kBAAkB,EAAE,IAAI;MACxBC,yBAAyB,EAAE,IAAI;MAC/BC,uBAAuB,EAAE,IAAI;MAC7BC,uBAAuB,EAAE,IAAI;MAC7BC,yBAAyB,EAAE,IAAI;MAE/B;MACAC,qBAAqB,EAAE,IAAI;MAC3BC,4BAA4B,EAAE,IAAI;MAClCC,0BAA0B,EAAE,IAAI;MAChCC,0BAA0B,EAAE,IAAI;MAChCC,4BAA4B,EAAE,IAAI;MAMlC;MACAC,WAAW,EAAE,IAAI;MAAkB;MACnCC,WAAW,EAAE,IAAI;MAAkB;MACnCC,WAAW,EAAE,IAAI;MAAkB;MACnCC,WAAW,EAAE,IAAI;MAAkB;MACnCC,WAAW,EAAE,IAAI;MAAkB;MACnCC,SAAS,EAAE,IAAI;MAAoB;MACnCC,WAAW,EAAE,IAAI;MAAkB;MACnCC,WAAW,EAAE,IAAI;MAAkB;MACnCC,aAAa,EAAE,IAAI;MAAgB;MACnCC,eAAe,EAAE,IAAI;MAAc;MACnCC,gBAAgB,EAAE,IAAI;MAAa;MACnCC,mBAAmB,EAAE,IAAI;MAAU;MACnCC,uBAAuB,EAAE,IAAI;MAAM;MACnCC,eAAe,EAAE,IAAI;MAAc;MACnCC,mBAAmB,EAAE,IAAI;MAAU;MACnCC,iBAAiB,EAAE,IAAI;MAAY;MAMnC;MACAC,iBAAiB,EAAE,IAAI;MAAU;MACjCC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE,IAAI;MACjBC,WAAW,EAAE;;;CAKxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}