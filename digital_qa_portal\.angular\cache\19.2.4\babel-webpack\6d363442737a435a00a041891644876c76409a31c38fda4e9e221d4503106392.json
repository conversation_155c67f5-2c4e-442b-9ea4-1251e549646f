{"ast": null, "code": "/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function (arg) {\n    return func(transform(arg));\n  };\n}\nexport default overArg;", "map": {"version": 3, "names": ["overArg", "func", "transform", "arg"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_overArg.js"], "sourcesContent": ["/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\nexport default overArg;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAOA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAChC,OAAO,UAASC,GAAG,EAAE;IACnB,OAAOF,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,CAAC;EAC7B,CAAC;AACH;AAEA,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}