export interface IAppConfig {
    configuration: {
        getTenant: {
            url: string,
            type: string,
        },
        getTenantConfig: {
            url: string,
            type: string,
            paramList: { tenantUid: string }
        }
    },
    auth: {
        login: {
            url: string,
            type: string,
        },
        mfaRegistration: {
            url: string,
            type: string,
        },
        mfaValidation: {
            url: string,
            type: string,
            paramList: { mfaCode: string }
        }
    },
    administration: {
        auditor: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        },
        technician: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        },
        roles: {
            getUserRoleList: {
                url: string,
                type: string,
            },
            createORUpdate: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string
            }

        },
        users: {
            get: {
                url: string,
                type: string,
                paramList: { role: string }
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            activateUser: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            deActivateUser: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        },
        brand: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
            }
        },
        categories: {
            get: {
                url: string,
                type: string,
            },
            create: {
                url: string,
                type: string,
            },
            update: {
                url: string,
                type: string,
                paramList: { uuid: string }
            },
            delete: {
                url: string,
                type: string,
                paramList: { uuid: string }
            }
        },
    },
    notification: {
        get: {
            url: string,
            type: string
        },
        getLatestNotification: {
            url: string,
            type: string
        },
        updateNotificationStatusByUid: {
            url: string,
            type: string,
            paramList: { uuid: any, status: any },
        },
        getNotificationHistory: {
            url: string,
            type: string,
            paramList: { from: any, to: any },
        },
        readAllNotifications: {
            url: string,
            type: string,
        }
    },

}
