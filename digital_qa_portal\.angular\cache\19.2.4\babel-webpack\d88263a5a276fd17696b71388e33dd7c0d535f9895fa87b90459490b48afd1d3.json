{"ast": null, "code": "import createCtor from './_createCtor.js';\nimport root from './_root.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1;\n\n/**\n * Creates a function that wraps `func` to invoke it with the optional `this`\n * binding of `thisArg`.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {*} [thisArg] The `this` binding of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createBind(func, bitmask, thisArg) {\n  var isBind = bitmask & WRAP_BIND_FLAG,\n    Ctor = createCtor(func);\n  function wrapper() {\n    var fn = this && this !== root && this instanceof wrapper ? Ctor : func;\n    return fn.apply(isBind ? thisArg : this, arguments);\n  }\n  return wrapper;\n}\nexport default createBind;", "map": {"version": 3, "names": ["createCtor", "root", "WRAP_BIND_FLAG", "createBind", "func", "bitmask", "thisArg", "isBind", "Ctor", "wrapper", "fn", "apply", "arguments"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_createBind.js"], "sourcesContent": ["import createCtor from './_createCtor.js';\nimport root from './_root.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1;\n\n/**\n * Creates a function that wraps `func` to invoke it with the optional `this`\n * binding of `thisArg`.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {*} [thisArg] The `this` binding of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createBind(func, bitmask, thisArg) {\n  var isBind = bitmask & WRAP_BIND_FLAG,\n      Ctor = createCtor(func);\n\n  function wrapper() {\n    var fn = (this && this !== root && this instanceof wrapper) ? Ctor : func;\n    return fn.apply(isBind ? thisArg : this, arguments);\n  }\n  return wrapper;\n}\n\nexport default createBind;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,cAAc,GAAG,CAAC;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAC1C,IAAIC,MAAM,GAAGF,OAAO,GAAGH,cAAc;IACjCM,IAAI,GAAGR,UAAU,CAACI,IAAI,CAAC;EAE3B,SAASK,OAAOA,CAAA,EAAG;IACjB,IAAIC,EAAE,GAAI,IAAI,IAAI,IAAI,KAAKT,IAAI,IAAI,IAAI,YAAYQ,OAAO,GAAID,IAAI,GAAGJ,IAAI;IACzE,OAAOM,EAAE,CAACC,KAAK,CAACJ,MAAM,GAAGD,OAAO,GAAG,IAAI,EAAEM,SAAS,CAAC;EACrD;EACA,OAAOH,OAAO;AAChB;AAEA,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}