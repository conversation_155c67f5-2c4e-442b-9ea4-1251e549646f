{"ast": null, "code": "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n    proto = typeof Ctor == 'function' && Ctor.prototype || objectProto;\n  return value === proto;\n}\nexport default isPrototype;", "map": {"version": 3, "names": ["objectProto", "Object", "prototype", "isPrototype", "value", "Ctor", "constructor", "proto"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_isPrototype.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\nexport default isPrototype;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,IAAI,GAAGD,KAAK,IAAIA,KAAK,CAACE,WAAW;IACjCC,KAAK,GAAI,OAAOF,IAAI,IAAI,UAAU,IAAIA,IAAI,CAACH,SAAS,IAAKF,WAAW;EAExE,OAAOI,KAAK,KAAKG,KAAK;AACxB;AAEA,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}