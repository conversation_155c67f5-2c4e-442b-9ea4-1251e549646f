{"ast": null, "code": "function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\nClassList.prototype = {\n  add: function (name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function (name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function (name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\nfunction classedAdd(node, names) {\n  var list = classList(node),\n    i = -1,\n    n = names.length;\n  while (++i < n) list.add(names[i]);\n}\nfunction classedRemove(node, names) {\n  var list = classList(node),\n    i = -1,\n    n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\nfunction classedTrue(names) {\n  return function () {\n    classedAdd(this, names);\n  };\n}\nfunction classedFalse(names) {\n  return function () {\n    classedRemove(this, names);\n  };\n}\nfunction classedFunction(names, value) {\n  return function () {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\nexport default function (name, value) {\n  var names = classArray(name + \"\");\n  if (arguments.length < 2) {\n    var list = classList(this.node()),\n      i = -1,\n      n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n  return this.each((typeof value === \"function\" ? classedFunction : value ? classedTrue : classedFalse)(names, value));\n}", "map": {"version": 3, "names": ["classArray", "string", "trim", "split", "classList", "node", "ClassList", "_node", "_names", "getAttribute", "prototype", "add", "name", "i", "indexOf", "push", "setAttribute", "join", "remove", "splice", "contains", "classedAdd", "names", "list", "n", "length", "classedRemove", "classedTrue", "classedFalse", "classedFunction", "value", "apply", "arguments", "each"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-selection/src/selection/classed.js"], "sourcesContent": ["function classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\n\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\n\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\n\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\n\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.add(names[i]);\n}\n\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n) list.remove(names[i]);\n}\n\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\n\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\n\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\n\nexport default function(name, value) {\n  var names = classArray(name + \"\");\n\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n) if (!list.contains(names[i])) return false;\n    return true;\n  }\n\n  return this.each((typeof value === \"function\"\n      ? classedFunction : value\n      ? classedTrue\n      : classedFalse)(names, value));\n}\n"], "mappings": "AAAA,SAASA,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAOA,MAAM,CAACC,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC;AACrC;AAEA,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,OAAOA,IAAI,CAACD,SAAS,IAAI,IAAIE,SAAS,CAACD,IAAI,CAAC;AAC9C;AAEA,SAASC,SAASA,CAACD,IAAI,EAAE;EACvB,IAAI,CAACE,KAAK,GAAGF,IAAI;EACjB,IAAI,CAACG,MAAM,GAAGR,UAAU,CAACK,IAAI,CAACI,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAC5D;AAEAH,SAAS,CAACI,SAAS,GAAG;EACpBC,GAAG,EAAE,SAAAA,CAASC,IAAI,EAAE;IAClB,IAAIC,CAAC,GAAG,IAAI,CAACL,MAAM,CAACM,OAAO,CAACF,IAAI,CAAC;IACjC,IAAIC,CAAC,GAAG,CAAC,EAAE;MACT,IAAI,CAACL,MAAM,CAACO,IAAI,CAACH,IAAI,CAAC;MACtB,IAAI,CAACL,KAAK,CAACS,YAAY,CAAC,OAAO,EAAE,IAAI,CAACR,MAAM,CAACS,IAAI,CAAC,GAAG,CAAC,CAAC;IACzD;EACF,CAAC;EACDC,MAAM,EAAE,SAAAA,CAASN,IAAI,EAAE;IACrB,IAAIC,CAAC,GAAG,IAAI,CAACL,MAAM,CAACM,OAAO,CAACF,IAAI,CAAC;IACjC,IAAIC,CAAC,IAAI,CAAC,EAAE;MACV,IAAI,CAACL,MAAM,CAACW,MAAM,CAACN,CAAC,EAAE,CAAC,CAAC;MACxB,IAAI,CAACN,KAAK,CAACS,YAAY,CAAC,OAAO,EAAE,IAAI,CAACR,MAAM,CAACS,IAAI,CAAC,GAAG,CAAC,CAAC;IACzD;EACF,CAAC;EACDG,QAAQ,EAAE,SAAAA,CAASR,IAAI,EAAE;IACvB,OAAO,IAAI,CAACJ,MAAM,CAACM,OAAO,CAACF,IAAI,CAAC,IAAI,CAAC;EACvC;AACF,CAAC;AAED,SAASS,UAAUA,CAAChB,IAAI,EAAEiB,KAAK,EAAE;EAC/B,IAAIC,IAAI,GAAGnB,SAAS,CAACC,IAAI,CAAC;IAAEQ,CAAC,GAAG,CAAC,CAAC;IAAEW,CAAC,GAAGF,KAAK,CAACG,MAAM;EACpD,OAAO,EAAEZ,CAAC,GAAGW,CAAC,EAAED,IAAI,CAACZ,GAAG,CAACW,KAAK,CAACT,CAAC,CAAC,CAAC;AACpC;AAEA,SAASa,aAAaA,CAACrB,IAAI,EAAEiB,KAAK,EAAE;EAClC,IAAIC,IAAI,GAAGnB,SAAS,CAACC,IAAI,CAAC;IAAEQ,CAAC,GAAG,CAAC,CAAC;IAAEW,CAAC,GAAGF,KAAK,CAACG,MAAM;EACpD,OAAO,EAAEZ,CAAC,GAAGW,CAAC,EAAED,IAAI,CAACL,MAAM,CAACI,KAAK,CAACT,CAAC,CAAC,CAAC;AACvC;AAEA,SAASc,WAAWA,CAACL,KAAK,EAAE;EAC1B,OAAO,YAAW;IAChBD,UAAU,CAAC,IAAI,EAAEC,KAAK,CAAC;EACzB,CAAC;AACH;AAEA,SAASM,YAAYA,CAACN,KAAK,EAAE;EAC3B,OAAO,YAAW;IAChBI,aAAa,CAAC,IAAI,EAAEJ,KAAK,CAAC;EAC5B,CAAC;AACH;AAEA,SAASO,eAAeA,CAACP,KAAK,EAAEQ,KAAK,EAAE;EACrC,OAAO,YAAW;IAChB,CAACA,KAAK,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,GAAGX,UAAU,GAAGK,aAAa,EAAE,IAAI,EAAEJ,KAAK,CAAC;EAC1E,CAAC;AACH;AAEA,eAAe,UAASV,IAAI,EAAEkB,KAAK,EAAE;EACnC,IAAIR,KAAK,GAAGtB,UAAU,CAACY,IAAI,GAAG,EAAE,CAAC;EAEjC,IAAIoB,SAAS,CAACP,MAAM,GAAG,CAAC,EAAE;IACxB,IAAIF,IAAI,GAAGnB,SAAS,CAAC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MAAEQ,CAAC,GAAG,CAAC,CAAC;MAAEW,CAAC,GAAGF,KAAK,CAACG,MAAM;IAC3D,OAAO,EAAEZ,CAAC,GAAGW,CAAC,EAAE,IAAI,CAACD,IAAI,CAACH,QAAQ,CAACE,KAAK,CAACT,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;IAC1D,OAAO,IAAI;EACb;EAEA,OAAO,IAAI,CAACoB,IAAI,CAAC,CAAC,OAAOH,KAAK,KAAK,UAAU,GACvCD,eAAe,GAAGC,KAAK,GACvBH,WAAW,GACXC,YAAY,EAAEN,KAAK,EAAEQ,KAAK,CAAC,CAAC;AACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}