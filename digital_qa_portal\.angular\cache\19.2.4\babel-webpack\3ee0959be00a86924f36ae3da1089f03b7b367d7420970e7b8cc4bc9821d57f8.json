{"ast": null, "code": "import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';\nexport const TECHNICIAN_FORM_MODEL = {\n  code: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Code\",\n    placeholder: 'Ex. TECH001',\n    topGroupTitleIcon: 'pi-user',\n    severity: ICON_BUTTON_COLOR.WARNING,\n    topGroupTitle: \"Technician Details\",\n    show: true,\n    rules: {\n      required: true,\n      maxLength: 250\n    }\n  },\n  userName: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Username\",\n    placeholder: 'Ex. john123',\n    show: true,\n    rules: {\n      required: true,\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_NO_SPACE,\n      minLength: 5,\n      maxLength: 20\n    },\n    tooltip: \"Username should be alphabets and numbers (No space allowed)\"\n  },\n  password: {\n    type: FORM_CONTROL_TYPES.PASSWORD,\n    value: \"\",\n    label: \"Password\",\n    placeholder: 'Ex. Password123',\n    show: true,\n    rules: {\n      required: true,\n      maxLength: 250\n    }\n  },\n  firstName: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"First Name\",\n    placeholder: 'Ex. John',\n    show: true,\n    rules: {\n      required: true,\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,\n      maxLength: 250\n    }\n  },\n  lastName: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Last Name\",\n    placeholder: 'Ex. Doe',\n    show: true,\n    rules: {\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,\n      maxLength: 250\n    }\n  },\n  role: {\n    type: FORM_CONTROL_TYPES.SINGLE_SELECT,\n    onChange: event => {},\n    options: [],\n    value: \"\",\n    label: \"Role\",\n    placeholder: 'Select role',\n    show: true,\n    rules: {\n      required: true\n    }\n  },\n  email: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Email\",\n    placeholder: 'Ex. <EMAIL>',\n    topGroupTitle: 'Contact Details',\n    topGroupTitleIcon: 'pi-phone',\n    severity: ICON_BUTTON_COLOR.WARNING,\n    show: true,\n    rules: {\n      required: true,\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,\n      maxLength: 250\n    }\n  },\n  altemail: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Alternative Email\",\n    placeholder: 'Ex. <EMAIL>',\n    show: true,\n    rules: {\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,\n      maxLength: 250\n    }\n  },\n  contactNo: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Contact No\",\n    placeholder: 'Ex. +1234567890',\n    show: true,\n    rules: {\n      required: true,\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.PHONE_PATTERN,\n      maxLength: 15\n    }\n  },\n  addressLine1: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Address Line 1\",\n    placeholder: 'Ex. 123 Main Street',\n    topGroupTitle: 'Address Details',\n    topGroupTitleIcon: 'pi-map-marker',\n    severity: ICON_BUTTON_COLOR.WARNING,\n    show: true,\n    rules: {\n      maxLength: 250\n    }\n  },\n  addressLine2: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Address Line 2\",\n    placeholder: 'Ex. Apt 4B',\n    show: true,\n    rules: {\n      maxLength: 250\n    }\n  },\n  city: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"City\",\n    placeholder: 'Ex. New York',\n    show: true,\n    rules: {\n      maxLength: 250,\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN\n    }\n  },\n  country: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Country\",\n    placeholder: 'Ex. USA',\n    show: true,\n    rules: {\n      maxLength: 250,\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN\n    }\n  },\n  pincode: {\n    type: FORM_CONTROL_TYPES.TEXT,\n    value: \"\",\n    label: \"Pincode\",\n    placeholder: 'Ex. 123456',\n    show: true,\n    rules: {\n      pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,\n      maxLength: 10\n    }\n  },\n  isActive: {\n    type: FORM_CONTROL_TYPES.CHECKBOX,\n    value: true,\n    label: \"Active\",\n    show: true\n  },\n  mappedWith: {\n    type: FORM_CONTROL_TYPES.SINGLE_SELECT,\n    onChange: event => {},\n    options: [],\n    value: \"\",\n    label: \"Mapped With\",\n    placeholder: 'Select mapping',\n    show: true,\n    rules: {\n      // Not required\n    }\n  }\n};", "map": {"version": 3, "names": ["FORM_CONTROL_TYPES", "FORM_FIELDS_CONSTANTS_VALUES", "ICON_BUTTON_COLOR", "TECHNICIAN_FORM_MODEL", "code", "type", "TEXT", "value", "label", "placeholder", "topGroupTitleIcon", "severity", "WARNING", "topGroupTitle", "show", "rules", "required", "max<PERSON><PERSON><PERSON>", "userName", "pattern", "ALPHA_NUMERIC_PATTERN_NO_SPACE", "<PERSON><PERSON><PERSON><PERSON>", "tooltip", "password", "PASSWORD", "firstName", "ALPHA_NUMERIC_PATTERN_WITH_SPACE", "lastName", "role", "SINGLE_SELECT", "onChange", "event", "options", "email", "EMAIL_PATTERN", "altemail", "contactNo", "PHONE_PATTERN", "addressLine1", "addressLine2", "city", "ALPHA_PATTERN", "country", "pincode", "NUMERIC_PATTERN", "isActive", "CHECKBOX", "mappedWith"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\app\\modules\\admin\\technician\\create-edit-technician\\technician-form.model.ts"], "sourcesContent": ["import { FORM_CONTROL_TYPES, FORM_FIELDS_CONSTANTS_VALUES, ICON_BUTTON_COLOR } from 'app/shared/tapas-ui';\n\nexport const TECHNICIAN_FORM_MODEL = {\n    code: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Code\",\n        placeholder: 'Ex. TECH001',\n        topGroupTitleIcon: 'pi-user',\n        severity: ICON_BUTTON_COLOR.WARNING,\n        topGroupTitle: \"Technician Details\",\n        show: true,\n        rules: {\n            required: true,\n            maxLength: 250,\n        }\n    },\n    userName: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Username\",\n        placeholder: 'Ex. john123',\n        show: true,\n        rules: {\n            required: true,\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_NO_SPACE,\n            minLength: 5,\n            maxLength: 20,\n        },\n        tooltip: \"Username should be alphabets and numbers (No space allowed)\"\n    },\n    password: {\n        type: FORM_CONTROL_TYPES.PASSWORD,\n        value: \"\",\n        label: \"Password\",\n        placeholder: 'Ex. Password123',\n        show: true,\n        rules: {\n            required: true,\n            maxLength: 250,\n        }\n    },\n    firstName: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"First Name\",\n        placeholder: 'Ex. John',\n        show: true,\n        rules: {\n            required: true,\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,\n            maxLength: 250,\n        }\n    },\n    lastName: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Last Name\",\n        placeholder: 'Ex. Doe',\n        show: true,\n        rules: {\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_NUMERIC_PATTERN_WITH_SPACE,\n            maxLength: 250,\n        }\n    },\n    role: {\n        type: FORM_CONTROL_TYPES.SINGLE_SELECT,\n        onChange: (event) => { },\n        options: [],\n        value: \"\",\n        label: \"Role\",\n        placeholder: 'Select role',\n        show: true,\n        rules: {\n            required: true,\n        }\n    },\n    email: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Email\",\n        placeholder: 'Ex. <EMAIL>',\n        topGroupTitle: 'Contact Details',\n        topGroupTitleIcon: 'pi-phone',\n        severity: ICON_BUTTON_COLOR.WARNING,\n        show: true,\n        rules: {\n            required: true,\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,\n            maxLength: 250,\n        }\n    },\n    altemail: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Alternative Email\",\n        placeholder: 'Ex. <EMAIL>',\n        show: true,\n        rules: {\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.EMAIL_PATTERN,\n            maxLength: 250,\n        }\n    },\n    contactNo: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Contact No\",\n        placeholder: 'Ex. +1234567890',\n        show: true,\n        rules: {\n            required: true,\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.PHONE_PATTERN,\n            maxLength: 15,\n        }\n    },\n    addressLine1: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Address Line 1\",\n        placeholder: 'Ex. 123 Main Street',\n        topGroupTitle: 'Address Details',\n        topGroupTitleIcon: 'pi-map-marker',\n        severity: ICON_BUTTON_COLOR.WARNING,\n        show: true,\n        rules: {\n            maxLength: 250,\n        }\n    },\n    addressLine2: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Address Line 2\",\n        placeholder: 'Ex. Apt 4B',\n        show: true,\n        rules: {\n            maxLength: 250,\n        }\n    },\n    city: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"City\",\n        placeholder: 'Ex. New York',\n        show: true,\n        rules: {\n            maxLength: 250,\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,\n        }\n    },\n    country: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Country\",\n        placeholder: 'Ex. USA',\n        show: true,\n        rules: {\n            maxLength: 250,\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.ALPHA_PATTERN,\n        }\n    },\n    pincode: {\n        type: FORM_CONTROL_TYPES.TEXT,\n        value: \"\",\n        label: \"Pincode\",\n        placeholder: 'Ex. 123456',\n        show: true,\n        rules: {\n            pattern: FORM_FIELDS_CONSTANTS_VALUES.NUMERIC_PATTERN,\n            maxLength: 10,\n        }\n    },\n    isActive: {\n        type: FORM_CONTROL_TYPES.CHECKBOX,\n        value: true,\n        label: \"Active\",\n        show: true,\n    },\n    mappedWith: {\n        type: FORM_CONTROL_TYPES.SINGLE_SELECT,\n        onChange: (event) => { },\n        options: [],\n        value: \"\",\n        label: \"Mapped With\",\n        placeholder: 'Select mapping',\n        show: true,\n        rules: {\n            // Not required\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,4BAA4B,EAAEC,iBAAiB,QAAQ,qBAAqB;AAEzG,OAAO,MAAMC,qBAAqB,GAAG;EACjCC,IAAI,EAAE;IACFC,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,aAAa;IAC1BC,iBAAiB,EAAE,SAAS;IAC5BC,QAAQ,EAAET,iBAAiB,CAACU,OAAO;IACnCC,aAAa,EAAE,oBAAoB;IACnCC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE;;GAElB;EACDC,QAAQ,EAAE;IACNb,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,aAAa;IAC1BK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHC,QAAQ,EAAE,IAAI;MACdG,OAAO,EAAElB,4BAA4B,CAACmB,8BAA8B;MACpEC,SAAS,EAAE,CAAC;MACZJ,SAAS,EAAE;KACd;IACDK,OAAO,EAAE;GACZ;EACDC,QAAQ,EAAE;IACNlB,IAAI,EAAEL,kBAAkB,CAACwB,QAAQ;IACjCjB,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,iBAAiB;IAC9BK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE;;GAElB;EACDQ,SAAS,EAAE;IACPpB,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,UAAU;IACvBK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHC,QAAQ,EAAE,IAAI;MACdG,OAAO,EAAElB,4BAA4B,CAACyB,gCAAgC;MACtET,SAAS,EAAE;;GAElB;EACDU,QAAQ,EAAE;IACNtB,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,SAAS;IACtBK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHI,OAAO,EAAElB,4BAA4B,CAACyB,gCAAgC;MACtET,SAAS,EAAE;;GAElB;EACDW,IAAI,EAAE;IACFvB,IAAI,EAAEL,kBAAkB,CAAC6B,aAAa;IACtCC,QAAQ,EAAGC,KAAK,IAAI,CAAG,CAAC;IACxBC,OAAO,EAAE,EAAE;IACXzB,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,aAAa;IAC1BK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHC,QAAQ,EAAE;;GAEjB;EACDiB,KAAK,EAAE;IACH5B,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,sBAAsB;IACnCI,aAAa,EAAE,iBAAiB;IAChCH,iBAAiB,EAAE,UAAU;IAC7BC,QAAQ,EAAET,iBAAiB,CAACU,OAAO;IACnCE,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHC,QAAQ,EAAE,IAAI;MACdG,OAAO,EAAElB,4BAA4B,CAACiC,aAAa;MACnDjB,SAAS,EAAE;;GAElB;EACDkB,QAAQ,EAAE;IACN9B,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,0BAA0B;IACvCK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHI,OAAO,EAAElB,4BAA4B,CAACiC,aAAa;MACnDjB,SAAS,EAAE;;GAElB;EACDmB,SAAS,EAAE;IACP/B,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE,iBAAiB;IAC9BK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHC,QAAQ,EAAE,IAAI;MACdG,OAAO,EAAElB,4BAA4B,CAACoC,aAAa;MACnDpB,SAAS,EAAE;;GAElB;EACDqB,YAAY,EAAE;IACVjC,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,qBAAqB;IAClCI,aAAa,EAAE,iBAAiB;IAChCH,iBAAiB,EAAE,eAAe;IAClCC,QAAQ,EAAET,iBAAiB,CAACU,OAAO;IACnCE,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHE,SAAS,EAAE;;GAElB;EACDsB,YAAY,EAAE;IACVlC,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,YAAY;IACzBK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHE,SAAS,EAAE;;GAElB;EACDuB,IAAI,EAAE;IACFnC,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,MAAM;IACbC,WAAW,EAAE,cAAc;IAC3BK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHE,SAAS,EAAE,GAAG;MACdE,OAAO,EAAElB,4BAA4B,CAACwC;;GAE7C;EACDC,OAAO,EAAE;IACLrC,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,SAAS;IACtBK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHE,SAAS,EAAE,GAAG;MACdE,OAAO,EAAElB,4BAA4B,CAACwC;;GAE7C;EACDE,OAAO,EAAE;IACLtC,IAAI,EAAEL,kBAAkB,CAACM,IAAI;IAC7BC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,SAAS;IAChBC,WAAW,EAAE,YAAY;IACzBK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACHI,OAAO,EAAElB,4BAA4B,CAAC2C,eAAe;MACrD3B,SAAS,EAAE;;GAElB;EACD4B,QAAQ,EAAE;IACNxC,IAAI,EAAEL,kBAAkB,CAAC8C,QAAQ;IACjCvC,KAAK,EAAE,IAAI;IACXC,KAAK,EAAE,QAAQ;IACfM,IAAI,EAAE;GACT;EACDiC,UAAU,EAAE;IACR1C,IAAI,EAAEL,kBAAkB,CAAC6B,aAAa;IACtCC,QAAQ,EAAGC,KAAK,IAAI,CAAG,CAAC;IACxBC,OAAO,EAAE,EAAE;IACXzB,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,gBAAgB;IAC7BK,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE;MACH;IAAA;;CAGX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}