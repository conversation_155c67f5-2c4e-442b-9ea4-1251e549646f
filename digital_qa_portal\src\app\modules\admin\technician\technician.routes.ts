import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';

import { TechnicianListComponent } from './technician-list.component';

export default [
    {
        path: '',
        component: TechnicianListComponent,
        canActivate: [permissionGuard],
        data: { permission: "ADMIN_TECHNICIAN_MASTER" },
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;





