{"ast": null, "code": "import clamp from './clamp.js';\nimport inRange from './inRange.js';\nimport random from './random.js';\nexport default {\n  clamp,\n  inRange,\n  random\n};", "map": {"version": 3, "names": ["clamp", "inRange", "random"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/number.default.js"], "sourcesContent": ["import clamp from './clamp.js';\nimport inRange from './inRange.js';\nimport random from './random.js';\n\nexport default {\n  clamp, inRange, random\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;AAC9B,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,MAAM,MAAM,aAAa;AAEhC,eAAe;EACbF,KAAK;EAAEC,OAAO;EAAEC;AAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}