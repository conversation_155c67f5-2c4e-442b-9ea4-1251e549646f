{"ast": null, "code": "import defineProperty from './_defineProperty.js';\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\nexport default baseAssignValue;", "map": {"version": 3, "names": ["defineProperty", "baseAssignValue", "object", "key", "value"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_baseAssignValue.js"], "sourcesContent": ["import defineProperty from './_defineProperty.js';\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\n\nexport default baseAssignValue;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC3C,IAAID,GAAG,IAAI,WAAW,IAAIH,cAAc,EAAE;IACxCA,cAAc,CAACE,MAAM,EAAEC,GAAG,EAAE;MAC1B,cAAc,EAAE,IAAI;MACpB,YAAY,EAAE,IAAI;MAClB,OAAO,EAAEC,KAAK;MACd,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,MAAM;IACLF,MAAM,CAACC,GAAG,CAAC,GAAGC,KAAK;EACrB;AACF;AAEA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}