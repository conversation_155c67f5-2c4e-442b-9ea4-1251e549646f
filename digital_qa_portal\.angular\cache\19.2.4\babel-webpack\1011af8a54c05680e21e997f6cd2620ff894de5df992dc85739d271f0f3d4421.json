{"ast": null, "code": "import arrayMap from './_arrayMap.js';\n\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\nfunction baseValues(object, props) {\n  return arrayMap(props, function (key) {\n    return object[key];\n  });\n}\nexport default baseValues;", "map": {"version": 3, "names": ["arrayMap", "baseValues", "object", "props", "key"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_baseValues.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\n\n/**\n * The base implementation of `_.values` and `_.valuesIn` which creates an\n * array of `object` property values corresponding to the property names\n * of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the array of property values.\n */\nfunction baseValues(object, props) {\n  return arrayMap(props, function(key) {\n    return object[key];\n  });\n}\n\nexport default baseValues;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACjC,OAAOH,QAAQ,CAACG,KAAK,EAAE,UAASC,GAAG,EAAE;IACnC,OAAOF,MAAM,CAACE,GAAG,CAAC;EACpB,CAAC,CAAC;AACJ;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}