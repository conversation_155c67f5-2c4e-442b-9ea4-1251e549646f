{"ast": null, "code": "import isStrictComparable from './_isStrictComparable.js';\nimport keys from './keys.js';\n\n/**\n * Gets the property names, values, and compare flags of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the match data of `object`.\n */\nfunction getMatchData(object) {\n  var result = keys(object),\n    length = result.length;\n  while (length--) {\n    var key = result[length],\n      value = object[key];\n    result[length] = [key, value, isStrictComparable(value)];\n  }\n  return result;\n}\nexport default getMatchData;", "map": {"version": 3, "names": ["isStrictComparable", "keys", "getMatchData", "object", "result", "length", "key", "value"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_getMatchData.js"], "sourcesContent": ["import isStrictComparable from './_isStrictComparable.js';\nimport keys from './keys.js';\n\n/**\n * Gets the property names, values, and compare flags of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the match data of `object`.\n */\nfunction getMatchData(object) {\n  var result = keys(object),\n      length = result.length;\n\n  while (length--) {\n    var key = result[length],\n        value = object[key];\n\n    result[length] = [key, value, isStrictComparable(value)];\n  }\n  return result;\n}\n\nexport default getMatchData;\n"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,0BAA0B;AACzD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,MAAM,EAAE;EAC5B,IAAIC,MAAM,GAAGH,IAAI,CAACE,MAAM,CAAC;IACrBE,MAAM,GAAGD,MAAM,CAACC,MAAM;EAE1B,OAAOA,MAAM,EAAE,EAAE;IACf,IAAIC,GAAG,GAAGF,MAAM,CAACC,MAAM,CAAC;MACpBE,KAAK,GAAGJ,MAAM,CAACG,GAAG,CAAC;IAEvBF,MAAM,CAACC,MAAM,CAAC,GAAG,CAACC,GAAG,EAAEC,KAAK,EAAEP,kBAAkB,CAACO,KAAK,CAAC,CAAC;EAC1D;EACA,OAAOH,MAAM;AACf;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}