{"ast": null, "code": "function propertyRemove(name) {\n  return function () {\n    delete this[name];\n  };\n}\nfunction propertyConstant(name, value) {\n  return function () {\n    this[name] = value;\n  };\n}\nfunction propertyFunction(name, value) {\n  return function () {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];else this[name] = v;\n  };\n}\nexport default function (name, value) {\n  return arguments.length > 1 ? this.each((value == null ? propertyRemove : typeof value === \"function\" ? propertyFunction : propertyConstant)(name, value)) : this.node()[name];\n}", "map": {"version": 3, "names": ["propertyRemove", "name", "propertyConstant", "value", "propertyFunction", "v", "apply", "arguments", "length", "each", "node"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-selection/src/selection/property.js"], "sourcesContent": ["function propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\n\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\n\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null) delete this[name];\n    else this[name] = v;\n  };\n}\n\nexport default function(name, value) {\n  return arguments.length > 1\n      ? this.each((value == null\n          ? propertyRemove : typeof value === \"function\"\n          ? propertyFunction\n          : propertyConstant)(name, value))\n      : this.node()[name];\n}\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,IAAI,EAAE;EAC5B,OAAO,YAAW;IAChB,OAAO,IAAI,CAACA,IAAI,CAAC;EACnB,CAAC;AACH;AAEA,SAASC,gBAAgBA,CAACD,IAAI,EAAEE,KAAK,EAAE;EACrC,OAAO,YAAW;IAChB,IAAI,CAACF,IAAI,CAAC,GAAGE,KAAK;EACpB,CAAC;AACH;AAEA,SAASC,gBAAgBA,CAACH,IAAI,EAAEE,KAAK,EAAE;EACrC,OAAO,YAAW;IAChB,IAAIE,CAAC,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAIF,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI,CAACJ,IAAI,CAAC,CAAC,KAC5B,IAAI,CAACA,IAAI,CAAC,GAAGI,CAAC;EACrB,CAAC;AACH;AAEA,eAAe,UAASJ,IAAI,EAAEE,KAAK,EAAE;EACnC,OAAOI,SAAS,CAACC,MAAM,GAAG,CAAC,GACrB,IAAI,CAACC,IAAI,CAAC,CAACN,KAAK,IAAI,IAAI,GACpBH,cAAc,GAAG,OAAOG,KAAK,KAAK,UAAU,GAC5CC,gBAAgB,GAChBF,gBAAgB,EAAED,IAAI,EAAEE,KAAK,CAAC,CAAC,GACnC,IAAI,CAACO,IAAI,CAAC,CAAC,CAACT,IAAI,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}