{"ast": null, "code": "import interrupt from \"../interrupt.js\";\nexport default function (name) {\n  return this.each(function () {\n    interrupt(this, name);\n  });\n}", "map": {"version": 3, "names": ["interrupt", "name", "each"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-transition/src/selection/interrupt.js"], "sourcesContent": ["import interrupt from \"../interrupt.js\";\n\nexport default function(name) {\n  return this.each(function() {\n    interrupt(this, name);\n  });\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AAEvC,eAAe,UAASC,IAAI,EAAE;EAC5B,OAAO,IAAI,CAACC,IAAI,CAAC,YAAW;IAC1BF,SAAS,CAAC,IAAI,EAAEC,IAAI,CAAC;EACvB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}