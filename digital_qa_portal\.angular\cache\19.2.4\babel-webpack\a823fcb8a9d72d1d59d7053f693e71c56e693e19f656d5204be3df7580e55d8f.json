{"ast": null, "code": "import getMapData from './_getMapData.js';\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n    size = data.size;\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\nexport default mapCacheSet;", "map": {"version": 3, "names": ["getMapData", "mapCacheSet", "key", "value", "data", "size", "set"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_mapCacheSet.js"], "sourcesContent": ["import getMapData from './_getMapData.js';\n\n/**\n * Sets the map `key` to `value`.\n *\n * @private\n * @name set\n * @memberOf MapCache\n * @param {string} key The key of the value to set.\n * @param {*} value The value to set.\n * @returns {Object} Returns the map cache instance.\n */\nfunction mapCacheSet(key, value) {\n  var data = getMapData(this, key),\n      size = data.size;\n\n  data.set(key, value);\n  this.size += data.size == size ? 0 : 1;\n  return this;\n}\n\nexport default mapCacheSet;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC/B,IAAIC,IAAI,GAAGJ,UAAU,CAAC,IAAI,EAAEE,GAAG,CAAC;IAC5BG,IAAI,GAAGD,IAAI,CAACC,IAAI;EAEpBD,IAAI,CAACE,GAAG,CAACJ,GAAG,EAAEC,KAAK,CAAC;EACpB,IAAI,CAACE,IAAI,IAAID,IAAI,CAACC,IAAI,IAAIA,IAAI,GAAG,CAAC,GAAG,CAAC;EACtC,OAAO,IAAI;AACb;AAEA,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}