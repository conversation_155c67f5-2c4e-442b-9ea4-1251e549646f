{"ast": null, "code": "/**\n * The base implementation of `_.hasIn` without support for deep paths.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {Array|string} key The key to check.\n * @returns {boolean} Returns `true` if `key` exists, else `false`.\n */\nfunction baseHasIn(object, key) {\n  return object != null && key in Object(object);\n}\nexport default baseHasIn;", "map": {"version": 3, "names": ["baseHasIn", "object", "key", "Object"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_baseHasIn.js"], "sourcesContent": ["/**\n * The base implementation of `_.hasIn` without support for deep paths.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {Array|string} key The key to check.\n * @returns {boolean} Returns `true` if `key` exists, else `false`.\n */\nfunction baseHasIn(object, key) {\n  return object != null && key in Object(object);\n}\n\nexport default baseHasIn;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC9B,OAAOD,MAAM,IAAI,IAAI,IAAIC,GAAG,IAAIC,MAAM,CAACF,MAAM,CAAC;AAChD;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}