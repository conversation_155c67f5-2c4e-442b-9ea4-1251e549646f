{"ast": null, "code": "/** Used to match template delimiters. */\nvar reEvaluate = /<%([\\s\\S]+?)%>/g;\nexport default reEvaluate;", "map": {"version": 3, "names": ["reEvaluate"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_reEvaluate.js"], "sourcesContent": ["/** Used to match template delimiters. */\nvar reEvaluate = /<%([\\s\\S]+?)%>/g;\n\nexport default reEvaluate;\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAG,iBAAiB;AAElC,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}