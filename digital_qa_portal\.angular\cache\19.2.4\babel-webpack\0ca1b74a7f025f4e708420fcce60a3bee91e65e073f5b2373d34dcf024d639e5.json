{"ast": null, "code": "export const epsilon = 1.1102230246251565e-16;\nexport const splitter = 134217729;\nexport const resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nexport function sum(elen, e, flen, f, h) {\n  let Q, Qnew, hh, bvirt;\n  let enow = e[0];\n  let fnow = f[0];\n  let eindex = 0;\n  let findex = 0;\n  if (fnow > enow === fnow > -enow) {\n    Q = enow;\n    enow = e[++eindex];\n  } else {\n    Q = fnow;\n    fnow = f[++findex];\n  }\n  let hindex = 0;\n  if (eindex < elen && findex < flen) {\n    if (fnow > enow === fnow > -enow) {\n      Qnew = enow + Q;\n      hh = Q - (Qnew - enow);\n      enow = e[++eindex];\n    } else {\n      Qnew = fnow + Q;\n      hh = Q - (Qnew - fnow);\n      fnow = f[++findex];\n    }\n    Q = Qnew;\n    if (hh !== 0) {\n      h[hindex++] = hh;\n    }\n    while (eindex < elen && findex < flen) {\n      if (fnow > enow === fnow > -enow) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n      } else {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n      }\n      Q = Qnew;\n      if (hh !== 0) {\n        h[hindex++] = hh;\n      }\n    }\n  }\n  while (eindex < elen) {\n    Qnew = Q + enow;\n    bvirt = Qnew - Q;\n    hh = Q - (Qnew - bvirt) + (enow - bvirt);\n    enow = e[++eindex];\n    Q = Qnew;\n    if (hh !== 0) {\n      h[hindex++] = hh;\n    }\n  }\n  while (findex < flen) {\n    Qnew = Q + fnow;\n    bvirt = Qnew - Q;\n    hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n    fnow = f[++findex];\n    Q = Qnew;\n    if (hh !== 0) {\n      h[hindex++] = hh;\n    }\n  }\n  if (Q !== 0 || hindex === 0) {\n    h[hindex++] = Q;\n  }\n  return hindex;\n}\nexport function sum_three(alen, a, blen, b, clen, c, tmp, out) {\n  return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nexport function scale(elen, e, b, h) {\n  let Q, sum, hh, product1, product0;\n  let bvirt, c, ahi, alo, bhi, blo;\n  c = splitter * b;\n  bhi = c - (c - b);\n  blo = b - bhi;\n  let enow = e[0];\n  Q = enow * b;\n  c = splitter * enow;\n  ahi = c - (c - enow);\n  alo = enow - ahi;\n  hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n  let hindex = 0;\n  if (hh !== 0) {\n    h[hindex++] = hh;\n  }\n  for (let i = 1; i < elen; i++) {\n    enow = e[i];\n    product1 = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n    sum = Q + product0;\n    bvirt = sum - Q;\n    hh = Q - (sum - bvirt) + (product0 - bvirt);\n    if (hh !== 0) {\n      h[hindex++] = hh;\n    }\n    Q = product1 + sum;\n    hh = sum - (Q - product1);\n    if (hh !== 0) {\n      h[hindex++] = hh;\n    }\n  }\n  if (Q !== 0 || hindex === 0) {\n    h[hindex++] = Q;\n  }\n  return hindex;\n}\nexport function negate(elen, e) {\n  for (let i = 0; i < elen; i++) e[i] = -e[i];\n  return elen;\n}\nexport function estimate(elen, e) {\n  let Q = e[0];\n  for (let i = 1; i < elen; i++) Q += e[i];\n  return Q;\n}\nexport function vec(n) {\n  return new Float64Array(n);\n}", "map": {"version": 3, "names": ["epsilon", "splitter", "resulterrbound", "sum", "elen", "e", "flen", "f", "h", "Q", "Qnew", "hh", "bvirt", "enow", "fnow", "eindex", "findex", "hindex", "sum_three", "alen", "a", "blen", "b", "clen", "c", "tmp", "out", "scale", "product1", "product0", "ahi", "alo", "bhi", "blo", "i", "negate", "estimate", "vec", "n", "Float64Array"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/robust-predicates/esm/util.js"], "sourcesContent": ["export const epsilon = 1.1102230246251565e-16;\nexport const splitter = 134217729;\nexport const resulterrbound = (3 + 8 * epsilon) * epsilon;\n\n// fast_expansion_sum_zeroelim routine from oritinal code\nexport function sum(elen, e, flen, f, h) {\n    let Q, Qnew, hh, bvirt;\n    let enow = e[0];\n    let fnow = f[0];\n    let eindex = 0;\n    let findex = 0;\n    if ((fnow > enow) === (fnow > -enow)) {\n        Q = enow;\n        enow = e[++eindex];\n    } else {\n        Q = fnow;\n        fnow = f[++findex];\n    }\n    let hindex = 0;\n    if (eindex < elen && findex < flen) {\n        if ((fnow > enow) === (fnow > -enow)) {\n            Qnew = enow + Q;\n            hh = Q - (Qnew - enow);\n            enow = e[++eindex];\n        } else {\n            Qnew = fnow + Q;\n            hh = Q - (Qnew - fnow);\n            fnow = f[++findex];\n        }\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        while (eindex < elen && findex < flen) {\n            if ((fnow > enow) === (fnow > -enow)) {\n                Qnew = Q + enow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (enow - bvirt);\n                enow = e[++eindex];\n            } else {\n                Qnew = Q + fnow;\n                bvirt = Qnew - Q;\n                hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n                fnow = f[++findex];\n            }\n            Q = Qnew;\n            if (hh !== 0) {\n                h[hindex++] = hh;\n            }\n        }\n    }\n    while (eindex < elen) {\n        Qnew = Q + enow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (enow - bvirt);\n        enow = e[++eindex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    while (findex < flen) {\n        Qnew = Q + fnow;\n        bvirt = Qnew - Q;\n        hh = Q - (Qnew - bvirt) + (fnow - bvirt);\n        fnow = f[++findex];\n        Q = Qnew;\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function sum_three(alen, a, blen, b, clen, c, tmp, out) {\n    return sum(sum(alen, a, blen, b, tmp), tmp, clen, c, out);\n}\n\n// scale_expansion_zeroelim routine from oritinal code\nexport function scale(elen, e, b, h) {\n    let Q, sum, hh, product1, product0;\n    let bvirt, c, ahi, alo, bhi, blo;\n\n    c = splitter * b;\n    bhi = c - (c - b);\n    blo = b - bhi;\n    let enow = e[0];\n    Q = enow * b;\n    c = splitter * enow;\n    ahi = c - (c - enow);\n    alo = enow - ahi;\n    hh = alo * blo - (Q - ahi * bhi - alo * bhi - ahi * blo);\n    let hindex = 0;\n    if (hh !== 0) {\n        h[hindex++] = hh;\n    }\n    for (let i = 1; i < elen; i++) {\n        enow = e[i];\n        product1 = enow * b;\n        c = splitter * enow;\n        ahi = c - (c - enow);\n        alo = enow - ahi;\n        product0 = alo * blo - (product1 - ahi * bhi - alo * bhi - ahi * blo);\n        sum = Q + product0;\n        bvirt = sum - Q;\n        hh = Q - (sum - bvirt) + (product0 - bvirt);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n        Q = product1 + sum;\n        hh = sum - (Q - product1);\n        if (hh !== 0) {\n            h[hindex++] = hh;\n        }\n    }\n    if (Q !== 0 || hindex === 0) {\n        h[hindex++] = Q;\n    }\n    return hindex;\n}\n\nexport function negate(elen, e) {\n    for (let i = 0; i < elen; i++) e[i] = -e[i];\n    return elen;\n}\n\nexport function estimate(elen, e) {\n    let Q = e[0];\n    for (let i = 1; i < elen; i++) Q += e[i];\n    return Q;\n}\n\nexport function vec(n) {\n    return new Float64Array(n);\n}\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,sBAAsB;AAC7C,OAAO,MAAMC,QAAQ,GAAG,SAAS;AACjC,OAAO,MAAMC,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGF,OAAO,IAAIA,OAAO;;AAEzD;AACA,OAAO,SAASG,GAAGA,CAACC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACrC,IAAIC,CAAC,EAAEC,IAAI,EAAEC,EAAE,EAAEC,KAAK;EACtB,IAAIC,IAAI,GAAGR,CAAC,CAAC,CAAC,CAAC;EACf,IAAIS,IAAI,GAAGP,CAAC,CAAC,CAAC,CAAC;EACf,IAAIQ,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;EACd,IAAKF,IAAI,GAAGD,IAAI,KAAOC,IAAI,GAAG,CAACD,IAAK,EAAE;IAClCJ,CAAC,GAAGI,IAAI;IACRA,IAAI,GAAGR,CAAC,CAAC,EAAEU,MAAM,CAAC;EACtB,CAAC,MAAM;IACHN,CAAC,GAAGK,IAAI;IACRA,IAAI,GAAGP,CAAC,CAAC,EAAES,MAAM,CAAC;EACtB;EACA,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIF,MAAM,GAAGX,IAAI,IAAIY,MAAM,GAAGV,IAAI,EAAE;IAChC,IAAKQ,IAAI,GAAGD,IAAI,KAAOC,IAAI,GAAG,CAACD,IAAK,EAAE;MAClCH,IAAI,GAAGG,IAAI,GAAGJ,CAAC;MACfE,EAAE,GAAGF,CAAC,IAAIC,IAAI,GAAGG,IAAI,CAAC;MACtBA,IAAI,GAAGR,CAAC,CAAC,EAAEU,MAAM,CAAC;IACtB,CAAC,MAAM;MACHL,IAAI,GAAGI,IAAI,GAAGL,CAAC;MACfE,EAAE,GAAGF,CAAC,IAAIC,IAAI,GAAGI,IAAI,CAAC;MACtBA,IAAI,GAAGP,CAAC,CAAC,EAAES,MAAM,CAAC;IACtB;IACAP,CAAC,GAAGC,IAAI;IACR,IAAIC,EAAE,KAAK,CAAC,EAAE;MACVH,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGN,EAAE;IACpB;IACA,OAAOI,MAAM,GAAGX,IAAI,IAAIY,MAAM,GAAGV,IAAI,EAAE;MACnC,IAAKQ,IAAI,GAAGD,IAAI,KAAOC,IAAI,GAAG,CAACD,IAAK,EAAE;QAClCH,IAAI,GAAGD,CAAC,GAAGI,IAAI;QACfD,KAAK,GAAGF,IAAI,GAAGD,CAAC;QAChBE,EAAE,GAAGF,CAAC,IAAIC,IAAI,GAAGE,KAAK,CAAC,IAAIC,IAAI,GAAGD,KAAK,CAAC;QACxCC,IAAI,GAAGR,CAAC,CAAC,EAAEU,MAAM,CAAC;MACtB,CAAC,MAAM;QACHL,IAAI,GAAGD,CAAC,GAAGK,IAAI;QACfF,KAAK,GAAGF,IAAI,GAAGD,CAAC;QAChBE,EAAE,GAAGF,CAAC,IAAIC,IAAI,GAAGE,KAAK,CAAC,IAAIE,IAAI,GAAGF,KAAK,CAAC;QACxCE,IAAI,GAAGP,CAAC,CAAC,EAAES,MAAM,CAAC;MACtB;MACAP,CAAC,GAAGC,IAAI;MACR,IAAIC,EAAE,KAAK,CAAC,EAAE;QACVH,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGN,EAAE;MACpB;IACJ;EACJ;EACA,OAAOI,MAAM,GAAGX,IAAI,EAAE;IAClBM,IAAI,GAAGD,CAAC,GAAGI,IAAI;IACfD,KAAK,GAAGF,IAAI,GAAGD,CAAC;IAChBE,EAAE,GAAGF,CAAC,IAAIC,IAAI,GAAGE,KAAK,CAAC,IAAIC,IAAI,GAAGD,KAAK,CAAC;IACxCC,IAAI,GAAGR,CAAC,CAAC,EAAEU,MAAM,CAAC;IAClBN,CAAC,GAAGC,IAAI;IACR,IAAIC,EAAE,KAAK,CAAC,EAAE;MACVH,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGN,EAAE;IACpB;EACJ;EACA,OAAOK,MAAM,GAAGV,IAAI,EAAE;IAClBI,IAAI,GAAGD,CAAC,GAAGK,IAAI;IACfF,KAAK,GAAGF,IAAI,GAAGD,CAAC;IAChBE,EAAE,GAAGF,CAAC,IAAIC,IAAI,GAAGE,KAAK,CAAC,IAAIE,IAAI,GAAGF,KAAK,CAAC;IACxCE,IAAI,GAAGP,CAAC,CAAC,EAAES,MAAM,CAAC;IAClBP,CAAC,GAAGC,IAAI;IACR,IAAIC,EAAE,KAAK,CAAC,EAAE;MACVH,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGN,EAAE;IACpB;EACJ;EACA,IAAIF,CAAC,KAAK,CAAC,IAAIQ,MAAM,KAAK,CAAC,EAAE;IACzBT,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGR,CAAC;EACnB;EACA,OAAOQ,MAAM;AACjB;AAEA,OAAO,SAASC,SAASA,CAACC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC3D,OAAOvB,GAAG,CAACA,GAAG,CAACgB,IAAI,EAAEC,CAAC,EAAEC,IAAI,EAAEC,CAAC,EAAEG,GAAG,CAAC,EAAEA,GAAG,EAAEF,IAAI,EAAEC,CAAC,EAAEE,GAAG,CAAC;AAC7D;;AAEA;AACA,OAAO,SAASC,KAAKA,CAACvB,IAAI,EAAEC,CAAC,EAAEiB,CAAC,EAAEd,CAAC,EAAE;EACjC,IAAIC,CAAC,EAAEN,GAAG,EAAEQ,EAAE,EAAEiB,QAAQ,EAAEC,QAAQ;EAClC,IAAIjB,KAAK,EAAEY,CAAC,EAAEM,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG;EAEhCT,CAAC,GAAGvB,QAAQ,GAAGqB,CAAC;EAChBU,GAAG,GAAGR,CAAC,IAAIA,CAAC,GAAGF,CAAC,CAAC;EACjBW,GAAG,GAAGX,CAAC,GAAGU,GAAG;EACb,IAAInB,IAAI,GAAGR,CAAC,CAAC,CAAC,CAAC;EACfI,CAAC,GAAGI,IAAI,GAAGS,CAAC;EACZE,CAAC,GAAGvB,QAAQ,GAAGY,IAAI;EACnBiB,GAAG,GAAGN,CAAC,IAAIA,CAAC,GAAGX,IAAI,CAAC;EACpBkB,GAAG,GAAGlB,IAAI,GAAGiB,GAAG;EAChBnB,EAAE,GAAGoB,GAAG,GAAGE,GAAG,IAAIxB,CAAC,GAAGqB,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;EACxD,IAAIhB,MAAM,GAAG,CAAC;EACd,IAAIN,EAAE,KAAK,CAAC,EAAE;IACVH,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGN,EAAE;EACpB;EACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,IAAI,EAAE8B,CAAC,EAAE,EAAE;IAC3BrB,IAAI,GAAGR,CAAC,CAAC6B,CAAC,CAAC;IACXN,QAAQ,GAAGf,IAAI,GAAGS,CAAC;IACnBE,CAAC,GAAGvB,QAAQ,GAAGY,IAAI;IACnBiB,GAAG,GAAGN,CAAC,IAAIA,CAAC,GAAGX,IAAI,CAAC;IACpBkB,GAAG,GAAGlB,IAAI,GAAGiB,GAAG;IAChBD,QAAQ,GAAGE,GAAG,GAAGE,GAAG,IAAIL,QAAQ,GAAGE,GAAG,GAAGE,GAAG,GAAGD,GAAG,GAAGC,GAAG,GAAGF,GAAG,GAAGG,GAAG,CAAC;IACrE9B,GAAG,GAAGM,CAAC,GAAGoB,QAAQ;IAClBjB,KAAK,GAAGT,GAAG,GAAGM,CAAC;IACfE,EAAE,GAAGF,CAAC,IAAIN,GAAG,GAAGS,KAAK,CAAC,IAAIiB,QAAQ,GAAGjB,KAAK,CAAC;IAC3C,IAAID,EAAE,KAAK,CAAC,EAAE;MACVH,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGN,EAAE;IACpB;IACAF,CAAC,GAAGmB,QAAQ,GAAGzB,GAAG;IAClBQ,EAAE,GAAGR,GAAG,IAAIM,CAAC,GAAGmB,QAAQ,CAAC;IACzB,IAAIjB,EAAE,KAAK,CAAC,EAAE;MACVH,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGN,EAAE;IACpB;EACJ;EACA,IAAIF,CAAC,KAAK,CAAC,IAAIQ,MAAM,KAAK,CAAC,EAAE;IACzBT,CAAC,CAACS,MAAM,EAAE,CAAC,GAAGR,CAAC;EACnB;EACA,OAAOQ,MAAM;AACjB;AAEA,OAAO,SAASkB,MAAMA,CAAC/B,IAAI,EAAEC,CAAC,EAAE;EAC5B,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,IAAI,EAAE8B,CAAC,EAAE,EAAE7B,CAAC,CAAC6B,CAAC,CAAC,GAAG,CAAC7B,CAAC,CAAC6B,CAAC,CAAC;EAC3C,OAAO9B,IAAI;AACf;AAEA,OAAO,SAASgC,QAAQA,CAAChC,IAAI,EAAEC,CAAC,EAAE;EAC9B,IAAII,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;EACZ,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,IAAI,EAAE8B,CAAC,EAAE,EAAEzB,CAAC,IAAIJ,CAAC,CAAC6B,CAAC,CAAC;EACxC,OAAOzB,CAAC;AACZ;AAEA,OAAO,SAAS4B,GAAGA,CAACC,CAAC,EAAE;EACnB,OAAO,IAAIC,YAAY,CAACD,CAAC,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}