{"ast": null, "code": "import { NgClass } from '@angular/common';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fuse/components/navigation/navigation.service\";\nexport class FuseHorizontalNavigationDividerItemComponent {\n  /**\n   * Constructor\n   */\n  constructor(_changeDetectorRef, _fuseNavigationService) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._fuseNavigationService = _fuseNavigationService;\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Get the parent navigation component\n    this._fuseHorizontalNavigationComponent = this._fuseNavigationService.getComponent(this.name);\n    // Subscribe to onRefreshed on the navigation component\n    this._fuseHorizontalNavigationComponent.onRefreshed.pipe(takeUntil(this._unsubscribeAll)).subscribe(() => {\n      // Mark for check\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  static {\n    this.ɵfac = function FuseHorizontalNavigationDividerItemComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || FuseHorizontalNavigationDividerItemComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FuseNavigationService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FuseHorizontalNavigationDividerItemComponent,\n      selectors: [[\"fuse-horizontal-navigation-divider-item\"]],\n      inputs: {\n        item: \"item\",\n        name: \"name\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[1, \"fuse-horizontal-navigation-item-wrapper\", \"divider\", 3, \"ngClass\"]],\n      template: function FuseHorizontalNavigationDividerItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", ctx.item.classes == null ? null : ctx.item.classes.wrapper);\n        }\n      },\n      dependencies: [NgClass],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}", "map": {"version": 3, "names": ["Ng<PERSON><PERSON>", "Subject", "takeUntil", "FuseHorizontalNavigationDividerItemComponent", "constructor", "_changeDetectorRef", "_fuseNavigationService", "_unsubscribeAll", "ngOnInit", "_fuseHorizontalNavigationComponent", "getComponent", "name", "onRefreshed", "pipe", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "next", "complete", "i0", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "FuseNavigationService", "selectors", "inputs", "item", "decls", "vars", "consts", "template", "FuseHorizontalNavigationDividerItemComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵproperty", "classes", "wrapper", "encapsulation", "changeDetection"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\@fuse\\components\\navigation\\horizontal\\components\\divider\\divider.component.ts", "D:\\portals\\madhura\\digital_qa_portal\\src\\@fuse\\components\\navigation\\horizontal\\components\\divider\\divider.component.html"], "sourcesContent": ["import { NgClass } from '@angular/common';\r\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';\r\nimport { FuseHorizontalNavigationComponent } from '@fuse/components/navigation/horizontal/horizontal.component';\r\nimport { FuseNavigationService } from '@fuse/components/navigation/navigation.service';\r\nimport { FuseNavigationItem } from '@fuse/components/navigation/navigation.types';\r\nimport { Subject, takeUntil } from 'rxjs';\r\n\r\n@Component({\r\n    selector       : 'fuse-horizontal-navigation-divider-item',\r\n    templateUrl    : './divider.component.html',\r\n    changeDetection: ChangeDetectionStrategy.OnPush,\r\n    standalone     : true,\r\n    imports        : [NgClass],\r\n})\r\nexport class FuseHorizontalNavigationDividerItemComponent implements OnInit, OnDestroy\r\n{\r\n    @Input() item: FuseNavigationItem;\r\n    @Input() name: string;\r\n\r\n    private _fuseHorizontalNavigationComponent: FuseHorizontalNavigationComponent;\r\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\r\n\r\n    /**\r\n     * Constructor\r\n     */\r\n    constructor(\r\n        private _changeDetectorRef: ChangeDetectorRef,\r\n        private _fuseNavigationService: FuseNavigationService,\r\n    )\r\n    {\r\n    }\r\n\r\n    // -----------------------------------------------------------------------------------------------------\r\n    // @ Lifecycle hooks\r\n    // -----------------------------------------------------------------------------------------------------\r\n\r\n    /**\r\n     * On init\r\n     */\r\n    ngOnInit(): void\r\n    {\r\n        // Get the parent navigation component\r\n        this._fuseHorizontalNavigationComponent = this._fuseNavigationService.getComponent(this.name);\r\n\r\n        // Subscribe to onRefreshed on the navigation component\r\n        this._fuseHorizontalNavigationComponent.onRefreshed.pipe(\r\n            takeUntil(this._unsubscribeAll),\r\n        ).subscribe(() =>\r\n        {\r\n            // Mark for check\r\n            this._changeDetectorRef.markForCheck();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * On destroy\r\n     */\r\n    ngOnDestroy(): void\r\n    {\r\n        // Unsubscribe from all subscriptions\r\n        this._unsubscribeAll.next(null);\r\n        this._unsubscribeAll.complete();\r\n    }\r\n}\r\n", "<!-- Divider -->\r\n<div\r\n    class=\"fuse-horizontal-navigation-item-wrapper divider\"\r\n    [ngClass]=\"item.classes?.wrapper\"></div>\r\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AAKzC,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;AASzC,OAAM,MAAOC,4CAA4C;EAQrD;;;EAGAC,YACYC,kBAAqC,EACrCC,sBAA6C;IAD7C,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IAP1B,KAAAC,eAAe,GAAiB,IAAIN,OAAO,EAAO;EAU1D;EAEA;EACA;EACA;EAEA;;;EAGAO,QAAQA,CAAA;IAEJ;IACA,IAAI,CAACC,kCAAkC,GAAG,IAAI,CAACH,sBAAsB,CAACI,YAAY,CAAC,IAAI,CAACC,IAAI,CAAC;IAE7F;IACA,IAAI,CAACF,kCAAkC,CAACG,WAAW,CAACC,IAAI,CACpDX,SAAS,CAAC,IAAI,CAACK,eAAe,CAAC,CAClC,CAACO,SAAS,CAAC,MAAK;MAEb;MACA,IAAI,CAACT,kBAAkB,CAACU,YAAY,EAAE;IAC1C,CAAC,CAAC;EACN;EAEA;;;EAGAC,WAAWA,CAAA;IAEP;IACA,IAAI,CAACT,eAAe,CAACU,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACV,eAAe,CAACW,QAAQ,EAAE;EACnC;;;uCAhDSf,4CAA4C,EAAAgB,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,iBAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAE,EAAA,CAAAC,qBAAA;IAAA;EAAA;;;YAA5CpB,4CAA4C;MAAAqB,SAAA;MAAAC,MAAA;QAAAC,IAAA;QAAAf,IAAA;MAAA;MAAAgB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sDAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbzDb,EAAA,CAAAe,SAAA,aAE4C;;;UAAxCf,EAAA,CAAAgB,UAAA,YAAAF,GAAA,CAAAP,IAAA,CAAAU,OAAA,kBAAAH,GAAA,CAAAP,IAAA,CAAAU,OAAA,CAAAC,OAAA,CAAiC;;;qBDSfrC,OAAO;MAAAsC,aAAA;MAAAC,eAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}