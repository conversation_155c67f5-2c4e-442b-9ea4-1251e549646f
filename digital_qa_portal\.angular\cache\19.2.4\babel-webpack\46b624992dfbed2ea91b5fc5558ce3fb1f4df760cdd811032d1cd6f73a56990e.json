{"ast": null, "code": "function styleInterpolate(name, i, priority) {\n  return function (t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\nexport default function (name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error();\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}", "map": {"version": 3, "names": ["styleInterpolate", "name", "i", "priority", "t", "style", "setProperty", "call", "styleTween", "value", "i0", "tween", "apply", "arguments", "_value", "key", "length", "Error"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-transition/src/transition/styleTween.js"], "sourcesContent": ["function styleInterpolate(name, i, priority) {\n  return function(t) {\n    this.style.setProperty(name, i.call(this, t), priority);\n  };\n}\n\nfunction styleTween(name, value, priority) {\n  var t, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t = (i0 = i) && styleInterpolate(name, i, priority);\n    return t;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value, priority) {\n  var key = \"style.\" + (name += \"\");\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  return this.tween(key, styleTween(name, value, priority == null ? \"\" : priority));\n}\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,IAAI,EAAEC,CAAC,EAAEC,QAAQ,EAAE;EAC3C,OAAO,UAASC,CAAC,EAAE;IACjB,IAAI,CAACC,KAAK,CAACC,WAAW,CAACL,IAAI,EAAEC,CAAC,CAACK,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC,EAAED,QAAQ,CAAC;EACzD,CAAC;AACH;AAEA,SAASK,UAAUA,CAACP,IAAI,EAAEQ,KAAK,EAAEN,QAAQ,EAAE;EACzC,IAAIC,CAAC,EAAEM,EAAE;EACT,SAASC,KAAKA,CAAA,EAAG;IACf,IAAIT,CAAC,GAAGO,KAAK,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAIX,CAAC,KAAKQ,EAAE,EAAEN,CAAC,GAAG,CAACM,EAAE,GAAGR,CAAC,KAAKF,gBAAgB,CAACC,IAAI,EAAEC,CAAC,EAAEC,QAAQ,CAAC;IACjE,OAAOC,CAAC;EACV;EACAO,KAAK,CAACG,MAAM,GAAGL,KAAK;EACpB,OAAOE,KAAK;AACd;AAEA,eAAe,UAASV,IAAI,EAAEQ,KAAK,EAAEN,QAAQ,EAAE;EAC7C,IAAIY,GAAG,GAAG,QAAQ,IAAId,IAAI,IAAI,EAAE,CAAC;EACjC,IAAIY,SAAS,CAACG,MAAM,GAAG,CAAC,EAAE,OAAO,CAACD,GAAG,GAAG,IAAI,CAACJ,KAAK,CAACI,GAAG,CAAC,KAAKA,GAAG,CAACD,MAAM;EACtE,IAAIL,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,CAACE,KAAK,CAACI,GAAG,EAAE,IAAI,CAAC;EAC/C,IAAI,OAAON,KAAK,KAAK,UAAU,EAAE,MAAM,IAAIQ,KAAK,CAAD,CAAC;EAChD,OAAO,IAAI,CAACN,KAAK,CAACI,GAAG,EAAEP,UAAU,CAACP,IAAI,EAAEQ,KAAK,EAAEN,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAGA,QAAQ,CAAC,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}