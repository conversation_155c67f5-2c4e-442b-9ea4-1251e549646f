{"ast": null, "code": "export default function ZoomEvent(type, {\n  sourceEvent,\n  target,\n  transform,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {\n      value: type,\n      enumerable: true,\n      configurable: true\n    },\n    sourceEvent: {\n      value: sourceEvent,\n      enumerable: true,\n      configurable: true\n    },\n    target: {\n      value: target,\n      enumerable: true,\n      configurable: true\n    },\n    transform: {\n      value: transform,\n      enumerable: true,\n      configurable: true\n    },\n    _: {\n      value: dispatch\n    }\n  });\n}", "map": {"version": 3, "names": ["ZoomEvent", "type", "sourceEvent", "target", "transform", "dispatch", "Object", "defineProperties", "value", "enumerable", "configurable", "_"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-zoom/src/event.js"], "sourcesContent": ["export default function ZoomEvent(type, {\n  sourceEvent,\n  target,\n  transform,\n  dispatch\n}) {\n  Object.defineProperties(this, {\n    type: {value: type, enumerable: true, configurable: true},\n    sourceEvent: {value: sourceEvent, enumerable: true, configurable: true},\n    target: {value: target, enumerable: true, configurable: true},\n    transform: {value: transform, enumerable: true, configurable: true},\n    _: {value: dispatch}\n  });\n}\n"], "mappings": "AAAA,eAAe,SAASA,SAASA,CAACC,IAAI,EAAE;EACtCC,WAAW;EACXC,MAAM;EACNC,SAAS;EACTC;AACF,CAAC,EAAE;EACDC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;IAC5BN,IAAI,EAAE;MAACO,KAAK,EAAEP,IAAI;MAAEQ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACzDR,WAAW,EAAE;MAACM,KAAK,EAAEN,WAAW;MAAEO,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACvEP,MAAM,EAAE;MAACK,KAAK,EAAEL,MAAM;MAAEM,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IAC7DN,SAAS,EAAE;MAACI,KAAK,EAAEJ,SAAS;MAAEK,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAI,CAAC;IACnEC,CAAC,EAAE;MAACH,KAAK,EAAEH;IAAQ;EACrB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}