{"ast": null, "code": "import { permissionGuard } from 'app/core/auth/guards/permission.guard';\nimport { TechnicianListComponent } from './technician-list.component';\nexport default [{\n  path: '',\n  component: TechnicianListComponent,\n  canActivate: [permissionGuard],\n  data: {\n    permission: \"ADMIN_TECHNICIAN_MASTER\"\n  }\n}, {\n  path: 'not-found',\n  loadChildren: () => import('app/modules/page-not-found/page-not-found.routes')\n}, {\n  path: '**',\n  pathMatch: 'full',\n  redirectTo: 'not-found'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "TechnicianListComponent", "path", "component", "canActivate", "data", "permission", "loadChildren", "pathMatch", "redirectTo"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\app\\modules\\admin\\technician\\technician.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { permissionGuard } from 'app/core/auth/guards/permission.guard';\n\nimport { TechnicianListComponent } from './technician-list.component';\n\nexport default [\n    {\n        path: '',\n        component: TechnicianListComponent,\n        canActivate: [permissionGuard],\n        data: { permission: \"ADMIN_TECHNICIAN_MASTER\" },\n    },\n    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },\n    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },\n] as Routes;\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,uCAAuC;AAEvE,SAASC,uBAAuB,QAAQ,6BAA6B;AAErE,eAAe,CACX;EACIC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEF,uBAAuB;EAClCG,WAAW,EAAE,CAACJ,eAAe,CAAC;EAC9BK,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAyB;CAChD,EACD;EAAEJ,IAAI,EAAE,WAAW;EAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD;AAAC,CAAE,EACrG;EAAEL,IAAI,EAAE,IAAI;EAAEM,SAAS,EAAE,MAAM;EAAEC,UAAU,EAAE;AAAW,CAAE,CACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}