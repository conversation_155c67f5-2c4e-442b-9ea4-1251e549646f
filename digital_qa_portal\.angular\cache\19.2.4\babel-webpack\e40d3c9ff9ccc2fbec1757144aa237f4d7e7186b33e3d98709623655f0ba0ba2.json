{"ast": null, "code": "import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseUnary from './_baseUnary.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates a function like `_.over`.\n *\n * @private\n * @param {Function} arrayFunc The function to iterate over iteratees.\n * @returns {Function} Returns the new over function.\n */\nfunction createOver(arrayFunc) {\n  return flatRest(function (iteratees) {\n    iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n    return baseRest(function (args) {\n      var thisArg = this;\n      return arrayFunc(iteratees, function (iteratee) {\n        return apply(iteratee, thisArg, args);\n      });\n    });\n  });\n}\nexport default createOver;", "map": {"version": 3, "names": ["apply", "arrayMap", "baseIteratee", "baseRest", "baseUnary", "flatRest", "createOver", "arrayFunc", "iteratees", "args", "thisArg", "iteratee"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_createOver.js"], "sourcesContent": ["import apply from './_apply.js';\nimport arrayMap from './_arrayMap.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport baseUnary from './_baseUnary.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates a function like `_.over`.\n *\n * @private\n * @param {Function} arrayFunc The function to iterate over iteratees.\n * @returns {Function} Returns the new over function.\n */\nfunction createOver(arrayFunc) {\n  return flatRest(function(iteratees) {\n    iteratees = arrayMap(iteratees, baseUnary(baseIteratee));\n    return baseRest(function(args) {\n      var thisArg = this;\n      return arrayFunc(iteratees, function(iteratee) {\n        return apply(iteratee, thisArg, args);\n      });\n    });\n  });\n}\n\nexport default createOver;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC7B,OAAOF,QAAQ,CAAC,UAASG,SAAS,EAAE;IAClCA,SAAS,GAAGP,QAAQ,CAACO,SAAS,EAAEJ,SAAS,CAACF,YAAY,CAAC,CAAC;IACxD,OAAOC,QAAQ,CAAC,UAASM,IAAI,EAAE;MAC7B,IAAIC,OAAO,GAAG,IAAI;MAClB,OAAOH,SAAS,CAACC,SAAS,EAAE,UAASG,QAAQ,EAAE;QAC7C,OAAOX,KAAK,CAACW,QAAQ,EAAED,OAAO,EAAED,IAAI,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}