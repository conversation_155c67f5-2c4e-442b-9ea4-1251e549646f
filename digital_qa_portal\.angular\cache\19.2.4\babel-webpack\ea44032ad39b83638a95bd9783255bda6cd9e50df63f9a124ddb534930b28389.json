{"ast": null, "code": "import baseRange from './_baseRange.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\nfunction createRange(fromRight) {\n  return function (start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    }\n    // Ensure the sign of `-0` is preserved.\n    start = toFinite(start);\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n    step = step === undefined ? start < end ? 1 : -1 : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\nexport default createRange;", "map": {"version": 3, "names": ["baseRange", "isIterateeCall", "toFinite", "createRange", "fromRight", "start", "end", "step", "undefined"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_createRange.js"], "sourcesContent": ["import baseRange from './_baseRange.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n\n/**\n * Creates a `_.range` or `_.rangeRight` function.\n *\n * @private\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new range function.\n */\nfunction createRange(fromRight) {\n  return function(start, end, step) {\n    if (step && typeof step != 'number' && isIterateeCall(start, end, step)) {\n      end = step = undefined;\n    }\n    // Ensure the sign of `-0` is preserved.\n    start = toFinite(start);\n    if (end === undefined) {\n      end = start;\n      start = 0;\n    } else {\n      end = toFinite(end);\n    }\n    step = step === undefined ? (start < end ? 1 : -1) : toFinite(step);\n    return baseRange(start, end, step, fromRight);\n  };\n}\n\nexport default createRange;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,SAAS,EAAE;EAC9B,OAAO,UAASC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAE;IAChC,IAAIA,IAAI,IAAI,OAAOA,IAAI,IAAI,QAAQ,IAAIN,cAAc,CAACI,KAAK,EAAEC,GAAG,EAAEC,IAAI,CAAC,EAAE;MACvED,GAAG,GAAGC,IAAI,GAAGC,SAAS;IACxB;IACA;IACAH,KAAK,GAAGH,QAAQ,CAACG,KAAK,CAAC;IACvB,IAAIC,GAAG,KAAKE,SAAS,EAAE;MACrBF,GAAG,GAAGD,KAAK;MACXA,KAAK,GAAG,CAAC;IACX,CAAC,MAAM;MACLC,GAAG,GAAGJ,QAAQ,CAACI,GAAG,CAAC;IACrB;IACAC,IAAI,GAAGA,IAAI,KAAKC,SAAS,GAAIH,KAAK,GAAGC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAIJ,QAAQ,CAACK,IAAI,CAAC;IACnE,OAAOP,SAAS,CAACK,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAEH,SAAS,CAAC;EAC/C,CAAC;AACH;AAEA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}