{"ast": null, "code": "import getNative from './_getNative.js';\nvar defineProperty = function () {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}();\nexport default defineProperty;", "map": {"version": 3, "names": ["getNative", "defineProperty", "func", "Object", "e"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_defineProperty.js"], "sourcesContent": ["import getNative from './_getNative.js';\n\nvar defineProperty = (function() {\n  try {\n    var func = getNative(Object, 'defineProperty');\n    func({}, '', {});\n    return func;\n  } catch (e) {}\n}());\n\nexport default defineProperty;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AAEvC,IAAIC,cAAc,GAAI,YAAW;EAC/B,IAAI;IACF,IAAIC,IAAI,GAAGF,SAAS,CAACG,MAAM,EAAE,gBAAgB,CAAC;IAC9CD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IAChB,OAAOA,IAAI;EACb,CAAC,CAAC,OAAOE,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAE;AAEJ,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}