{"ast": null, "code": "import { InjectionToken } from '@angular/core';\nimport { API_TYPES } from './core/constants/enum-constants';\nexport const APP_UI_CONFIG = {\n  configuration: {\n    getTenant: {\n      url: \"user/getTenant\",\n      type: API_TYPES.GET\n    },\n    getTenantConfig: {\n      url: \"config/tenantConfig/{tenantUid}\",\n      type: API_TYPES.GET,\n      paramList: {\n        tenantUid: null\n      }\n    }\n  },\n  auth: {\n    login: {\n      url: 'user',\n      type: API_TYPES.GET\n    },\n    mfaRegistration: {\n      url: \"mfa/qrcode\",\n      type: API_TYPES.GET\n    },\n    mfaValidation: {\n      url: 'mfa/code/',\n      type: API_TYPES.POST,\n      paramList: {\n        mfaCode: ''\n      }\n    }\n  },\n  administration: {\n    auditor: {\n      get: {\n        url: 'auditor',\n        type: API_TYPES.GET\n      },\n      create: {\n        url: 'users/create',\n        type: API_TYPES.POST\n      },\n      update: {\n        url: 'users/{uuid}/update',\n        type: API_TYPES.PUT,\n        paramList: {\n          uuid: ''\n        }\n      },\n      delete: {\n        url: 'users/{uuid}/deactivate',\n        type: 'DELETE',\n        paramList: {\n          uuid: ''\n        }\n      }\n    },\n    technician: {\n      get: {\n        url: 'technician',\n        type: API_TYPES.GET\n      },\n      create: {\n        url: 'users/create',\n        type: API_TYPES.POST\n      },\n      update: {\n        url: 'users/{uuid}/update',\n        type: API_TYPES.PUT,\n        paramList: {\n          uuid: ''\n        }\n      },\n      delete: {\n        url: 'users/{uuid}/deactivate',\n        type: 'DELETE',\n        paramList: {\n          uuid: ''\n        }\n      }\n    },\n    roles: {\n      getUserRoleList: {\n        url: 'userRole',\n        type: API_TYPES.GET\n      },\n      createORUpdate: {\n        url: 'roles',\n        type: API_TYPES.POST\n      },\n      update: {\n        url: 'roles/{uuid}/update',\n        type: API_TYPES.PUT,\n        paramList: {\n          uuid: ''\n        }\n      },\n      delete: {\n        url: 'roles',\n        type: API_TYPES.DELETE\n      }\n    },\n    users: {\n      get: {\n        url: 'user/role/{role}',\n        type: API_TYPES.GET,\n        paramList: {\n          role: ''\n        }\n      },\n      create: {\n        url: 'users/create',\n        type: API_TYPES.POST\n      },\n      update: {\n        url: 'users/{uuid}/update',\n        type: API_TYPES.PUT,\n        paramList: {\n          uuid: ''\n        }\n      },\n      delete: {\n        url: 'users/{uuid}/deactivate',\n        type: 'DELETE',\n        paramList: {\n          uuid: ''\n        }\n      },\n      activateUser: {\n        url: 'users/{uuid}/activate',\n        type: API_TYPES.PUT,\n        paramList: {\n          uuid: ''\n        }\n      },\n      deActivateUser: {\n        url: 'users/{uuid}/deactivate',\n        type: API_TYPES.DELETE,\n        paramList: {\n          uuid: ''\n        }\n      }\n    },\n    brand: {\n      get: {\n        url: 'brands',\n        type: API_TYPES.GET\n      },\n      create: {\n        url: 'brands',\n        type: API_TYPES.POST\n      },\n      update: {\n        url: 'brands/{uuid}/update',\n        type: API_TYPES.PUT,\n        paramList: {\n          uuid: ''\n        }\n      },\n      delete: {\n        url: 'brands',\n        type: API_TYPES.DELETE\n      }\n    },\n    categories: {\n      get: {\n        url: 'category',\n        type: API_TYPES.GET\n      },\n      create: {\n        url: '/api/categories',\n        type: API_TYPES.POST\n      },\n      update: {\n        url: '/api/categories/{uuid}',\n        type: API_TYPES.PUT,\n        paramList: {\n          uuid: ''\n        }\n      },\n      delete: {\n        url: '/api/categories/{uuid}',\n        type: API_TYPES.DELETE,\n        paramList: {\n          uuid: ''\n        }\n      }\n    }\n  },\n  notification: {\n    get: {\n      url: 'notification/',\n      type: API_TYPES.GET\n    },\n    getLatestNotification: {\n      url: 'notification/latest',\n      type: API_TYPES.GET\n    },\n    updateNotificationStatusByUid: {\n      url: 'notification/{uuid}/status/{status}',\n      type: API_TYPES.PUT,\n      paramList: {\n        uuid: '',\n        status: '1'\n      }\n    },\n    getNotificationHistory: {\n      url: 'notification/{from}/notifications/{to}',\n      type: API_TYPES.GET,\n      paramList: {\n        from: '',\n        to: ''\n      }\n    },\n    readAllNotifications: {\n      url: 'notification/read/all',\n      type: API_TYPES.POST\n    }\n  }\n};\nexport let APP_CONFIG = new InjectionToken('app.config');", "map": {"version": 3, "names": ["InjectionToken", "API_TYPES", "APP_UI_CONFIG", "configuration", "<PERSON><PERSON><PERSON><PERSON>", "url", "type", "GET", "getTenantConfig", "paramList", "tenantUid", "auth", "login", "mfaRegistration", "mfaValidation", "POST", "mfaCode", "administration", "auditor", "get", "create", "update", "PUT", "uuid", "delete", "technician", "roles", "getUserRoleList", "createORUpdate", "DELETE", "users", "role", "activateUser", "deActivateUser", "brand", "categories", "notification", "getLatestNotification", "updateNotificationStatusByUid", "status", "getNotificationHistory", "from", "to", "readAllNotifications", "APP_CONFIG"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\app\\app-config.constants.ts"], "sourcesContent": ["import { InjectionToken } from '@angular/core';\r\n\r\nimport { IAppConfig } from './app-config.interface';\r\nimport { API_TYPES } from './core/constants/enum-constants';\r\n\r\nexport const APP_UI_CONFIG: IAppConfig = {\r\n    configuration: {\r\n        getTenant: {\r\n            url: \"user/getTenant\",\r\n            type: API_TYPES.GET\r\n        },\r\n        getTenantConfig: {\r\n            url: \"config/tenantConfig/{tenantUid}\",\r\n            type: API_TYPES.GET,\r\n            paramList: { tenantUid: null }\r\n        }\r\n    },\r\n    auth: {\r\n        login: {\r\n            url: 'user',\r\n            type: API_TYPES.GET,\r\n        },\r\n        mfaRegistration: {\r\n            url: \"mfa/qrcode\",\r\n            type: API_TYPES.GET,\r\n        },\r\n        mfaValidation: {\r\n            url: 'mfa/code/',\r\n            type: API_TYPES.POST,\r\n            paramList: { mfaCode: '' }\r\n        }\r\n    },\r\n    administration: {\r\n        auditor: {\r\n            get: {\r\n                url: 'auditor',\r\n                type: API_TYPES.GET,\r\n            },\r\n            create: {\r\n                url: 'users/create',\r\n                type: API_TYPES.POST\r\n            },\r\n            update: {\r\n                url: 'users/{uuid}/update',\r\n                type: API_TYPES.PUT,\r\n                paramList: { uuid: '' }\r\n            },\r\n            delete: {\r\n                url: 'users/{uuid}/deactivate',\r\n                type: 'DELETE',\r\n                paramList: { uuid: '' }\r\n            }\r\n        },\r\n        technician: {\r\n            get: {\r\n                url: 'technician',\r\n                type: API_TYPES.GET,\r\n            },\r\n            create: {\r\n                url: 'users/create',\r\n                type: API_TYPES.POST\r\n            },\r\n            update: {\r\n                url: 'users/{uuid}/update',\r\n                type: API_TYPES.PUT,\r\n                paramList: { uuid: '' }\r\n            },\r\n            delete: {\r\n                url: 'users/{uuid}/deactivate',\r\n                type: 'DELETE',\r\n                paramList: { uuid: '' }\r\n            }\r\n        },\r\n        roles: {\r\n            getUserRoleList: {\r\n                url: 'userRole',\r\n                type: API_TYPES.GET,\r\n            },\r\n            createORUpdate: {\r\n                url: 'roles',\r\n                type: API_TYPES.POST\r\n            },\r\n            update: {\r\n                url: 'roles/{uuid}/update',\r\n                type: API_TYPES.PUT,\r\n                paramList: { uuid: '' }\r\n            },\r\n            delete: {\r\n                url: 'roles',\r\n                type: API_TYPES.DELETE,\r\n            }\r\n\r\n        },\r\n        users: {\r\n            get: {\r\n                url: 'user/role/{role}',\r\n                type: API_TYPES.GET,\r\n                paramList: { role: '' }\r\n            },\r\n            create: {\r\n                url: 'users/create',\r\n                type: API_TYPES.POST\r\n            },\r\n            update: {\r\n                url: 'users/{uuid}/update',\r\n                type: API_TYPES.PUT,\r\n                paramList: { uuid: '' }\r\n            },\r\n            delete: {\r\n                url: 'users/{uuid}/deactivate',\r\n                type: 'DELETE',\r\n                paramList: { uuid: '' }\r\n            },\r\n            activateUser: {\r\n                url: 'users/{uuid}/activate',\r\n                type: API_TYPES.PUT,\r\n                paramList: { uuid: '' }\r\n            },\r\n            deActivateUser: {\r\n                url: 'users/{uuid}/deactivate',\r\n                type: API_TYPES.DELETE,\r\n                paramList: { uuid: '' }\r\n            }\r\n        },\r\n\r\n        brand: {\r\n            get: {\r\n                url: 'brands',\r\n                type: API_TYPES.GET,\r\n            },\r\n            create: {\r\n                url: 'brands',\r\n                type: API_TYPES.POST\r\n            },\r\n            update: {\r\n                url: 'brands/{uuid}/update',\r\n                type: API_TYPES.PUT,\r\n                paramList: { uuid: '' }\r\n            },\r\n            delete: {\r\n                url: 'brands',\r\n                type: API_TYPES.DELETE,\r\n            }\r\n        },\r\n        categories: {\r\n            get: {\r\n                url: 'category',\r\n                type: API_TYPES.GET,\r\n\r\n            },\r\n            create: {\r\n                url: '/api/categories',\r\n                type: API_TYPES.POST,\r\n            },\r\n            update: {\r\n                url: '/api/categories/{uuid}',\r\n                type: API_TYPES.PUT,\r\n                paramList: {\r\n                    uuid: ''\r\n                }\r\n            },\r\n            delete: {\r\n                url: '/api/categories/{uuid}',\r\n                type: API_TYPES.DELETE,\r\n                paramList: {\r\n                    uuid: ''\r\n                }\r\n            }\r\n        }\r\n\r\n    },\r\n\r\n    notification: {\r\n        get: {\r\n            url: 'notification/',\r\n            type: API_TYPES.GET\r\n        },\r\n        getLatestNotification: {\r\n            url: 'notification/latest',\r\n            type: API_TYPES.GET\r\n        },\r\n        updateNotificationStatusByUid: {\r\n            url: 'notification/{uuid}/status/{status}',\r\n            type: API_TYPES.PUT,\r\n            paramList: { uuid: '', status: '1' },\r\n        },\r\n        getNotificationHistory: {\r\n            url: 'notification/{from}/notifications/{to}',\r\n            type: API_TYPES.GET,\r\n            paramList: { from: '', to: '' },\r\n        },\r\n        readAllNotifications: {\r\n            url: 'notification/read/all',\r\n            type: API_TYPES.POST,\r\n        }\r\n    },\r\n\r\n\r\n}\r\n\r\nexport let APP_CONFIG = new InjectionToken<IAppConfig>('app.config');\r\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,eAAe;AAG9C,SAASC,SAAS,QAAQ,iCAAiC;AAE3D,OAAO,MAAMC,aAAa,GAAe;EACrCC,aAAa,EAAE;IACXC,SAAS,EAAE;MACPC,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAEL,SAAS,CAACM;KACnB;IACDC,eAAe,EAAE;MACbH,GAAG,EAAE,iCAAiC;MACtCC,IAAI,EAAEL,SAAS,CAACM,GAAG;MACnBE,SAAS,EAAE;QAAEC,SAAS,EAAE;MAAI;;GAEnC;EACDC,IAAI,EAAE;IACFC,KAAK,EAAE;MACHP,GAAG,EAAE,MAAM;MACXC,IAAI,EAAEL,SAAS,CAACM;KACnB;IACDM,eAAe,EAAE;MACbR,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAEL,SAAS,CAACM;KACnB;IACDO,aAAa,EAAE;MACXT,GAAG,EAAE,WAAW;MAChBC,IAAI,EAAEL,SAAS,CAACc,IAAI;MACpBN,SAAS,EAAE;QAAEO,OAAO,EAAE;MAAE;;GAE/B;EACDC,cAAc,EAAE;IACZC,OAAO,EAAE;MACLC,GAAG,EAAE;QACDd,GAAG,EAAE,SAAS;QACdC,IAAI,EAAEL,SAAS,CAACM;OACnB;MACDa,MAAM,EAAE;QACJf,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAEL,SAAS,CAACc;OACnB;MACDM,MAAM,EAAE;QACJhB,GAAG,EAAE,qBAAqB;QAC1BC,IAAI,EAAEL,SAAS,CAACqB,GAAG;QACnBb,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;OACxB;MACDC,MAAM,EAAE;QACJnB,GAAG,EAAE,yBAAyB;QAC9BC,IAAI,EAAE,QAAQ;QACdG,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;;KAE5B;IACDE,UAAU,EAAE;MACRN,GAAG,EAAE;QACDd,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAEL,SAAS,CAACM;OACnB;MACDa,MAAM,EAAE;QACJf,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAEL,SAAS,CAACc;OACnB;MACDM,MAAM,EAAE;QACJhB,GAAG,EAAE,qBAAqB;QAC1BC,IAAI,EAAEL,SAAS,CAACqB,GAAG;QACnBb,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;OACxB;MACDC,MAAM,EAAE;QACJnB,GAAG,EAAE,yBAAyB;QAC9BC,IAAI,EAAE,QAAQ;QACdG,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;;KAE5B;IACDG,KAAK,EAAE;MACHC,eAAe,EAAE;QACbtB,GAAG,EAAE,UAAU;QACfC,IAAI,EAAEL,SAAS,CAACM;OACnB;MACDqB,cAAc,EAAE;QACZvB,GAAG,EAAE,OAAO;QACZC,IAAI,EAAEL,SAAS,CAACc;OACnB;MACDM,MAAM,EAAE;QACJhB,GAAG,EAAE,qBAAqB;QAC1BC,IAAI,EAAEL,SAAS,CAACqB,GAAG;QACnBb,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;OACxB;MACDC,MAAM,EAAE;QACJnB,GAAG,EAAE,OAAO;QACZC,IAAI,EAAEL,SAAS,CAAC4B;;KAGvB;IACDC,KAAK,EAAE;MACHX,GAAG,EAAE;QACDd,GAAG,EAAE,kBAAkB;QACvBC,IAAI,EAAEL,SAAS,CAACM,GAAG;QACnBE,SAAS,EAAE;UAAEsB,IAAI,EAAE;QAAE;OACxB;MACDX,MAAM,EAAE;QACJf,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAEL,SAAS,CAACc;OACnB;MACDM,MAAM,EAAE;QACJhB,GAAG,EAAE,qBAAqB;QAC1BC,IAAI,EAAEL,SAAS,CAACqB,GAAG;QACnBb,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;OACxB;MACDC,MAAM,EAAE;QACJnB,GAAG,EAAE,yBAAyB;QAC9BC,IAAI,EAAE,QAAQ;QACdG,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;OACxB;MACDS,YAAY,EAAE;QACV3B,GAAG,EAAE,uBAAuB;QAC5BC,IAAI,EAAEL,SAAS,CAACqB,GAAG;QACnBb,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;OACxB;MACDU,cAAc,EAAE;QACZ5B,GAAG,EAAE,yBAAyB;QAC9BC,IAAI,EAAEL,SAAS,CAAC4B,MAAM;QACtBpB,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;;KAE5B;IAEDW,KAAK,EAAE;MACHf,GAAG,EAAE;QACDd,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAEL,SAAS,CAACM;OACnB;MACDa,MAAM,EAAE;QACJf,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAEL,SAAS,CAACc;OACnB;MACDM,MAAM,EAAE;QACJhB,GAAG,EAAE,sBAAsB;QAC3BC,IAAI,EAAEL,SAAS,CAACqB,GAAG;QACnBb,SAAS,EAAE;UAAEc,IAAI,EAAE;QAAE;OACxB;MACDC,MAAM,EAAE;QACJnB,GAAG,EAAE,QAAQ;QACbC,IAAI,EAAEL,SAAS,CAAC4B;;KAEvB;IACDM,UAAU,EAAE;MACRhB,GAAG,EAAE;QACDd,GAAG,EAAE,UAAU;QACfC,IAAI,EAAEL,SAAS,CAACM;OAEnB;MACDa,MAAM,EAAE;QACJf,GAAG,EAAE,iBAAiB;QACtBC,IAAI,EAAEL,SAAS,CAACc;OACnB;MACDM,MAAM,EAAE;QACJhB,GAAG,EAAE,wBAAwB;QAC7BC,IAAI,EAAEL,SAAS,CAACqB,GAAG;QACnBb,SAAS,EAAE;UACPc,IAAI,EAAE;;OAEb;MACDC,MAAM,EAAE;QACJnB,GAAG,EAAE,wBAAwB;QAC7BC,IAAI,EAAEL,SAAS,CAAC4B,MAAM;QACtBpB,SAAS,EAAE;UACPc,IAAI,EAAE;;;;GAKrB;EAEDa,YAAY,EAAE;IACVjB,GAAG,EAAE;MACDd,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAEL,SAAS,CAACM;KACnB;IACD8B,qBAAqB,EAAE;MACnBhC,GAAG,EAAE,qBAAqB;MAC1BC,IAAI,EAAEL,SAAS,CAACM;KACnB;IACD+B,6BAA6B,EAAE;MAC3BjC,GAAG,EAAE,qCAAqC;MAC1CC,IAAI,EAAEL,SAAS,CAACqB,GAAG;MACnBb,SAAS,EAAE;QAAEc,IAAI,EAAE,EAAE;QAAEgB,MAAM,EAAE;MAAG;KACrC;IACDC,sBAAsB,EAAE;MACpBnC,GAAG,EAAE,wCAAwC;MAC7CC,IAAI,EAAEL,SAAS,CAACM,GAAG;MACnBE,SAAS,EAAE;QAAEgC,IAAI,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE;KAChC;IACDC,oBAAoB,EAAE;MAClBtC,GAAG,EAAE,uBAAuB;MAC5BC,IAAI,EAAEL,SAAS,CAACc;;;CAK3B;AAED,OAAO,IAAI6B,UAAU,GAAG,IAAI5C,cAAc,CAAa,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}