{"ast": null, "code": "import { get, init } from \"./schedule.js\";\nfunction delayFunction(id, value) {\n  return function () {\n    init(this, id).delay = +value.apply(this, arguments);\n  };\n}\nfunction delayConstant(id, value) {\n  return value = +value, function () {\n    init(this, id).delay = value;\n  };\n}\nexport default function (value) {\n  var id = this._id;\n  return arguments.length ? this.each((typeof value === \"function\" ? delayFunction : delayConstant)(id, value)) : get(this.node(), id).delay;\n}", "map": {"version": 3, "names": ["get", "init", "delayFunction", "id", "value", "delay", "apply", "arguments", "delayConstant", "_id", "length", "each", "node"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-transition/src/transition/delay.js"], "sourcesContent": ["import {get, init} from \"./schedule.js\";\n\nfunction delayFunction(id, value) {\n  return function() {\n    init(this, id).delay = +value.apply(this, arguments);\n  };\n}\n\nfunction delayConstant(id, value) {\n  return value = +value, function() {\n    init(this, id).delay = value;\n  };\n}\n\nexport default function(value) {\n  var id = this._id;\n\n  return arguments.length\n      ? this.each((typeof value === \"function\"\n          ? delayFunction\n          : delayConstant)(id, value))\n      : get(this.node(), id).delay;\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,QAAO,eAAe;AAEvC,SAASC,aAAaA,CAACC,EAAE,EAAEC,KAAK,EAAE;EAChC,OAAO,YAAW;IAChBH,IAAI,CAAC,IAAI,EAAEE,EAAE,CAAC,CAACE,KAAK,GAAG,CAACD,KAAK,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EACtD,CAAC;AACH;AAEA,SAASC,aAAaA,CAACL,EAAE,EAAEC,KAAK,EAAE;EAChC,OAAOA,KAAK,GAAG,CAACA,KAAK,EAAE,YAAW;IAChCH,IAAI,CAAC,IAAI,EAAEE,EAAE,CAAC,CAACE,KAAK,GAAGD,KAAK;EAC9B,CAAC;AACH;AAEA,eAAe,UAASA,KAAK,EAAE;EAC7B,IAAID,EAAE,GAAG,IAAI,CAACM,GAAG;EAEjB,OAAOF,SAAS,CAACG,MAAM,GACjB,IAAI,CAACC,IAAI,CAAC,CAAC,OAAOP,KAAK,KAAK,UAAU,GAClCF,aAAa,GACbM,aAAa,EAAEL,EAAE,EAAEC,KAAK,CAAC,CAAC,GAC9BJ,GAAG,CAAC,IAAI,CAACY,IAAI,CAAC,CAAC,EAAET,EAAE,CAAC,CAACE,KAAK;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}