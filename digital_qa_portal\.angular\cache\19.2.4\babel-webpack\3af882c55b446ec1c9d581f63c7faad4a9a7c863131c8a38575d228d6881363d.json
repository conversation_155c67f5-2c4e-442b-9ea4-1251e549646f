{"ast": null, "code": "import isPlainObject from './isPlainObject.js';\n\n/**\n * Used by `_.omit` to customize its `_.cloneDeep` use to only clone plain\n * objects.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {string} key The key of the property to inspect.\n * @returns {*} Returns the uncloned value or `undefined` to defer cloning to `_.cloneDeep`.\n */\nfunction customOmitClone(value) {\n  return isPlainObject(value) ? undefined : value;\n}\nexport default customOmitClone;", "map": {"version": 3, "names": ["isPlainObject", "customOmitClone", "value", "undefined"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_customOmitClone.js"], "sourcesContent": ["import isPlainObject from './isPlainObject.js';\n\n/**\n * Used by `_.omit` to customize its `_.cloneDeep` use to only clone plain\n * objects.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {string} key The key of the property to inspect.\n * @returns {*} Returns the uncloned value or `undefined` to defer cloning to `_.cloneDeep`.\n */\nfunction customOmitClone(value) {\n  return isPlainObject(value) ? undefined : value;\n}\n\nexport default customOmitClone;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAOF,aAAa,CAACE,KAAK,CAAC,GAAGC,SAAS,GAAGD,KAAK;AACjD;AAEA,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}