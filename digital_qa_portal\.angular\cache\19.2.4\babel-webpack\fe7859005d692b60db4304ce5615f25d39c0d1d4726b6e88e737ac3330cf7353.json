{"ast": null, "code": "import apply from './_apply.js';\nimport castPath from './_castPath.js';\nimport last from './last.js';\nimport parent from './_parent.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.invoke` without support for individual\n * method arguments.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the method to invoke.\n * @param {Array} args The arguments to invoke the method with.\n * @returns {*} Returns the result of the invoked method.\n */\nfunction baseInvoke(object, path, args) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  var func = object == null ? object : object[toKey(last(path))];\n  return func == null ? undefined : apply(func, object, args);\n}\nexport default baseInvoke;", "map": {"version": 3, "names": ["apply", "<PERSON><PERSON><PERSON>", "last", "parent", "to<PERSON><PERSON>", "baseInvoke", "object", "path", "args", "func", "undefined"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_baseInvoke.js"], "sourcesContent": ["import apply from './_apply.js';\nimport castPath from './_castPath.js';\nimport last from './last.js';\nimport parent from './_parent.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.invoke` without support for individual\n * method arguments.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the method to invoke.\n * @param {Array} args The arguments to invoke the method with.\n * @returns {*} Returns the result of the invoked method.\n */\nfunction baseInvoke(object, path, args) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  var func = object == null ? object : object[toKey(last(path))];\n  return func == null ? undefined : apply(func, object, args);\n}\n\nexport default baseInvoke;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACtCD,IAAI,GAAGN,QAAQ,CAACM,IAAI,EAAED,MAAM,CAAC;EAC7BA,MAAM,GAAGH,MAAM,CAACG,MAAM,EAAEC,IAAI,CAAC;EAC7B,IAAIE,IAAI,GAAGH,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGA,MAAM,CAACF,KAAK,CAACF,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC;EAC9D,OAAOE,IAAI,IAAI,IAAI,GAAGC,SAAS,GAAGV,KAAK,CAACS,IAAI,EAAEH,MAAM,EAAEE,IAAI,CAAC;AAC7D;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}