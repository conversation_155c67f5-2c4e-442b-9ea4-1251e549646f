{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Ng<PERSON><PERSON>, Injector, ViewContainerRef, afterNextRender, Directive, Input, ChangeDetectorRef, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { AriaDescriber, FocusMonitor, A11yModule } from '@angular/cdk/a11y';\nimport { Overlay, ScrollDispatcher, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { DOCUMENT, NgClass } from '@angular/common';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { M as MatCommonModule } from './common-module-a39ee957.mjs';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst _c0 = [\"tooltip\"];\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition({\n      scrollThrottle: SCROLL_THROTTLE_MS\n    });\n  }\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition({\n    scrollThrottle: SCROLL_THROTTLE_MS\n  });\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY\n};\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500\n  };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n  providedIn: 'root',\n  factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _platform = inject(Platform);\n  _ariaDescriber = inject(AriaDescriber);\n  _focusMonitor = inject(FocusMonitor);\n  _dir = inject(Directionality);\n  _injector = inject(Injector);\n  _viewContainerRef = inject(ViewContainerRef);\n  _defaultOptions = inject(MAT_TOOLTIP_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  _overlayRef;\n  _tooltipInstance;\n  _portal;\n  _position = 'below';\n  _positionAtOrigin = false;\n  _disabled = false;\n  _tooltipClass;\n  _viewInitialized = false;\n  _pointerExitEventsInitialized = false;\n  _tooltipComponent = TooltipComponent;\n  _viewportMargin = 8;\n  _currentPosition;\n  _cssClassPrefix = 'mat-mdc';\n  _ariaDescriptionPending;\n  _dirSubscribed = false;\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n  get position() {\n    return this._position;\n  }\n  set position(value) {\n    if (value !== this._position) {\n      this._position = value;\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n        this._tooltipInstance?.show(0);\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n  /**\n   * Whether tooltip should be relative to the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  get positionAtOrigin() {\n    return this._positionAtOrigin;\n  }\n  set positionAtOrigin(value) {\n    this._positionAtOrigin = coerceBooleanProperty(value);\n    this._detach();\n    this._overlayRef = null;\n  }\n  /** Disables the display of the tooltip. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    const isDisabled = coerceBooleanProperty(value);\n    if (this._disabled !== isDisabled) {\n      this._disabled = isDisabled;\n      // If tooltip is disabled, hide immediately.\n      if (isDisabled) {\n        this.hide(0);\n      } else {\n        this._setupPointerEnterEventsIfNeeded();\n      }\n      this._syncAriaDescription(this.message);\n    }\n  }\n  /** The default delay in ms before showing the tooltip after show is called */\n  get showDelay() {\n    return this._showDelay;\n  }\n  set showDelay(value) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n  _showDelay;\n  /** The default delay in ms before hiding the tooltip after hide is called */\n  get hideDelay() {\n    return this._hideDelay;\n  }\n  set hideDelay(value) {\n    this._hideDelay = coerceNumberProperty(value);\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n  _hideDelay;\n  /**\n   * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n   * uses a long press gesture to show and hide, however it can conflict with the native browser\n   * gestures. To work around the conflict, Angular Material disables native gestures on the\n   * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n   * elements). The different values for this option configure the touch event handling as follows:\n   * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n   *   browser gestures on particular elements. In particular, it allows text selection on inputs\n   *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n   * - `on` - Enables touch gestures for all elements and disables native\n   *   browser gestures with no exceptions.\n   * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n   *   showing on touch devices.\n   */\n  touchGestures = 'auto';\n  /** The message to be displayed in the tooltip */\n  get message() {\n    return this._message;\n  }\n  set message(value) {\n    const oldMessage = this._message;\n    // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n    this._message = value != null ? String(value).trim() : '';\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n      this._updateTooltipMessage();\n    }\n    this._syncAriaDescription(oldMessage);\n  }\n  _message = '';\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n  set tooltipClass(value) {\n    this._tooltipClass = value;\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n  /** Manually-bound passive event listeners. */\n  _passiveListeners = [];\n  /** Timer started at the last `touchstart` event. */\n  _touchstartTimeout = null;\n  /** Emits when the component is destroyed. */\n  _destroyed = new Subject();\n  /** Whether ngOnDestroyed has been called. */\n  _isDestroyed = false;\n  constructor() {\n    const defaultOptions = this._defaultOptions;\n    if (defaultOptions) {\n      this._showDelay = defaultOptions.showDelay;\n      this._hideDelay = defaultOptions.hideDelay;\n      if (defaultOptions.position) {\n        this.position = defaultOptions.position;\n      }\n      if (defaultOptions.positionAtOrigin) {\n        this.positionAtOrigin = defaultOptions.positionAtOrigin;\n      }\n      if (defaultOptions.touchGestures) {\n        this.touchGestures = defaultOptions.touchGestures;\n      }\n      if (defaultOptions.tooltipClass) {\n        this.tooltipClass = defaultOptions.tooltipClass;\n      }\n    }\n    this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n  }\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n    this._setupPointerEnterEventsIfNeeded();\n    this._focusMonitor.monitor(this._elementRef).pipe(takeUntil(this._destroyed)).subscribe(origin => {\n      // Note that the focus monitor runs outside the Angular zone.\n      if (!origin) {\n        this._ngZone.run(() => this.hide(0));\n      } else if (origin === 'keyboard') {\n        this._ngZone.run(() => this.show());\n      }\n    });\n  }\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n    // Optimization: Do not call clearTimeout unless there is an active timer.\n    if (this._touchstartTimeout) {\n      clearTimeout(this._touchstartTimeout);\n    }\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._tooltipInstance = null;\n    }\n    // Clean up the event listeners set in the constructor\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n    this._passiveListeners.length = 0;\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._isDestroyed = true;\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n  show(delay = this.showDelay, origin) {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      this._tooltipInstance?._cancelPendingAnimations();\n      return;\n    }\n    const overlayRef = this._createOverlay(origin);\n    this._detach();\n    this._portal = this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = this._tooltipInstance = overlayRef.attach(this._portal).instance;\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance.afterHidden().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._setTooltipClass(this._tooltipClass);\n    this._updateTooltipMessage();\n    instance.show(delay);\n  }\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n  hide(delay = this.hideDelay) {\n    const instance = this._tooltipInstance;\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n        this._detach();\n      }\n    }\n  }\n  /** Shows/hides the tooltip */\n  toggle(origin) {\n    this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n  }\n  /** Returns true if the tooltip is currently visible to the user */\n  _isTooltipVisible() {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n  /** Create the overlay config and position strategy */\n  _createOverlay(origin) {\n    if (this._overlayRef) {\n      const existingStrategy = this._overlayRef.getConfig().positionStrategy;\n      if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n        return this._overlayRef;\n      }\n      this._detach();\n    }\n    const scrollableAncestors = this._injector.get(ScrollDispatcher).getAncestorScrollContainers(this._elementRef);\n    const overlay = this._injector.get(Overlay);\n    // Create connected position strategy that listens for scroll events to reposition.\n    const strategy = overlay.position().flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef).withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`).withFlexibleDimensions(false).withViewportMargin(this._viewportMargin).withScrollableContainers(scrollableAncestors);\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n    this._overlayRef = overlay.create({\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n      scrollStrategy: this._injector.get(MAT_TOOLTIP_SCROLL_STRATEGY)()\n    });\n    this._updatePosition(this._overlayRef);\n    this._overlayRef.detachments().pipe(takeUntil(this._destroyed)).subscribe(() => this._detach());\n    this._overlayRef.outsidePointerEvents().pipe(takeUntil(this._destroyed)).subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n    this._overlayRef.keydownEvents().pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n        event.preventDefault();\n        event.stopPropagation();\n        this._ngZone.run(() => this.hide(0));\n      }\n    });\n    if (this._defaultOptions?.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n    if (!this._dirSubscribed) {\n      this._dirSubscribed = true;\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (this._overlayRef) {\n          this._updatePosition(this._overlayRef);\n        }\n      });\n    }\n    return this._overlayRef;\n  }\n  /** Detaches the currently-attached tooltip. */\n  _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n    this._tooltipInstance = null;\n  }\n  /** Updates the position of the current tooltip. */\n  _updatePosition(overlayRef) {\n    const position = overlayRef.getConfig().positionStrategy;\n    const origin = this._getOrigin();\n    const overlay = this._getOverlayPosition();\n    position.withPositions([this._addOffset({\n      ...origin.main,\n      ...overlay.main\n    }), this._addOffset({\n      ...origin.fallback,\n      ...overlay.fallback\n    })]);\n  }\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n  _addOffset(position) {\n    const offset = UNBOUNDED_ANCHOR_GAP;\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    if (position.originY === 'top') {\n      position.offsetY = -offset;\n    } else if (position.originY === 'bottom') {\n      position.offsetY = offset;\n    } else if (position.originX === 'start') {\n      position.offsetX = isLtr ? -offset : offset;\n    } else if (position.originX === 'end') {\n      position.offsetX = isLtr ? offset : -offset;\n    }\n    return position;\n  }\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n  _getOrigin() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition;\n    if (position == 'above' || position == 'below') {\n      originPosition = {\n        originX: 'center',\n        originY: position == 'above' ? 'top' : 'bottom'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      originPosition = {\n        originX: 'start',\n        originY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      originPosition = {\n        originX: 'end',\n        originY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(originPosition.originX, originPosition.originY);\n    return {\n      main: originPosition,\n      fallback: {\n        originX: x,\n        originY: y\n      }\n    };\n  }\n  /** Returns the overlay position and a fallback position based on the user's preference */\n  _getOverlayPosition() {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition;\n    if (position == 'above') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'bottom'\n      };\n    } else if (position == 'below') {\n      overlayPosition = {\n        overlayX: 'center',\n        overlayY: 'top'\n      };\n    } else if (position == 'before' || position == 'left' && isLtr || position == 'right' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'end',\n        overlayY: 'center'\n      };\n    } else if (position == 'after' || position == 'right' && isLtr || position == 'left' && !isLtr) {\n      overlayPosition = {\n        overlayX: 'start',\n        overlayY: 'center'\n      };\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n    const {\n      x,\n      y\n    } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n    return {\n      main: overlayPosition,\n      fallback: {\n        overlayX: x,\n        overlayY: y\n      }\n    };\n  }\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n  _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n      this._tooltipInstance._markForCheck();\n      afterNextRender(() => {\n        if (this._tooltipInstance) {\n          this._overlayRef.updatePosition();\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n  }\n  /** Updates the tooltip class */\n  _setTooltipClass(tooltipClass) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n      this._tooltipInstance._markForCheck();\n    }\n  }\n  /** Inverts an overlay position. */\n  _invertPosition(x, y) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n  _updateCurrentPositionClass(connectionPair) {\n    const {\n      overlayY,\n      originX,\n      originY\n    } = connectionPair;\n    let newPosition;\n    // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n      this._currentPosition = newPosition;\n    }\n  }\n  /** Binds the pointer events to the tooltip trigger. */\n  _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (this._disabled || !this.message || !this._viewInitialized || this._passiveListeners.length) {\n      return;\n    }\n    // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push(['mouseenter', event => {\n        this._setupPointerExitEventsIfNeeded();\n        let point = undefined;\n        if (event.x !== undefined && event.y !== undefined) {\n          point = event;\n        }\n        this.show(undefined, point);\n      }]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      this._passiveListeners.push(['touchstart', event => {\n        const touch = event.targetTouches?.[0];\n        const origin = touch ? {\n          x: touch.clientX,\n          y: touch.clientY\n        } : undefined;\n        // Note that it's important that we don't `preventDefault` here,\n        // because it can prevent click events from firing on the element.\n        this._setupPointerExitEventsIfNeeded();\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        const DEFAULT_LONGPRESS_DELAY = 500;\n        this._touchstartTimeout = setTimeout(() => {\n          this._touchstartTimeout = null;\n          this.show(undefined, origin);\n        }, this._defaultOptions?.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n      }]);\n    }\n    this._addListeners(this._passiveListeners);\n  }\n  _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n    this._pointerExitEventsInitialized = true;\n    const exitListeners = [];\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(['mouseleave', event => {\n        const newTarget = event.relatedTarget;\n        if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n          this.hide();\n        }\n      }], ['wheel', event => this._wheelListener(event)]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      const touchendListener = () => {\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        this.hide(this._defaultOptions?.touchendHideDelay);\n      };\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n    this._addListeners(exitListeners);\n    this._passiveListeners.push(...exitListeners);\n  }\n  _addListeners(listeners) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n  _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n  /** Listener for the `wheel` event on the element. */\n  _wheelListener(event) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._injector.get(DOCUMENT).elementFromPoint(event.clientX, event.clientY);\n      const element = this._elementRef.nativeElement;\n      // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n  _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style;\n      // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n      if (gestures === 'on' || element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA') {\n        style.userSelect = style.msUserSelect = style.webkitUserSelect = style.MozUserSelect = 'none';\n      }\n      // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n      if (gestures === 'on' || !element.draggable) {\n        style.webkitUserDrag = 'none';\n      }\n      style.touchAction = 'none';\n      style.webkitTapHighlightColor = 'transparent';\n    }\n  }\n  /** Updates the tooltip's ARIA description based on it current state. */\n  _syncAriaDescription(oldMessage) {\n    if (this._ariaDescriptionPending) {\n      return;\n    }\n    this._ariaDescriptionPending = true;\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, oldMessage, 'tooltip');\n    // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n    // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n    // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n    // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n    if (!this._isDestroyed) {\n      afterNextRender({\n        write: () => {\n          this._ariaDescriptionPending = false;\n          if (this.message && !this.disabled) {\n            this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n          }\n        }\n      }, {\n        injector: this._injector\n      });\n    }\n  }\n  static ɵfac = function MatTooltip_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTooltip)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatTooltip,\n    selectors: [[\"\", \"matTooltip\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-tooltip-trigger\"],\n    hostVars: 2,\n    hostBindings: function MatTooltip_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mat-mdc-tooltip-disabled\", ctx.disabled);\n      }\n    },\n    inputs: {\n      position: [0, \"matTooltipPosition\", \"position\"],\n      positionAtOrigin: [0, \"matTooltipPositionAtOrigin\", \"positionAtOrigin\"],\n      disabled: [0, \"matTooltipDisabled\", \"disabled\"],\n      showDelay: [0, \"matTooltipShowDelay\", \"showDelay\"],\n      hideDelay: [0, \"matTooltipHideDelay\", \"hideDelay\"],\n      touchGestures: [0, \"matTooltipTouchGestures\", \"touchGestures\"],\n      message: [0, \"matTooltip\", \"message\"],\n      tooltipClass: [0, \"matTooltipClass\", \"tooltipClass\"]\n    },\n    exportAs: [\"matTooltip\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[matTooltip]',\n      exportAs: 'matTooltip',\n      host: {\n        'class': 'mat-mdc-tooltip-trigger',\n        '[class.mat-mdc-tooltip-disabled]': 'disabled'\n      }\n    }]\n  }], () => [], {\n    position: [{\n      type: Input,\n      args: ['matTooltipPosition']\n    }],\n    positionAtOrigin: [{\n      type: Input,\n      args: ['matTooltipPositionAtOrigin']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['matTooltipDisabled']\n    }],\n    showDelay: [{\n      type: Input,\n      args: ['matTooltipShowDelay']\n    }],\n    hideDelay: [{\n      type: Input,\n      args: ['matTooltipHideDelay']\n    }],\n    touchGestures: [{\n      type: Input,\n      args: ['matTooltipTouchGestures']\n    }],\n    message: [{\n      type: Input,\n      args: ['matTooltip']\n    }],\n    tooltipClass: [{\n      type: Input,\n      args: ['matTooltipClass']\n    }]\n  });\n})();\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  /* Whether the tooltip text overflows to multiple lines */\n  _isMultiline = false;\n  /** Message to display in the tooltip */\n  message;\n  /** Classes to be added to the tooltip. Supports the same syntax as `ngClass`. */\n  tooltipClass;\n  /** The timeout ID of any current timer set to show the tooltip */\n  _showTimeoutId;\n  /** The timeout ID of any current timer set to hide the tooltip */\n  _hideTimeoutId;\n  /** Element that caused the tooltip to open. */\n  _triggerElement;\n  /** Amount of milliseconds to delay the closing sequence. */\n  _mouseLeaveHideDelay;\n  /** Whether animations are currently disabled. */\n  _animationsDisabled;\n  /** Reference to the internal tooltip element. */\n  _tooltip;\n  /** Whether interactions on the page should close the tooltip */\n  _closeOnInteraction = false;\n  /** Whether the tooltip is currently visible. */\n  _isVisible = false;\n  /** Subject for notifying that the tooltip has been hidden from the view */\n  _onHide = new Subject();\n  /** Name of the show animation and the class that toggles it. */\n  _showAnimation = 'mat-mdc-tooltip-show';\n  /** Name of the hide animation and the class that toggles it. */\n  _hideAnimation = 'mat-mdc-tooltip-hide';\n  constructor() {\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n  }\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n  show(delay) {\n    // Cancel the delayed hide if it is scheduled\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n  hide(delay) {\n    // Cancel the delayed show if it is scheduled\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n  afterHidden() {\n    return this._onHide;\n  }\n  /** Whether the tooltip is being displayed. */\n  isVisible() {\n    return this._isVisible;\n  }\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n    this._onHide.complete();\n    this._triggerElement = null;\n  }\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n  _handleBodyInteraction() {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n  _markForCheck() {\n    this._changeDetectorRef.markForCheck();\n  }\n  _handleMouseLeave({\n    relatedTarget\n  }) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n  _onShow() {\n    this._isMultiline = this._isTooltipMultiline();\n    this._markForCheck();\n  }\n  /** Whether the tooltip text has overflown to the next line */\n  _isTooltipMultiline() {\n    const rect = this._elementRef.nativeElement.getBoundingClientRect();\n    return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n  }\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n  _handleAnimationEnd({\n    animationName\n  }) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n  /** Cancels any pending animation sequences. */\n  _cancelPendingAnimations() {\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n  /** Handles the cleanup after an animation has finished. */\n  _finalizeAnimation(toVisible) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n  /** Toggles the visibility of the tooltip element. */\n  _toggleVisibility(isVisible) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    if (this._isVisible !== isVisible) {\n      this._isVisible = isVisible;\n      this._changeDetectorRef.markForCheck();\n    }\n    // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip);\n      // Use `getPropertyValue` to avoid issues with property renaming.\n      if (styles.getPropertyValue('animation-duration') === '0s' || styles.getPropertyValue('animation-name') === 'none') {\n        this._animationsDisabled = true;\n      }\n    }\n    if (isVisible) {\n      this._onShow();\n    }\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n      this._finalizeAnimation(isVisible);\n    }\n  }\n  static ɵfac = function TooltipComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TooltipComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TooltipComponent,\n    selectors: [[\"mat-tooltip-component\"]],\n    viewQuery: function TooltipComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tooltip = _t.first);\n      }\n    },\n    hostAttrs: [\"aria-hidden\", \"true\"],\n    hostBindings: function TooltipComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mouseleave\", function TooltipComponent_mouseleave_HostBindingHandler($event) {\n          return ctx._handleMouseLeave($event);\n        });\n      }\n    },\n    decls: 4,\n    vars: 4,\n    consts: [[\"tooltip\", \"\"], [1, \"mdc-tooltip\", \"mat-mdc-tooltip\", 3, \"animationend\", \"ngClass\"], [1, \"mat-mdc-tooltip-surface\", \"mdc-tooltip__surface\"]],\n    template: function TooltipComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵlistener(\"animationend\", function TooltipComponent_Template_div_animationend_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx._handleAnimationEnd($event));\n        });\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"mdc-tooltip--multiline\", ctx._isMultiline);\n        i0.ɵɵproperty(\"ngClass\", ctx.tooltipClass);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate(ctx.message);\n      }\n    },\n    dependencies: [NgClass],\n    styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'mat-tooltip-component',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '(mouseleave)': '_handleMouseLeave($event)',\n        'aria-hidden': 'true'\n      },\n      imports: [NgClass],\n      template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mat-mdc-tooltip-surface mdc-tooltip__surface\\\">{{message}}</div>\\n</div>\\n\",\n      styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"]\n    }]\n  }], () => [], {\n    _tooltip: [{\n      type: ViewChild,\n      args: ['tooltip', {\n        // Use a static query here since we interact directly with\n        // the DOM which can happen before `ngAfterViewInit`.\n        static: true\n      }]\n    }]\n  });\n})();\nclass MatTooltipModule {\n  static ɵfac = function MatTooltipModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatTooltipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatTooltipModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n    imports: [A11yModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatTooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n      exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n      providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\nexport { MatTooltip as M, SCROLL_THROTTLE_MS as S, TOOLTIP_PANEL_CLASS as T, MatTooltipModule as a, MAT_TOOLTIP_SCROLL_STRATEGY as b, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY as c, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER as d, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY as e, MAT_TOOLTIP_DEFAULT_OPTIONS as f, getMatTooltipInvalidPositionError as g, TooltipComponent as h };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "ElementRef", "NgZone", "Injector", "ViewContainerRef", "afterNextRender", "Directive", "Input", "ChangeDetectorRef", "ANIMATION_MODULE_TYPE", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ViewChild", "NgModule", "AriaDescriber", "FocusMonitor", "A11yModule", "Overlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OverlayModule", "CdkScrollableModule", "takeUntil", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "DOCUMENT", "Ng<PERSON><PERSON>", "normalizePassiveListenerOptions", "Platform", "Directionality", "ComponentPortal", "Subject", "M", "MatCommonModule", "_c0", "SCROLL_THROTTLE_MS", "getMatTooltipInvalidPositionError", "position", "Error", "MAT_TOOLTIP_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "reposition", "scrollThrottle", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER", "provide", "deps", "useFactory", "MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "touchendHideDelay", "MAT_TOOLTIP_DEFAULT_OPTIONS", "TOOLTIP_PANEL_CLASS", "PANEL_CLASS", "passiveListenerOptions", "passive", "MIN_VIEWPORT_TOOLTIP_THRESHOLD", "UNBOUNDED_ANCHOR_GAP", "MIN_HEIGHT", "MAX_WIDTH", "MatTooltip", "_elementRef", "_ngZone", "_platform", "_ariaDescriber", "_focusMonitor", "_dir", "_injector", "_viewContainerRef", "_defaultOptions", "optional", "_overlayRef", "_tooltipInstance", "_portal", "_position", "_position<PERSON><PERSON><PERSON><PERSON><PERSON>", "_disabled", "_tooltipClass", "_viewInitialized", "_pointerExitEventsInitialized", "_tooltipComponent", "TooltipComponent", "_viewportMargin", "_currentPosition", "_cssClassPrefix", "_ariaDescriptionPending", "_dirSubscribed", "value", "_updatePosition", "show", "updatePosition", "positionAt<PERSON><PERSON><PERSON>", "_detach", "disabled", "isDisabled", "hide", "_setupPointerEnterEventsIfNeeded", "_syncAriaDescription", "message", "_showDelay", "_hideDelay", "_mouseLeaveHideDelay", "touchGestures", "_message", "oldMessage", "String", "trim", "_isTooltipVisible", "_updateTooltipMessage", "tooltipClass", "_setTooltipClass", "_passiveListeners", "_touchstartTimeout", "_destroyed", "_isDestroyed", "constructor", "defaultOptions", "ngAfterViewInit", "monitor", "pipe", "subscribe", "origin", "run", "ngOnDestroy", "nativeElement", "clearTimeout", "dispose", "for<PERSON>ach", "event", "listener", "removeEventListener", "length", "next", "complete", "removeDescription", "stopMonitoring", "delay", "_cancelPendingAnimations", "overlayRef", "_createOverlay", "instance", "attach", "_triggerElement", "afterHidden", "isVisible", "toggle", "undefined", "existingStrategy", "getConfig", "positionStrategy", "_origin", "scrollableAncestors", "get", "getAncestorScrollContainers", "strategy", "flexibleConnectedTo", "withTransformOriginOn", "withFlexibleDimensions", "withViewportMargin", "withScrollableContainers", "position<PERSON><PERSON>es", "change", "_updateCurrentPositionClass", "connectionPair", "scrollableViewProperties", "isOverlayClipped", "create", "direction", "panelClass", "scrollStrategy", "detachments", "outsidePointerEvents", "_handleBodyInteraction", "keydownEvents", "keyCode", "preventDefault", "stopPropagation", "disableTooltipInteractivity", "addPanelClass", "has<PERSON>tta<PERSON>", "detach", "_get<PERSON><PERSON>in", "_getOverlayPosition", "withPositions", "_addOffset", "main", "fallback", "offset", "isLtr", "originY", "offsetY", "originX", "offsetX", "originPosition", "ngDevMode", "x", "y", "_invertPosition", "overlayPosition", "overlayX", "overlayY", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "injector", "newPosition", "classPrefix", "removePanelClass", "_platformSupportsMouseEvents", "push", "_setupPointerExitEventsIfNeeded", "point", "_disableNativeGesturesIfNecessary", "touch", "targetTouches", "clientX", "clientY", "DEFAULT_LONGPRESS_DELAY", "setTimeout", "touchLongPressShowDelay", "_addListeners", "exitListeners", "newTarget", "relatedTarget", "overlayElement", "contains", "_wheelListener", "touchendListener", "listeners", "addEventListener", "IOS", "ANDROID", "elementUnderPointer", "elementFromPoint", "element", "gestures", "style", "nodeName", "userSelect", "msUserSelect", "webkitUserSelect", "MozUserSelect", "draggable", "webkitUserDrag", "touchAction", "webkitTapHighlightColor", "write", "describe", "ɵfac", "MatTooltip_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatTooltip_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "exportAs", "ɵsetClassMetadata", "args", "selector", "host", "_changeDetectorRef", "_isMultiline", "_showTimeoutId", "_hideTimeoutId", "_animationsDisabled", "_tooltip", "_closeOnInteraction", "_isVisible", "_onHide", "_showAnimation", "_hideAnimation", "animationMode", "_toggleVisibility", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_handleMouseLeave", "_finalizeAnimation", "_onShow", "_isTooltipMultiline", "rect", "getBoundingClientRect", "height", "width", "_handleAnimationEnd", "animationName", "toVisible", "tooltip", "showClass", "hideClass", "classList", "remove", "add", "getComputedStyle", "styles", "getPropertyValue", "TooltipComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "TooltipComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "TooltipComponent_HostBindings", "ɵɵlistener", "TooltipComponent_mouseleave_HostBindingHandler", "$event", "decls", "vars", "consts", "template", "TooltipComponent_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "TooltipComponent_Template_div_animationend_0_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate", "dependencies", "encapsulation", "changeDetection", "None", "OnPush", "imports", "static", "MatTooltipModule", "MatTooltipModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers", "exports", "S", "T", "a", "b", "c", "d", "e", "f", "g", "h"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/@angular/material/fesm2022/module-087ecec3.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Ng<PERSON><PERSON>, Injector, ViewContainerRef, afterNextRender, Directive, Input, ChangeDetectorRef, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { AriaDescriber, FocusMonitor, A11yModule } from '@angular/cdk/a11y';\nimport { Overlay, ScrollDispatcher, OverlayModule } from '@angular/cdk/overlay';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { takeUntil } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { DOCUMENT, NgClass } from '@angular/common';\nimport { normalizePassiveListenerOptions, Platform } from '@angular/cdk/platform';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { M as MatCommonModule } from './common-module-a39ee957.mjs';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n    return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n    },\n});\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        showDelay: 0,\n        hideDelay: 0,\n        touchendHideDelay: 1500,\n    };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({ passive: true });\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _platform = inject(Platform);\n    _ariaDescriber = inject(AriaDescriber);\n    _focusMonitor = inject(FocusMonitor);\n    _dir = inject(Directionality);\n    _injector = inject(Injector);\n    _viewContainerRef = inject(ViewContainerRef);\n    _defaultOptions = inject(MAT_TOOLTIP_DEFAULT_OPTIONS, {\n        optional: true,\n    });\n    _overlayRef;\n    _tooltipInstance;\n    _portal;\n    _position = 'below';\n    _positionAtOrigin = false;\n    _disabled = false;\n    _tooltipClass;\n    _viewInitialized = false;\n    _pointerExitEventsInitialized = false;\n    _tooltipComponent = TooltipComponent;\n    _viewportMargin = 8;\n    _currentPosition;\n    _cssClassPrefix = 'mat-mdc';\n    _ariaDescriptionPending;\n    _dirSubscribed = false;\n    /** Allows the user to define the position of the tooltip relative to the parent element */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        if (value !== this._position) {\n            this._position = value;\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n                this._tooltipInstance?.show(0);\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    /**\n     * Whether tooltip should be relative to the click or touch origin\n     * instead of outside the element bounding box.\n     */\n    get positionAtOrigin() {\n        return this._positionAtOrigin;\n    }\n    set positionAtOrigin(value) {\n        this._positionAtOrigin = coerceBooleanProperty(value);\n        this._detach();\n        this._overlayRef = null;\n    }\n    /** Disables the display of the tooltip. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        const isDisabled = coerceBooleanProperty(value);\n        if (this._disabled !== isDisabled) {\n            this._disabled = isDisabled;\n            // If tooltip is disabled, hide immediately.\n            if (isDisabled) {\n                this.hide(0);\n            }\n            else {\n                this._setupPointerEnterEventsIfNeeded();\n            }\n            this._syncAriaDescription(this.message);\n        }\n    }\n    /** The default delay in ms before showing the tooltip after show is called */\n    get showDelay() {\n        return this._showDelay;\n    }\n    set showDelay(value) {\n        this._showDelay = coerceNumberProperty(value);\n    }\n    _showDelay;\n    /** The default delay in ms before hiding the tooltip after hide is called */\n    get hideDelay() {\n        return this._hideDelay;\n    }\n    set hideDelay(value) {\n        this._hideDelay = coerceNumberProperty(value);\n        if (this._tooltipInstance) {\n            this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n        }\n    }\n    _hideDelay;\n    /**\n     * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n     * uses a long press gesture to show and hide, however it can conflict with the native browser\n     * gestures. To work around the conflict, Angular Material disables native gestures on the\n     * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n     * elements). The different values for this option configure the touch event handling as follows:\n     * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n     *   browser gestures on particular elements. In particular, it allows text selection on inputs\n     *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n     * - `on` - Enables touch gestures for all elements and disables native\n     *   browser gestures with no exceptions.\n     * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n     *   showing on touch devices.\n     */\n    touchGestures = 'auto';\n    /** The message to be displayed in the tooltip */\n    get message() {\n        return this._message;\n    }\n    set message(value) {\n        const oldMessage = this._message;\n        // If the message is not a string (e.g. number), convert it to a string and trim it.\n        // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n        // away the string-conversion: https://github.com/angular/components/issues/20684\n        this._message = value != null ? String(value).trim() : '';\n        if (!this._message && this._isTooltipVisible()) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n            this._updateTooltipMessage();\n        }\n        this._syncAriaDescription(oldMessage);\n    }\n    _message = '';\n    /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n    get tooltipClass() {\n        return this._tooltipClass;\n    }\n    set tooltipClass(value) {\n        this._tooltipClass = value;\n        if (this._tooltipInstance) {\n            this._setTooltipClass(this._tooltipClass);\n        }\n    }\n    /** Manually-bound passive event listeners. */\n    _passiveListeners = [];\n    /** Timer started at the last `touchstart` event. */\n    _touchstartTimeout = null;\n    /** Emits when the component is destroyed. */\n    _destroyed = new Subject();\n    /** Whether ngOnDestroyed has been called. */\n    _isDestroyed = false;\n    constructor() {\n        const defaultOptions = this._defaultOptions;\n        if (defaultOptions) {\n            this._showDelay = defaultOptions.showDelay;\n            this._hideDelay = defaultOptions.hideDelay;\n            if (defaultOptions.position) {\n                this.position = defaultOptions.position;\n            }\n            if (defaultOptions.positionAtOrigin) {\n                this.positionAtOrigin = defaultOptions.positionAtOrigin;\n            }\n            if (defaultOptions.touchGestures) {\n                this.touchGestures = defaultOptions.touchGestures;\n            }\n            if (defaultOptions.tooltipClass) {\n                this.tooltipClass = defaultOptions.tooltipClass;\n            }\n        }\n        this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n    }\n    ngAfterViewInit() {\n        // This needs to happen after view init so the initial values for all inputs have been set.\n        this._viewInitialized = true;\n        this._setupPointerEnterEventsIfNeeded();\n        this._focusMonitor\n            .monitor(this._elementRef)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            // Note that the focus monitor runs outside the Angular zone.\n            if (!origin) {\n                this._ngZone.run(() => this.hide(0));\n            }\n            else if (origin === 'keyboard') {\n                this._ngZone.run(() => this.show());\n            }\n        });\n    }\n    /**\n     * Dispose the tooltip when destroyed.\n     */\n    ngOnDestroy() {\n        const nativeElement = this._elementRef.nativeElement;\n        // Optimization: Do not call clearTimeout unless there is an active timer.\n        if (this._touchstartTimeout) {\n            clearTimeout(this._touchstartTimeout);\n        }\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._tooltipInstance = null;\n        }\n        // Clean up the event listeners set in the constructor\n        this._passiveListeners.forEach(([event, listener]) => {\n            nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n        });\n        this._passiveListeners.length = 0;\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._isDestroyed = true;\n        this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n        this._focusMonitor.stopMonitoring(nativeElement);\n    }\n    /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n    show(delay = this.showDelay, origin) {\n        if (this.disabled || !this.message || this._isTooltipVisible()) {\n            this._tooltipInstance?._cancelPendingAnimations();\n            return;\n        }\n        const overlayRef = this._createOverlay(origin);\n        this._detach();\n        this._portal =\n            this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n        const instance = (this._tooltipInstance = overlayRef.attach(this._portal).instance);\n        instance._triggerElement = this._elementRef.nativeElement;\n        instance._mouseLeaveHideDelay = this._hideDelay;\n        instance\n            .afterHidden()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._setTooltipClass(this._tooltipClass);\n        this._updateTooltipMessage();\n        instance.show(delay);\n    }\n    /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n    hide(delay = this.hideDelay) {\n        const instance = this._tooltipInstance;\n        if (instance) {\n            if (instance.isVisible()) {\n                instance.hide(delay);\n            }\n            else {\n                instance._cancelPendingAnimations();\n                this._detach();\n            }\n        }\n    }\n    /** Shows/hides the tooltip */\n    toggle(origin) {\n        this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n    }\n    /** Returns true if the tooltip is currently visible to the user */\n    _isTooltipVisible() {\n        return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n    }\n    /** Create the overlay config and position strategy */\n    _createOverlay(origin) {\n        if (this._overlayRef) {\n            const existingStrategy = this._overlayRef.getConfig()\n                .positionStrategy;\n            if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n                return this._overlayRef;\n            }\n            this._detach();\n        }\n        const scrollableAncestors = this._injector\n            .get(ScrollDispatcher)\n            .getAncestorScrollContainers(this._elementRef);\n        const overlay = this._injector.get(Overlay);\n        // Create connected position strategy that listens for scroll events to reposition.\n        const strategy = overlay\n            .position()\n            .flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef)\n            .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n            .withFlexibleDimensions(false)\n            .withViewportMargin(this._viewportMargin)\n            .withScrollableContainers(scrollableAncestors);\n        strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n            this._updateCurrentPositionClass(change.connectionPair);\n            if (this._tooltipInstance) {\n                if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n                    // After position changes occur and the overlay is clipped by\n                    // a parent scrollable then close the tooltip.\n                    this._ngZone.run(() => this.hide(0));\n                }\n            }\n        });\n        this._overlayRef = overlay.create({\n            direction: this._dir,\n            positionStrategy: strategy,\n            panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n            scrollStrategy: this._injector.get(MAT_TOOLTIP_SCROLL_STRATEGY)(),\n        });\n        this._updatePosition(this._overlayRef);\n        this._overlayRef\n            .detachments()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._overlayRef\n            .outsidePointerEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n        this._overlayRef\n            .keydownEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(event => {\n            if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n                event.preventDefault();\n                event.stopPropagation();\n                this._ngZone.run(() => this.hide(0));\n            }\n        });\n        if (this._defaultOptions?.disableTooltipInteractivity) {\n            this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n        }\n        if (!this._dirSubscribed) {\n            this._dirSubscribed = true;\n            this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                if (this._overlayRef) {\n                    this._updatePosition(this._overlayRef);\n                }\n            });\n        }\n        return this._overlayRef;\n    }\n    /** Detaches the currently-attached tooltip. */\n    _detach() {\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n        }\n        this._tooltipInstance = null;\n    }\n    /** Updates the position of the current tooltip. */\n    _updatePosition(overlayRef) {\n        const position = overlayRef.getConfig().positionStrategy;\n        const origin = this._getOrigin();\n        const overlay = this._getOverlayPosition();\n        position.withPositions([\n            this._addOffset({ ...origin.main, ...overlay.main }),\n            this._addOffset({ ...origin.fallback, ...overlay.fallback }),\n        ]);\n    }\n    /** Adds the configured offset to a position. Used as a hook for child classes. */\n    _addOffset(position) {\n        const offset = UNBOUNDED_ANCHOR_GAP;\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        if (position.originY === 'top') {\n            position.offsetY = -offset;\n        }\n        else if (position.originY === 'bottom') {\n            position.offsetY = offset;\n        }\n        else if (position.originX === 'start') {\n            position.offsetX = isLtr ? -offset : offset;\n        }\n        else if (position.originX === 'end') {\n            position.offsetX = isLtr ? offset : -offset;\n        }\n        return position;\n    }\n    /**\n     * Returns the origin position and a fallback position based on the user's position preference.\n     * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n     */\n    _getOrigin() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let originPosition;\n        if (position == 'above' || position == 'below') {\n            originPosition = { originX: 'center', originY: position == 'above' ? 'top' : 'bottom' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            originPosition = { originX: 'start', originY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            originPosition = { originX: 'end', originY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(originPosition.originX, originPosition.originY);\n        return {\n            main: originPosition,\n            fallback: { originX: x, originY: y },\n        };\n    }\n    /** Returns the overlay position and a fallback position based on the user's preference */\n    _getOverlayPosition() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let overlayPosition;\n        if (position == 'above') {\n            overlayPosition = { overlayX: 'center', overlayY: 'bottom' };\n        }\n        else if (position == 'below') {\n            overlayPosition = { overlayX: 'center', overlayY: 'top' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            overlayPosition = { overlayX: 'end', overlayY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            overlayPosition = { overlayX: 'start', overlayY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n        return {\n            main: overlayPosition,\n            fallback: { overlayX: x, overlayY: y },\n        };\n    }\n    /** Updates the tooltip message and repositions the overlay according to the new message length */\n    _updateTooltipMessage() {\n        // Must wait for the message to be painted to the tooltip so that the overlay can properly\n        // calculate the correct positioning based on the size of the text.\n        if (this._tooltipInstance) {\n            this._tooltipInstance.message = this.message;\n            this._tooltipInstance._markForCheck();\n            afterNextRender(() => {\n                if (this._tooltipInstance) {\n                    this._overlayRef.updatePosition();\n                }\n            }, {\n                injector: this._injector,\n            });\n        }\n    }\n    /** Updates the tooltip class */\n    _setTooltipClass(tooltipClass) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.tooltipClass = tooltipClass;\n            this._tooltipInstance._markForCheck();\n        }\n    }\n    /** Inverts an overlay position. */\n    _invertPosition(x, y) {\n        if (this.position === 'above' || this.position === 'below') {\n            if (y === 'top') {\n                y = 'bottom';\n            }\n            else if (y === 'bottom') {\n                y = 'top';\n            }\n        }\n        else {\n            if (x === 'end') {\n                x = 'start';\n            }\n            else if (x === 'start') {\n                x = 'end';\n            }\n        }\n        return { x, y };\n    }\n    /** Updates the class on the overlay panel based on the current position of the tooltip. */\n    _updateCurrentPositionClass(connectionPair) {\n        const { overlayY, originX, originY } = connectionPair;\n        let newPosition;\n        // If the overlay is in the middle along the Y axis,\n        // it means that it's either before or after.\n        if (overlayY === 'center') {\n            // Note that since this information is used for styling, we want to\n            // resolve `start` and `end` to their real values, otherwise consumers\n            // would have to remember to do it themselves on each consumption.\n            if (this._dir && this._dir.value === 'rtl') {\n                newPosition = originX === 'end' ? 'left' : 'right';\n            }\n            else {\n                newPosition = originX === 'start' ? 'left' : 'right';\n            }\n        }\n        else {\n            newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n        }\n        if (newPosition !== this._currentPosition) {\n            const overlayRef = this._overlayRef;\n            if (overlayRef) {\n                const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n                overlayRef.removePanelClass(classPrefix + this._currentPosition);\n                overlayRef.addPanelClass(classPrefix + newPosition);\n            }\n            this._currentPosition = newPosition;\n        }\n    }\n    /** Binds the pointer events to the tooltip trigger. */\n    _setupPointerEnterEventsIfNeeded() {\n        // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n        if (this._disabled ||\n            !this.message ||\n            !this._viewInitialized ||\n            this._passiveListeners.length) {\n            return;\n        }\n        // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n        // first tap from firing its click event or can cause the tooltip to open for clicks.\n        if (this._platformSupportsMouseEvents()) {\n            this._passiveListeners.push([\n                'mouseenter',\n                event => {\n                    this._setupPointerExitEventsIfNeeded();\n                    let point = undefined;\n                    if (event.x !== undefined && event.y !== undefined) {\n                        point = event;\n                    }\n                    this.show(undefined, point);\n                },\n            ]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            this._passiveListeners.push([\n                'touchstart',\n                event => {\n                    const touch = event.targetTouches?.[0];\n                    const origin = touch ? { x: touch.clientX, y: touch.clientY } : undefined;\n                    // Note that it's important that we don't `preventDefault` here,\n                    // because it can prevent click events from firing on the element.\n                    this._setupPointerExitEventsIfNeeded();\n                    if (this._touchstartTimeout) {\n                        clearTimeout(this._touchstartTimeout);\n                    }\n                    const DEFAULT_LONGPRESS_DELAY = 500;\n                    this._touchstartTimeout = setTimeout(() => {\n                        this._touchstartTimeout = null;\n                        this.show(undefined, origin);\n                    }, this._defaultOptions?.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n                },\n            ]);\n        }\n        this._addListeners(this._passiveListeners);\n    }\n    _setupPointerExitEventsIfNeeded() {\n        if (this._pointerExitEventsInitialized) {\n            return;\n        }\n        this._pointerExitEventsInitialized = true;\n        const exitListeners = [];\n        if (this._platformSupportsMouseEvents()) {\n            exitListeners.push([\n                'mouseleave',\n                event => {\n                    const newTarget = event.relatedTarget;\n                    if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n                        this.hide();\n                    }\n                },\n            ], ['wheel', event => this._wheelListener(event)]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            const touchendListener = () => {\n                if (this._touchstartTimeout) {\n                    clearTimeout(this._touchstartTimeout);\n                }\n                this.hide(this._defaultOptions?.touchendHideDelay);\n            };\n            exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n        }\n        this._addListeners(exitListeners);\n        this._passiveListeners.push(...exitListeners);\n    }\n    _addListeners(listeners) {\n        listeners.forEach(([event, listener]) => {\n            this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n        });\n    }\n    _platformSupportsMouseEvents() {\n        return !this._platform.IOS && !this._platform.ANDROID;\n    }\n    /** Listener for the `wheel` event on the element. */\n    _wheelListener(event) {\n        if (this._isTooltipVisible()) {\n            const elementUnderPointer = this._injector\n                .get(DOCUMENT)\n                .elementFromPoint(event.clientX, event.clientY);\n            const element = this._elementRef.nativeElement;\n            // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n            // won't fire if the user scrolls away using the wheel without moving their cursor. We\n            // work around it by finding the element under the user's cursor and closing the tooltip\n            // if it's not the trigger.\n            if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n                this.hide();\n            }\n        }\n    }\n    /** Disables the native browser gestures, based on how the tooltip has been configured. */\n    _disableNativeGesturesIfNecessary() {\n        const gestures = this.touchGestures;\n        if (gestures !== 'off') {\n            const element = this._elementRef.nativeElement;\n            const style = element.style;\n            // If gestures are set to `auto`, we don't disable text selection on inputs and\n            // textareas, because it prevents the user from typing into them on iOS Safari.\n            if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n                style.userSelect =\n                    style.msUserSelect =\n                        style.webkitUserSelect =\n                            style.MozUserSelect =\n                                'none';\n            }\n            // If we have `auto` gestures and the element uses native HTML dragging,\n            // we don't set `-webkit-user-drag` because it prevents the native behavior.\n            if (gestures === 'on' || !element.draggable) {\n                style.webkitUserDrag = 'none';\n            }\n            style.touchAction = 'none';\n            style.webkitTapHighlightColor = 'transparent';\n        }\n    }\n    /** Updates the tooltip's ARIA description based on it current state. */\n    _syncAriaDescription(oldMessage) {\n        if (this._ariaDescriptionPending) {\n            return;\n        }\n        this._ariaDescriptionPending = true;\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, oldMessage, 'tooltip');\n        // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n        // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n        // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n        // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n        if (!this._isDestroyed) {\n            afterNextRender({\n                write: () => {\n                    this._ariaDescriptionPending = false;\n                    if (this.message && !this.disabled) {\n                        this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n                    }\n                },\n            }, { injector: this._injector });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.0\", ngImport: i0, type: MatTooltip, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.0\", type: MatTooltip, isStandalone: true, selector: \"[matTooltip]\", inputs: { position: [\"matTooltipPosition\", \"position\"], positionAtOrigin: [\"matTooltipPositionAtOrigin\", \"positionAtOrigin\"], disabled: [\"matTooltipDisabled\", \"disabled\"], showDelay: [\"matTooltipShowDelay\", \"showDelay\"], hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"], touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"], message: [\"matTooltip\", \"message\"], tooltipClass: [\"matTooltipClass\", \"tooltipClass\"] }, host: { properties: { \"class.mat-mdc-tooltip-disabled\": \"disabled\" }, classAttribute: \"mat-mdc-tooltip-trigger\" }, exportAs: [\"matTooltip\"], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.0\", ngImport: i0, type: MatTooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTooltip]',\n                    exportAs: 'matTooltip',\n                    host: {\n                        'class': 'mat-mdc-tooltip-trigger',\n                        '[class.mat-mdc-tooltip-disabled]': 'disabled',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { position: [{\n                type: Input,\n                args: ['matTooltipPosition']\n            }], positionAtOrigin: [{\n                type: Input,\n                args: ['matTooltipPositionAtOrigin']\n            }], disabled: [{\n                type: Input,\n                args: ['matTooltipDisabled']\n            }], showDelay: [{\n                type: Input,\n                args: ['matTooltipShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['matTooltipHideDelay']\n            }], touchGestures: [{\n                type: Input,\n                args: ['matTooltipTouchGestures']\n            }], message: [{\n                type: Input,\n                args: ['matTooltip']\n            }], tooltipClass: [{\n                type: Input,\n                args: ['matTooltipClass']\n            }] } });\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    /* Whether the tooltip text overflows to multiple lines */\n    _isMultiline = false;\n    /** Message to display in the tooltip */\n    message;\n    /** Classes to be added to the tooltip. Supports the same syntax as `ngClass`. */\n    tooltipClass;\n    /** The timeout ID of any current timer set to show the tooltip */\n    _showTimeoutId;\n    /** The timeout ID of any current timer set to hide the tooltip */\n    _hideTimeoutId;\n    /** Element that caused the tooltip to open. */\n    _triggerElement;\n    /** Amount of milliseconds to delay the closing sequence. */\n    _mouseLeaveHideDelay;\n    /** Whether animations are currently disabled. */\n    _animationsDisabled;\n    /** Reference to the internal tooltip element. */\n    _tooltip;\n    /** Whether interactions on the page should close the tooltip */\n    _closeOnInteraction = false;\n    /** Whether the tooltip is currently visible. */\n    _isVisible = false;\n    /** Subject for notifying that the tooltip has been hidden from the view */\n    _onHide = new Subject();\n    /** Name of the show animation and the class that toggles it. */\n    _showAnimation = 'mat-mdc-tooltip-show';\n    /** Name of the hide animation and the class that toggles it. */\n    _hideAnimation = 'mat-mdc-tooltip-hide';\n    constructor() {\n        const animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n    }\n    /**\n     * Shows the tooltip with an animation originating from the provided origin\n     * @param delay Amount of milliseconds to the delay showing the tooltip.\n     */\n    show(delay) {\n        // Cancel the delayed hide if it is scheduled\n        if (this._hideTimeoutId != null) {\n            clearTimeout(this._hideTimeoutId);\n        }\n        this._showTimeoutId = setTimeout(() => {\n            this._toggleVisibility(true);\n            this._showTimeoutId = undefined;\n        }, delay);\n    }\n    /**\n     * Begins the animation to hide the tooltip after the provided delay in ms.\n     * @param delay Amount of milliseconds to delay showing the tooltip.\n     */\n    hide(delay) {\n        // Cancel the delayed show if it is scheduled\n        if (this._showTimeoutId != null) {\n            clearTimeout(this._showTimeoutId);\n        }\n        this._hideTimeoutId = setTimeout(() => {\n            this._toggleVisibility(false);\n            this._hideTimeoutId = undefined;\n        }, delay);\n    }\n    /** Returns an observable that notifies when the tooltip has been hidden from view. */\n    afterHidden() {\n        return this._onHide;\n    }\n    /** Whether the tooltip is being displayed. */\n    isVisible() {\n        return this._isVisible;\n    }\n    ngOnDestroy() {\n        this._cancelPendingAnimations();\n        this._onHide.complete();\n        this._triggerElement = null;\n    }\n    /**\n     * Interactions on the HTML body should close the tooltip immediately as defined in the\n     * material design spec.\n     * https://material.io/design/components/tooltips.html#behavior\n     */\n    _handleBodyInteraction() {\n        if (this._closeOnInteraction) {\n            this.hide(0);\n        }\n    }\n    /**\n     * Marks that the tooltip needs to be checked in the next change detection run.\n     * Mainly used for rendering the initial text before positioning a tooltip, which\n     * can be problematic in components with OnPush change detection.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n    _handleMouseLeave({ relatedTarget }) {\n        if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n            if (this.isVisible()) {\n                this.hide(this._mouseLeaveHideDelay);\n            }\n            else {\n                this._finalizeAnimation(false);\n            }\n        }\n    }\n    /**\n     * Callback for when the timeout in this.show() gets completed.\n     * This method is only needed by the mdc-tooltip, and so it is only implemented\n     * in the mdc-tooltip, not here.\n     */\n    _onShow() {\n        this._isMultiline = this._isTooltipMultiline();\n        this._markForCheck();\n    }\n    /** Whether the tooltip text has overflown to the next line */\n    _isTooltipMultiline() {\n        const rect = this._elementRef.nativeElement.getBoundingClientRect();\n        return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n    }\n    /** Event listener dispatched when an animation on the tooltip finishes. */\n    _handleAnimationEnd({ animationName }) {\n        if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n            this._finalizeAnimation(animationName === this._showAnimation);\n        }\n    }\n    /** Cancels any pending animation sequences. */\n    _cancelPendingAnimations() {\n        if (this._showTimeoutId != null) {\n            clearTimeout(this._showTimeoutId);\n        }\n        if (this._hideTimeoutId != null) {\n            clearTimeout(this._hideTimeoutId);\n        }\n        this._showTimeoutId = this._hideTimeoutId = undefined;\n    }\n    /** Handles the cleanup after an animation has finished. */\n    _finalizeAnimation(toVisible) {\n        if (toVisible) {\n            this._closeOnInteraction = true;\n        }\n        else if (!this.isVisible()) {\n            this._onHide.next();\n        }\n    }\n    /** Toggles the visibility of the tooltip element. */\n    _toggleVisibility(isVisible) {\n        // We set the classes directly here ourselves so that toggling the tooltip state\n        // isn't bound by change detection. This allows us to hide it even if the\n        // view ref has been detached from the CD tree.\n        const tooltip = this._tooltip.nativeElement;\n        const showClass = this._showAnimation;\n        const hideClass = this._hideAnimation;\n        tooltip.classList.remove(isVisible ? hideClass : showClass);\n        tooltip.classList.add(isVisible ? showClass : hideClass);\n        if (this._isVisible !== isVisible) {\n            this._isVisible = isVisible;\n            this._changeDetectorRef.markForCheck();\n        }\n        // It's common for internal apps to disable animations using `* { animation: none !important }`\n        // which can break the opening sequence. Try to detect such cases and work around them.\n        if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n            const styles = getComputedStyle(tooltip);\n            // Use `getPropertyValue` to avoid issues with property renaming.\n            if (styles.getPropertyValue('animation-duration') === '0s' ||\n                styles.getPropertyValue('animation-name') === 'none') {\n                this._animationsDisabled = true;\n            }\n        }\n        if (isVisible) {\n            this._onShow();\n        }\n        if (this._animationsDisabled) {\n            tooltip.classList.add('_mat-animation-noopable');\n            this._finalizeAnimation(isVisible);\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.0\", ngImport: i0, type: TooltipComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"19.2.0\", type: TooltipComponent, isStandalone: true, selector: \"mat-tooltip-component\", host: { attributes: { \"aria-hidden\": \"true\" }, listeners: { \"mouseleave\": \"_handleMouseLeave($event)\" } }, viewQueries: [{ propertyName: \"_tooltip\", first: true, predicate: [\"tooltip\"], descendants: true, static: true }], ngImport: i0, template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mat-mdc-tooltip-surface mdc-tooltip__surface\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"], dependencies: [{ kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.0\", ngImport: i0, type: TooltipComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tooltip-component', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '(mouseleave)': '_handleMouseLeave($event)',\n                        'aria-hidden': 'true',\n                    }, imports: [NgClass], template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mat-mdc-tooltip-surface mdc-tooltip__surface\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mat-mdc-tooltip{position:relative;transform:scale(0);display:inline-flex}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-surface{word-break:normal;overflow-wrap:anywhere;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center;will-change:transform,opacity;background-color:var(--mdc-plain-tooltip-container-color, var(--mat-sys-inverse-surface));color:var(--mdc-plain-tooltip-supporting-text-color, var(--mat-sys-inverse-on-surface));border-radius:var(--mdc-plain-tooltip-container-shape, var(--mat-sys-corner-extra-small));font-family:var(--mdc-plain-tooltip-supporting-text-font, var(--mat-sys-body-small-font));font-size:var(--mdc-plain-tooltip-supporting-text-size, var(--mat-sys-body-small-size));font-weight:var(--mdc-plain-tooltip-supporting-text-weight, var(--mat-sys-body-small-weight));line-height:var(--mdc-plain-tooltip-supporting-text-line-height, var(--mat-sys-body-small-line-height));letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking, var(--mat-sys-body-small-tracking))}.mat-mdc-tooltip-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}.mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mat-mdc-tooltip-surface{text-align:right}.mat-mdc-tooltip-panel{line-height:normal}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"] }]\n        }], ctorParameters: () => [], propDecorators: { _tooltip: [{\n                type: ViewChild,\n                args: ['tooltip', {\n                        // Use a static query here since we interact directly with\n                        // the DOM which can happen before `ngAfterViewInit`.\n                        static: true,\n                    }]\n            }] } });\n\nclass MatTooltipModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.0\", ngImport: i0, type: MatTooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.0\", ngImport: i0, type: MatTooltipModule, imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent], exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.0\", ngImport: i0, type: MatTooltipModule, providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [A11yModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.0\", ngImport: i0, type: MatTooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n                    exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n                    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\nexport { MatTooltip as M, SCROLL_THROTTLE_MS as S, TOOLTIP_PANEL_CLASS as T, MatTooltipModule as a, MAT_TOOLTIP_SCROLL_STRATEGY as b, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY as c, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER as d, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY as e, MAT_TOOLTIP_DEFAULT_OPTIONS as f, getMatTooltipInvalidPositionError as g, TooltipComponent as h };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AAC/P,SAASC,aAAa,EAAEC,YAAY,EAAEC,UAAU,QAAQ,mBAAmB;AAC3E,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,sBAAsB;AAC/E,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,uBAAuB;AACnF,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,QAAQ,EAAEC,OAAO,QAAQ,iBAAiB;AACnD,SAASC,+BAA+B,EAAEC,QAAQ,QAAQ,uBAAuB;AACjF,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;;AAEnE;AAAA,MAAAC,GAAA;AACA,MAAMC,kBAAkB,GAAG,EAAE;AAC7B;AACA;AACA;AACA;AACA,SAASC,iCAAiCA,CAACC,QAAQ,EAAE;EACjD,OAAOC,KAAK,CAAC,qBAAqBD,QAAQ,eAAe,CAAC;AAC9D;AACA;AACA,MAAME,2BAA2B,GAAG,IAAI1C,cAAc,CAAC,6BAA6B,EAAE;EAClF2C,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAG5C,MAAM,CAACkB,OAAO,CAAC;IAC/B,OAAO,MAAM0B,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;MAAEC,cAAc,EAAEV;IAAmB,CAAC,CAAC;EAC5F;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,SAASW,mCAAmCA,CAACJ,OAAO,EAAE;EAClD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAAEC,cAAc,EAAEV;EAAmB,CAAC,CAAC;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,4CAA4C,GAAG;EACjDC,OAAO,EAAET,2BAA2B;EACpCU,IAAI,EAAE,CAACjC,OAAO,CAAC;EACfkC,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAASK,mCAAmCA,CAAA,EAAG;EAC3C,OAAO;IACHC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE;EACvB,CAAC;AACL;AACA;AACA,MAAMC,2BAA2B,GAAG,IAAI1D,cAAc,CAAC,6BAA6B,EAAE;EAClF2C,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEU;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMK,mBAAmB,GAAG,uBAAuB;AACnD,MAAMC,WAAW,GAAG,eAAe;AACnC;AACA,MAAMC,sBAAsB,GAAG/B,+BAA+B,CAAC;EAAEgC,OAAO,EAAE;AAAK,CAAC,CAAC;AACjF;AACA;AACA,MAAMC,8BAA8B,GAAG,CAAC;AACxC,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,SAAS,GAAG,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,WAAW,GAAGnE,MAAM,CAACC,UAAU,CAAC;EAChCmE,OAAO,GAAGpE,MAAM,CAACE,MAAM,CAAC;EACxBmE,SAAS,GAAGrE,MAAM,CAAC8B,QAAQ,CAAC;EAC5BwC,cAAc,GAAGtE,MAAM,CAACe,aAAa,CAAC;EACtCwD,aAAa,GAAGvE,MAAM,CAACgB,YAAY,CAAC;EACpCwD,IAAI,GAAGxE,MAAM,CAAC+B,cAAc,CAAC;EAC7B0C,SAAS,GAAGzE,MAAM,CAACG,QAAQ,CAAC;EAC5BuE,iBAAiB,GAAG1E,MAAM,CAACI,gBAAgB,CAAC;EAC5CuE,eAAe,GAAG3E,MAAM,CAACyD,2BAA2B,EAAE;IAClDmB,QAAQ,EAAE;EACd,CAAC,CAAC;EACFC,WAAW;EACXC,gBAAgB;EAChBC,OAAO;EACPC,SAAS,GAAG,OAAO;EACnBC,iBAAiB,GAAG,KAAK;EACzBC,SAAS,GAAG,KAAK;EACjBC,aAAa;EACbC,gBAAgB,GAAG,KAAK;EACxBC,6BAA6B,GAAG,KAAK;EACrCC,iBAAiB,GAAGC,gBAAgB;EACpCC,eAAe,GAAG,CAAC;EACnBC,gBAAgB;EAChBC,eAAe,GAAG,SAAS;EAC3BC,uBAAuB;EACvBC,cAAc,GAAG,KAAK;EACtB;EACA,IAAIrD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACyC,SAAS;EACzB;EACA,IAAIzC,QAAQA,CAACsD,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACb,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGa,KAAK;MACtB,IAAI,IAAI,CAAChB,WAAW,EAAE;QAClB,IAAI,CAACiB,eAAe,CAAC,IAAI,CAACjB,WAAW,CAAC;QACtC,IAAI,CAACC,gBAAgB,EAAEiB,IAAI,CAAC,CAAC,CAAC;QAC9B,IAAI,CAAClB,WAAW,CAACmB,cAAc,CAAC,CAAC;MACrC;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAChB,iBAAiB;EACjC;EACA,IAAIgB,gBAAgBA,CAACJ,KAAK,EAAE;IACxB,IAAI,CAACZ,iBAAiB,GAAG1D,qBAAqB,CAACsE,KAAK,CAAC;IACrD,IAAI,CAACK,OAAO,CAAC,CAAC;IACd,IAAI,CAACrB,WAAW,GAAG,IAAI;EAC3B;EACA;EACA,IAAIsB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjB,SAAS;EACzB;EACA,IAAIiB,QAAQA,CAACN,KAAK,EAAE;IAChB,MAAMO,UAAU,GAAG7E,qBAAqB,CAACsE,KAAK,CAAC;IAC/C,IAAI,IAAI,CAACX,SAAS,KAAKkB,UAAU,EAAE;MAC/B,IAAI,CAAClB,SAAS,GAAGkB,UAAU;MAC3B;MACA,IAAIA,UAAU,EAAE;QACZ,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MAChB,CAAC,MACI;QACD,IAAI,CAACC,gCAAgC,CAAC,CAAC;MAC3C;MACA,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACC,OAAO,CAAC;IAC3C;EACJ;EACA;EACA,IAAIlD,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACmD,UAAU;EAC1B;EACA,IAAInD,SAASA,CAACuC,KAAK,EAAE;IACjB,IAAI,CAACY,UAAU,GAAGjF,oBAAoB,CAACqE,KAAK,CAAC;EACjD;EACAY,UAAU;EACV;EACA,IAAIlD,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACmD,UAAU;EAC1B;EACA,IAAInD,SAASA,CAACsC,KAAK,EAAE;IACjB,IAAI,CAACa,UAAU,GAAGlF,oBAAoB,CAACqE,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACf,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAC6B,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAChE;EACJ;EACAA,UAAU;EACV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,aAAa,GAAG,MAAM;EACtB;EACA,IAAIJ,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACK,QAAQ;EACxB;EACA,IAAIL,OAAOA,CAACX,KAAK,EAAE;IACf,MAAMiB,UAAU,GAAG,IAAI,CAACD,QAAQ;IAChC;IACA;IACA;IACA,IAAI,CAACA,QAAQ,GAAGhB,KAAK,IAAI,IAAI,GAAGkB,MAAM,CAAClB,KAAK,CAAC,CAACmB,IAAI,CAAC,CAAC,GAAG,EAAE;IACzD,IAAI,CAAC,IAAI,CAACH,QAAQ,IAAI,IAAI,CAACI,iBAAiB,CAAC,CAAC,EAAE;MAC5C,IAAI,CAACZ,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACC,gCAAgC,CAAC,CAAC;MACvC,IAAI,CAACY,qBAAqB,CAAC,CAAC;IAChC;IACA,IAAI,CAACX,oBAAoB,CAACO,UAAU,CAAC;EACzC;EACAD,QAAQ,GAAG,EAAE;EACb;EACA,IAAIM,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAChC,aAAa;EAC7B;EACA,IAAIgC,YAAYA,CAACtB,KAAK,EAAE;IACpB,IAAI,CAACV,aAAa,GAAGU,KAAK;IAC1B,IAAI,IAAI,CAACf,gBAAgB,EAAE;MACvB,IAAI,CAACsC,gBAAgB,CAAC,IAAI,CAACjC,aAAa,CAAC;IAC7C;EACJ;EACA;EACAkC,iBAAiB,GAAG,EAAE;EACtB;EACAC,kBAAkB,GAAG,IAAI;EACzB;EACAC,UAAU,GAAG,IAAItF,OAAO,CAAC,CAAC;EAC1B;EACAuF,YAAY,GAAG,KAAK;EACpBC,WAAWA,CAAA,EAAG;IACV,MAAMC,cAAc,GAAG,IAAI,CAAC/C,eAAe;IAC3C,IAAI+C,cAAc,EAAE;MAChB,IAAI,CAACjB,UAAU,GAAGiB,cAAc,CAACpE,SAAS;MAC1C,IAAI,CAACoD,UAAU,GAAGgB,cAAc,CAACnE,SAAS;MAC1C,IAAImE,cAAc,CAACnF,QAAQ,EAAE;QACzB,IAAI,CAACA,QAAQ,GAAGmF,cAAc,CAACnF,QAAQ;MAC3C;MACA,IAAImF,cAAc,CAACzB,gBAAgB,EAAE;QACjC,IAAI,CAACA,gBAAgB,GAAGyB,cAAc,CAACzB,gBAAgB;MAC3D;MACA,IAAIyB,cAAc,CAACd,aAAa,EAAE;QAC9B,IAAI,CAACA,aAAa,GAAGc,cAAc,CAACd,aAAa;MACrD;MACA,IAAIc,cAAc,CAACP,YAAY,EAAE;QAC7B,IAAI,CAACA,YAAY,GAAGO,cAAc,CAACP,YAAY;MACnD;IACJ;IACA,IAAI,CAAC3B,eAAe,GAAG1B,8BAA8B;EACzD;EACA6D,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAACvC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACkB,gCAAgC,CAAC,CAAC;IACvC,IAAI,CAAC/B,aAAa,CACbqD,OAAO,CAAC,IAAI,CAACzD,WAAW,CAAC,CACzB0D,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACiG,UAAU,CAAC,CAAC,CAChCO,SAAS,CAACC,MAAM,IAAI;MACrB;MACA,IAAI,CAACA,MAAM,EAAE;QACT,IAAI,CAAC3D,OAAO,CAAC4D,GAAG,CAAC,MAAM,IAAI,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MACI,IAAI0B,MAAM,KAAK,UAAU,EAAE;QAC5B,IAAI,CAAC3D,OAAO,CAAC4D,GAAG,CAAC,MAAM,IAAI,CAACjC,IAAI,CAAC,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIkC,WAAWA,CAAA,EAAG;IACV,MAAMC,aAAa,GAAG,IAAI,CAAC/D,WAAW,CAAC+D,aAAa;IACpD;IACA,IAAI,IAAI,CAACZ,kBAAkB,EAAE;MACzBa,YAAY,CAAC,IAAI,CAACb,kBAAkB,CAAC;IACzC;IACA,IAAI,IAAI,CAACzC,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACuD,OAAO,CAAC,CAAC;MAC1B,IAAI,CAACtD,gBAAgB,GAAG,IAAI;IAChC;IACA;IACA,IAAI,CAACuC,iBAAiB,CAACgB,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MAClDL,aAAa,CAACM,mBAAmB,CAACF,KAAK,EAAEC,QAAQ,EAAE3E,sBAAsB,CAAC;IAC9E,CAAC,CAAC;IACF,IAAI,CAACyD,iBAAiB,CAACoB,MAAM,GAAG,CAAC;IACjC,IAAI,CAAClB,UAAU,CAACmB,IAAI,CAAC,CAAC;IACtB,IAAI,CAACnB,UAAU,CAACoB,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACnB,YAAY,GAAG,IAAI;IACxB,IAAI,CAAClD,cAAc,CAACsE,iBAAiB,CAACV,aAAa,EAAE,IAAI,CAAC1B,OAAO,EAAE,SAAS,CAAC;IAC7E,IAAI,CAACjC,aAAa,CAACsE,cAAc,CAACX,aAAa,CAAC;EACpD;EACA;EACAnC,IAAIA,CAAC+C,KAAK,GAAG,IAAI,CAACxF,SAAS,EAAEyE,MAAM,EAAE;IACjC,IAAI,IAAI,CAAC5B,QAAQ,IAAI,CAAC,IAAI,CAACK,OAAO,IAAI,IAAI,CAACS,iBAAiB,CAAC,CAAC,EAAE;MAC5D,IAAI,CAACnC,gBAAgB,EAAEiE,wBAAwB,CAAC,CAAC;MACjD;IACJ;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,cAAc,CAAClB,MAAM,CAAC;IAC9C,IAAI,CAAC7B,OAAO,CAAC,CAAC;IACd,IAAI,CAACnB,OAAO,GACR,IAAI,CAACA,OAAO,IAAI,IAAI/C,eAAe,CAAC,IAAI,CAACsD,iBAAiB,EAAE,IAAI,CAACZ,iBAAiB,CAAC;IACvF,MAAMwE,QAAQ,GAAI,IAAI,CAACpE,gBAAgB,GAAGkE,UAAU,CAACG,MAAM,CAAC,IAAI,CAACpE,OAAO,CAAC,CAACmE,QAAS;IACnFA,QAAQ,CAACE,eAAe,GAAG,IAAI,CAACjF,WAAW,CAAC+D,aAAa;IACzDgB,QAAQ,CAACvC,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAC/CwC,QAAQ,CACHG,WAAW,CAAC,CAAC,CACbxB,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACiG,UAAU,CAAC,CAAC,CAChCO,SAAS,CAAC,MAAM,IAAI,CAAC5B,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAACkB,gBAAgB,CAAC,IAAI,CAACjC,aAAa,CAAC;IACzC,IAAI,CAAC+B,qBAAqB,CAAC,CAAC;IAC5BgC,QAAQ,CAACnD,IAAI,CAAC+C,KAAK,CAAC;EACxB;EACA;EACAzC,IAAIA,CAACyC,KAAK,GAAG,IAAI,CAACvF,SAAS,EAAE;IACzB,MAAM2F,QAAQ,GAAG,IAAI,CAACpE,gBAAgB;IACtC,IAAIoE,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAE;QACtBJ,QAAQ,CAAC7C,IAAI,CAACyC,KAAK,CAAC;MACxB,CAAC,MACI;QACDI,QAAQ,CAACH,wBAAwB,CAAC,CAAC;QACnC,IAAI,CAAC7C,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;EACAqD,MAAMA,CAACxB,MAAM,EAAE;IACX,IAAI,CAACd,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACZ,IAAI,CAAC,CAAC,GAAG,IAAI,CAACN,IAAI,CAACyD,SAAS,EAAEzB,MAAM,CAAC;EACzE;EACA;EACAd,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC,IAAI,CAACnC,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAACwE,SAAS,CAAC,CAAC;EACvE;EACA;EACAL,cAAcA,CAAClB,MAAM,EAAE;IACnB,IAAI,IAAI,CAAClD,WAAW,EAAE;MAClB,MAAM4E,gBAAgB,GAAG,IAAI,CAAC5E,WAAW,CAAC6E,SAAS,CAAC,CAAC,CAChDC,gBAAgB;MACrB,IAAI,CAAC,CAAC,IAAI,CAAC1D,gBAAgB,IAAI,CAAC8B,MAAM,KAAK0B,gBAAgB,CAACG,OAAO,YAAY3J,UAAU,EAAE;QACvF,OAAO,IAAI,CAAC4E,WAAW;MAC3B;MACA,IAAI,CAACqB,OAAO,CAAC,CAAC;IAClB;IACA,MAAM2D,mBAAmB,GAAG,IAAI,CAACpF,SAAS,CACrCqF,GAAG,CAAC3I,gBAAgB,CAAC,CACrB4I,2BAA2B,CAAC,IAAI,CAAC5F,WAAW,CAAC;IAClD,MAAMvB,OAAO,GAAG,IAAI,CAAC6B,SAAS,CAACqF,GAAG,CAAC5I,OAAO,CAAC;IAC3C;IACA,MAAM8I,QAAQ,GAAGpH,OAAO,CACnBL,QAAQ,CAAC,CAAC,CACV0H,mBAAmB,CAAC,IAAI,CAAChE,gBAAgB,GAAG8B,MAAM,IAAI,IAAI,CAAC5D,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAC1F+F,qBAAqB,CAAC,IAAI,IAAI,CAACxE,eAAe,UAAU,CAAC,CACzDyE,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,kBAAkB,CAAC,IAAI,CAAC5E,eAAe,CAAC,CACxC6E,wBAAwB,CAACR,mBAAmB,CAAC;IAClDG,QAAQ,CAACM,eAAe,CAACzC,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACiG,UAAU,CAAC,CAAC,CAACO,SAAS,CAACyC,MAAM,IAAI;MAC1E,IAAI,CAACC,2BAA2B,CAACD,MAAM,CAACE,cAAc,CAAC;MACvD,IAAI,IAAI,CAAC3F,gBAAgB,EAAE;QACvB,IAAIyF,MAAM,CAACG,wBAAwB,CAACC,gBAAgB,IAAI,IAAI,CAAC7F,gBAAgB,CAACwE,SAAS,CAAC,CAAC,EAAE;UACvF;UACA;UACA,IAAI,CAAClF,OAAO,CAAC4D,GAAG,CAAC,MAAM,IAAI,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACxB,WAAW,GAAGjC,OAAO,CAACgI,MAAM,CAAC;MAC9BC,SAAS,EAAE,IAAI,CAACrG,IAAI;MACpBmF,gBAAgB,EAAEK,QAAQ;MAC1Bc,UAAU,EAAE,GAAG,IAAI,CAACpF,eAAe,IAAI/B,WAAW,EAAE;MACpDoH,cAAc,EAAE,IAAI,CAACtG,SAAS,CAACqF,GAAG,CAACrH,2BAA2B,CAAC,CAAC;IACpE,CAAC,CAAC;IACF,IAAI,CAACqD,eAAe,CAAC,IAAI,CAACjB,WAAW,CAAC;IACtC,IAAI,CAACA,WAAW,CACXmG,WAAW,CAAC,CAAC,CACbnD,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACiG,UAAU,CAAC,CAAC,CAChCO,SAAS,CAAC,MAAM,IAAI,CAAC5B,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAACrB,WAAW,CACXoG,oBAAoB,CAAC,CAAC,CACtBpD,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACiG,UAAU,CAAC,CAAC,CAChCO,SAAS,CAAC,MAAM,IAAI,CAAChD,gBAAgB,EAAEoG,sBAAsB,CAAC,CAAC,CAAC;IACrE,IAAI,CAACrG,WAAW,CACXsG,aAAa,CAAC,CAAC,CACftD,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACiG,UAAU,CAAC,CAAC,CAChCO,SAAS,CAACQ,KAAK,IAAI;MACpB,IAAI,IAAI,CAACrB,iBAAiB,CAAC,CAAC,IAAIqB,KAAK,CAAC8C,OAAO,KAAK3J,MAAM,IAAI,CAACC,cAAc,CAAC4G,KAAK,CAAC,EAAE;QAChFA,KAAK,CAAC+C,cAAc,CAAC,CAAC;QACtB/C,KAAK,CAACgD,eAAe,CAAC,CAAC;QACvB,IAAI,CAAClH,OAAO,CAAC4D,GAAG,CAAC,MAAM,IAAI,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAAC1B,eAAe,EAAE4G,2BAA2B,EAAE;MACnD,IAAI,CAAC1G,WAAW,CAAC2G,aAAa,CAAC,GAAG,IAAI,CAAC9F,eAAe,gCAAgC,CAAC;IAC3F;IACA,IAAI,CAAC,IAAI,CAACE,cAAc,EAAE;MACtB,IAAI,CAACA,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACpB,IAAI,CAAC+F,MAAM,CAAC1C,IAAI,CAACvG,SAAS,CAAC,IAAI,CAACiG,UAAU,CAAC,CAAC,CAACO,SAAS,CAAC,MAAM;QAC9D,IAAI,IAAI,CAACjD,WAAW,EAAE;UAClB,IAAI,CAACiB,eAAe,CAAC,IAAI,CAACjB,WAAW,CAAC;QAC1C;MACJ,CAAC,CAAC;IACN;IACA,OAAO,IAAI,CAACA,WAAW;EAC3B;EACA;EACAqB,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACrB,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC4G,WAAW,CAAC,CAAC,EAAE;MACpD,IAAI,CAAC5G,WAAW,CAAC6G,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC5G,gBAAgB,GAAG,IAAI;EAChC;EACA;EACAgB,eAAeA,CAACkD,UAAU,EAAE;IACxB,MAAMzG,QAAQ,GAAGyG,UAAU,CAACU,SAAS,CAAC,CAAC,CAACC,gBAAgB;IACxD,MAAM5B,MAAM,GAAG,IAAI,CAAC4D,UAAU,CAAC,CAAC;IAChC,MAAM/I,OAAO,GAAG,IAAI,CAACgJ,mBAAmB,CAAC,CAAC;IAC1CrJ,QAAQ,CAACsJ,aAAa,CAAC,CACnB,IAAI,CAACC,UAAU,CAAC;MAAE,GAAG/D,MAAM,CAACgE,IAAI;MAAE,GAAGnJ,OAAO,CAACmJ;IAAK,CAAC,CAAC,EACpD,IAAI,CAACD,UAAU,CAAC;MAAE,GAAG/D,MAAM,CAACiE,QAAQ;MAAE,GAAGpJ,OAAO,CAACoJ;IAAS,CAAC,CAAC,CAC/D,CAAC;EACN;EACA;EACAF,UAAUA,CAACvJ,QAAQ,EAAE;IACjB,MAAM0J,MAAM,GAAGlI,oBAAoB;IACnC,MAAMmI,KAAK,GAAG,CAAC,IAAI,CAAC1H,IAAI,IAAI,IAAI,CAACA,IAAI,CAACqB,KAAK,IAAI,KAAK;IACpD,IAAItD,QAAQ,CAAC4J,OAAO,KAAK,KAAK,EAAE;MAC5B5J,QAAQ,CAAC6J,OAAO,GAAG,CAACH,MAAM;IAC9B,CAAC,MACI,IAAI1J,QAAQ,CAAC4J,OAAO,KAAK,QAAQ,EAAE;MACpC5J,QAAQ,CAAC6J,OAAO,GAAGH,MAAM;IAC7B,CAAC,MACI,IAAI1J,QAAQ,CAAC8J,OAAO,KAAK,OAAO,EAAE;MACnC9J,QAAQ,CAAC+J,OAAO,GAAGJ,KAAK,GAAG,CAACD,MAAM,GAAGA,MAAM;IAC/C,CAAC,MACI,IAAI1J,QAAQ,CAAC8J,OAAO,KAAK,KAAK,EAAE;MACjC9J,QAAQ,CAAC+J,OAAO,GAAGJ,KAAK,GAAGD,MAAM,GAAG,CAACA,MAAM;IAC/C;IACA,OAAO1J,QAAQ;EACnB;EACA;AACJ;AACA;AACA;EACIoJ,UAAUA,CAAA,EAAG;IACT,MAAMO,KAAK,GAAG,CAAC,IAAI,CAAC1H,IAAI,IAAI,IAAI,CAACA,IAAI,CAACqB,KAAK,IAAI,KAAK;IACpD,MAAMtD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIgK,cAAc;IAClB,IAAIhK,QAAQ,IAAI,OAAO,IAAIA,QAAQ,IAAI,OAAO,EAAE;MAC5CgK,cAAc,GAAG;QAAEF,OAAO,EAAE,QAAQ;QAAEF,OAAO,EAAE5J,QAAQ,IAAI,OAAO,GAAG,KAAK,GAAG;MAAS,CAAC;IAC3F,CAAC,MACI,IAAIA,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAI2J,KAAM,IAC5B3J,QAAQ,IAAI,OAAO,IAAI,CAAC2J,KAAM,EAAE;MACjCK,cAAc,GAAG;QAAEF,OAAO,EAAE,OAAO;QAAEF,OAAO,EAAE;MAAS,CAAC;IAC5D,CAAC,MACI,IAAI5J,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAI2J,KAAM,IAC7B3J,QAAQ,IAAI,MAAM,IAAI,CAAC2J,KAAM,EAAE;MAChCK,cAAc,GAAG;QAAEF,OAAO,EAAE,KAAK;QAAEF,OAAO,EAAE;MAAS,CAAC;IAC1D,CAAC,MACI,IAAI,OAAOK,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMlK,iCAAiC,CAACC,QAAQ,CAAC;IACrD;IACA,MAAM;MAAEkK,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACJ,cAAc,CAACF,OAAO,EAAEE,cAAc,CAACJ,OAAO,CAAC;IACrF,OAAO;MACHJ,IAAI,EAAEQ,cAAc;MACpBP,QAAQ,EAAE;QAAEK,OAAO,EAAEI,CAAC;QAAEN,OAAO,EAAEO;MAAE;IACvC,CAAC;EACL;EACA;EACAd,mBAAmBA,CAAA,EAAG;IAClB,MAAMM,KAAK,GAAG,CAAC,IAAI,CAAC1H,IAAI,IAAI,IAAI,CAACA,IAAI,CAACqB,KAAK,IAAI,KAAK;IACpD,MAAMtD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIqK,eAAe;IACnB,IAAIrK,QAAQ,IAAI,OAAO,EAAE;MACrBqK,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAChE,CAAC,MACI,IAAIvK,QAAQ,IAAI,OAAO,EAAE;MAC1BqK,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAM,CAAC;IAC7D,CAAC,MACI,IAAIvK,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAI2J,KAAM,IAC5B3J,QAAQ,IAAI,OAAO,IAAI,CAAC2J,KAAM,EAAE;MACjCU,eAAe,GAAG;QAAEC,QAAQ,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC7D,CAAC,MACI,IAAIvK,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAI2J,KAAM,IAC7B3J,QAAQ,IAAI,MAAM,IAAI,CAAC2J,KAAM,EAAE;MAChCU,eAAe,GAAG;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC/D,CAAC,MACI,IAAI,OAAON,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMlK,iCAAiC,CAACC,QAAQ,CAAC;IACrD;IACA,MAAM;MAAEkK,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACC,eAAe,CAACC,QAAQ,EAAED,eAAe,CAACE,QAAQ,CAAC;IACzF,OAAO;MACHf,IAAI,EAAEa,eAAe;MACrBZ,QAAQ,EAAE;QAAEa,QAAQ,EAAEJ,CAAC;QAAEK,QAAQ,EAAEJ;MAAE;IACzC,CAAC;EACL;EACA;EACAxF,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,IAAI,CAACpC,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAC0B,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5C,IAAI,CAAC1B,gBAAgB,CAACiI,aAAa,CAAC,CAAC;MACrC1M,eAAe,CAAC,MAAM;QAClB,IAAI,IAAI,CAACyE,gBAAgB,EAAE;UACvB,IAAI,CAACD,WAAW,CAACmB,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,EAAE;QACCgH,QAAQ,EAAE,IAAI,CAACvI;MACnB,CAAC,CAAC;IACN;EACJ;EACA;EACA2C,gBAAgBA,CAACD,YAAY,EAAE;IAC3B,IAAI,IAAI,CAACrC,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACqC,YAAY,GAAGA,YAAY;MACjD,IAAI,CAACrC,gBAAgB,CAACiI,aAAa,CAAC,CAAC;IACzC;EACJ;EACA;EACAJ,eAAeA,CAACF,CAAC,EAAEC,CAAC,EAAE;IAClB,IAAI,IAAI,CAACnK,QAAQ,KAAK,OAAO,IAAI,IAAI,CAACA,QAAQ,KAAK,OAAO,EAAE;MACxD,IAAImK,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,QAAQ;MAChB,CAAC,MACI,IAAIA,CAAC,KAAK,QAAQ,EAAE;QACrBA,CAAC,GAAG,KAAK;MACb;IACJ,CAAC,MACI;MACD,IAAID,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,OAAO;MACf,CAAC,MACI,IAAIA,CAAC,KAAK,OAAO,EAAE;QACpBA,CAAC,GAAG,KAAK;MACb;IACJ;IACA,OAAO;MAAEA,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACAlC,2BAA2BA,CAACC,cAAc,EAAE;IACxC,MAAM;MAAEqC,QAAQ;MAAET,OAAO;MAAEF;IAAQ,CAAC,GAAG1B,cAAc;IACrD,IAAIwC,WAAW;IACf;IACA;IACA,IAAIH,QAAQ,KAAK,QAAQ,EAAE;MACvB;MACA;MACA;MACA,IAAI,IAAI,CAACtI,IAAI,IAAI,IAAI,CAACA,IAAI,CAACqB,KAAK,KAAK,KAAK,EAAE;QACxCoH,WAAW,GAAGZ,OAAO,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;MACtD,CAAC,MACI;QACDY,WAAW,GAAGZ,OAAO,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;MACxD;IACJ,CAAC,MACI;MACDY,WAAW,GAAGH,QAAQ,KAAK,QAAQ,IAAIX,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;IAChF;IACA,IAAIc,WAAW,KAAK,IAAI,CAACxH,gBAAgB,EAAE;MACvC,MAAMuD,UAAU,GAAG,IAAI,CAACnE,WAAW;MACnC,IAAImE,UAAU,EAAE;QACZ,MAAMkE,WAAW,GAAG,GAAG,IAAI,CAACxH,eAAe,IAAI/B,WAAW,GAAG;QAC7DqF,UAAU,CAACmE,gBAAgB,CAACD,WAAW,GAAG,IAAI,CAACzH,gBAAgB,CAAC;QAChEuD,UAAU,CAACwC,aAAa,CAAC0B,WAAW,GAAGD,WAAW,CAAC;MACvD;MACA,IAAI,CAACxH,gBAAgB,GAAGwH,WAAW;IACvC;EACJ;EACA;EACA3G,gCAAgCA,CAAA,EAAG;IAC/B;IACA,IAAI,IAAI,CAACpB,SAAS,IACd,CAAC,IAAI,CAACsB,OAAO,IACb,CAAC,IAAI,CAACpB,gBAAgB,IACtB,IAAI,CAACiC,iBAAiB,CAACoB,MAAM,EAAE;MAC/B;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAAC2E,4BAA4B,CAAC,CAAC,EAAE;MACrC,IAAI,CAAC/F,iBAAiB,CAACgG,IAAI,CAAC,CACxB,YAAY,EACZ/E,KAAK,IAAI;QACL,IAAI,CAACgF,+BAA+B,CAAC,CAAC;QACtC,IAAIC,KAAK,GAAG/D,SAAS;QACrB,IAAIlB,KAAK,CAACmE,CAAC,KAAKjD,SAAS,IAAIlB,KAAK,CAACoE,CAAC,KAAKlD,SAAS,EAAE;UAChD+D,KAAK,GAAGjF,KAAK;QACjB;QACA,IAAI,CAACvC,IAAI,CAACyD,SAAS,EAAE+D,KAAK,CAAC;MAC/B,CAAC,CACJ,CAAC;IACN,CAAC,MACI,IAAI,IAAI,CAAC3G,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAAC4G,iCAAiC,CAAC,CAAC;MACxC,IAAI,CAACnG,iBAAiB,CAACgG,IAAI,CAAC,CACxB,YAAY,EACZ/E,KAAK,IAAI;QACL,MAAMmF,KAAK,GAAGnF,KAAK,CAACoF,aAAa,GAAG,CAAC,CAAC;QACtC,MAAM3F,MAAM,GAAG0F,KAAK,GAAG;UAAEhB,CAAC,EAAEgB,KAAK,CAACE,OAAO;UAAEjB,CAAC,EAAEe,KAAK,CAACG;QAAQ,CAAC,GAAGpE,SAAS;QACzE;QACA;QACA,IAAI,CAAC8D,+BAA+B,CAAC,CAAC;QACtC,IAAI,IAAI,CAAChG,kBAAkB,EAAE;UACzBa,YAAY,CAAC,IAAI,CAACb,kBAAkB,CAAC;QACzC;QACA,MAAMuG,uBAAuB,GAAG,GAAG;QACnC,IAAI,CAACvG,kBAAkB,GAAGwG,UAAU,CAAC,MAAM;UACvC,IAAI,CAACxG,kBAAkB,GAAG,IAAI;UAC9B,IAAI,CAACvB,IAAI,CAACyD,SAAS,EAAEzB,MAAM,CAAC;QAChC,CAAC,EAAE,IAAI,CAACpD,eAAe,EAAEoJ,uBAAuB,IAAIF,uBAAuB,CAAC;MAChF,CAAC,CACJ,CAAC;IACN;IACA,IAAI,CAACG,aAAa,CAAC,IAAI,CAAC3G,iBAAiB,CAAC;EAC9C;EACAiG,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACjI,6BAA6B,EAAE;MACpC;IACJ;IACA,IAAI,CAACA,6BAA6B,GAAG,IAAI;IACzC,MAAM4I,aAAa,GAAG,EAAE;IACxB,IAAI,IAAI,CAACb,4BAA4B,CAAC,CAAC,EAAE;MACrCa,aAAa,CAACZ,IAAI,CAAC,CACf,YAAY,EACZ/E,KAAK,IAAI;QACL,MAAM4F,SAAS,GAAG5F,KAAK,CAAC6F,aAAa;QACrC,IAAI,CAACD,SAAS,IAAI,CAAC,IAAI,CAACrJ,WAAW,EAAEuJ,cAAc,CAACC,QAAQ,CAACH,SAAS,CAAC,EAAE;UACrE,IAAI,CAAC7H,IAAI,CAAC,CAAC;QACf;MACJ,CAAC,CACJ,EAAE,CAAC,OAAO,EAAEiC,KAAK,IAAI,IAAI,CAACgG,cAAc,CAAChG,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC,MACI,IAAI,IAAI,CAAC1B,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAAC4G,iCAAiC,CAAC,CAAC;MACxC,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;QAC3B,IAAI,IAAI,CAACjH,kBAAkB,EAAE;UACzBa,YAAY,CAAC,IAAI,CAACb,kBAAkB,CAAC;QACzC;QACA,IAAI,CAACjB,IAAI,CAAC,IAAI,CAAC1B,eAAe,EAAEnB,iBAAiB,CAAC;MACtD,CAAC;MACDyK,aAAa,CAACZ,IAAI,CAAC,CAAC,UAAU,EAAEkB,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAEA,gBAAgB,CAAC,CAAC;IACzF;IACA,IAAI,CAACP,aAAa,CAACC,aAAa,CAAC;IACjC,IAAI,CAAC5G,iBAAiB,CAACgG,IAAI,CAAC,GAAGY,aAAa,CAAC;EACjD;EACAD,aAAaA,CAACQ,SAAS,EAAE;IACrBA,SAAS,CAACnG,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MACrC,IAAI,CAACpE,WAAW,CAAC+D,aAAa,CAACuG,gBAAgB,CAACnG,KAAK,EAAEC,QAAQ,EAAE3E,sBAAsB,CAAC;IAC5F,CAAC,CAAC;EACN;EACAwJ,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,CAAC,IAAI,CAAC/I,SAAS,CAACqK,GAAG,IAAI,CAAC,IAAI,CAACrK,SAAS,CAACsK,OAAO;EACzD;EACA;EACAL,cAAcA,CAAChG,KAAK,EAAE;IAClB,IAAI,IAAI,CAACrB,iBAAiB,CAAC,CAAC,EAAE;MAC1B,MAAM2H,mBAAmB,GAAG,IAAI,CAACnK,SAAS,CACrCqF,GAAG,CAACnI,QAAQ,CAAC,CACbkN,gBAAgB,CAACvG,KAAK,CAACqF,OAAO,EAAErF,KAAK,CAACsF,OAAO,CAAC;MACnD,MAAMkB,OAAO,GAAG,IAAI,CAAC3K,WAAW,CAAC+D,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAI0G,mBAAmB,KAAKE,OAAO,IAAI,CAACA,OAAO,CAACT,QAAQ,CAACO,mBAAmB,CAAC,EAAE;QAC3E,IAAI,CAACvI,IAAI,CAAC,CAAC;MACf;IACJ;EACJ;EACA;EACAmH,iCAAiCA,CAAA,EAAG;IAChC,MAAMuB,QAAQ,GAAG,IAAI,CAACnI,aAAa;IACnC,IAAImI,QAAQ,KAAK,KAAK,EAAE;MACpB,MAAMD,OAAO,GAAG,IAAI,CAAC3K,WAAW,CAAC+D,aAAa;MAC9C,MAAM8G,KAAK,GAAGF,OAAO,CAACE,KAAK;MAC3B;MACA;MACA,IAAID,QAAQ,KAAK,IAAI,IAAKD,OAAO,CAACG,QAAQ,KAAK,OAAO,IAAIH,OAAO,CAACG,QAAQ,KAAK,UAAW,EAAE;QACxFD,KAAK,CAACE,UAAU,GACZF,KAAK,CAACG,YAAY,GACdH,KAAK,CAACI,gBAAgB,GAClBJ,KAAK,CAACK,aAAa,GACf,MAAM;MAC1B;MACA;MACA;MACA,IAAIN,QAAQ,KAAK,IAAI,IAAI,CAACD,OAAO,CAACQ,SAAS,EAAE;QACzCN,KAAK,CAACO,cAAc,GAAG,MAAM;MACjC;MACAP,KAAK,CAACQ,WAAW,GAAG,MAAM;MAC1BR,KAAK,CAACS,uBAAuB,GAAG,aAAa;IACjD;EACJ;EACA;EACAlJ,oBAAoBA,CAACO,UAAU,EAAE;IAC7B,IAAI,IAAI,CAACnB,uBAAuB,EAAE;MAC9B;IACJ;IACA,IAAI,CAACA,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACrB,cAAc,CAACsE,iBAAiB,CAAC,IAAI,CAACzE,WAAW,CAAC+D,aAAa,EAAEpB,UAAU,EAAE,SAAS,CAAC;IAC5F;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACU,YAAY,EAAE;MACpBnH,eAAe,CAAC;QACZqP,KAAK,EAAEA,CAAA,KAAM;UACT,IAAI,CAAC/J,uBAAuB,GAAG,KAAK;UACpC,IAAI,IAAI,CAACa,OAAO,IAAI,CAAC,IAAI,CAACL,QAAQ,EAAE;YAChC,IAAI,CAAC7B,cAAc,CAACqL,QAAQ,CAAC,IAAI,CAACxL,WAAW,CAAC+D,aAAa,EAAE,IAAI,CAAC1B,OAAO,EAAE,SAAS,CAAC;UACzF;QACJ;MACJ,CAAC,EAAE;QAAEwG,QAAQ,EAAE,IAAI,CAACvI;MAAU,CAAC,CAAC;IACpC;EACJ;EACA,OAAOmL,IAAI,YAAAC,mBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF5L,UAAU;EAAA;EAC7G,OAAO6L,IAAI,kBAD8EjQ,EAAE,CAAAkQ,iBAAA;IAAAC,IAAA,EACJ/L,UAAU;IAAAgM,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADRzQ,EAAE,CAAA2Q,WAAA,6BAAAD,GAAA,CAAArK,QACK,CAAC;MAAA;IAAA;IAAAuK,MAAA;MAAAnO,QAAA;MAAA0D,gBAAA;MAAAE,QAAA;MAAA7C,SAAA;MAAAC,SAAA;MAAAqD,aAAA;MAAAJ,OAAA;MAAAW,YAAA;IAAA;IAAAwJ,QAAA;EAAA;AACrG;AACA;EAAA,QAAAnE,SAAA,oBAAAA,SAAA,KAH6F1M,EAAE,CAAA8Q,iBAAA,CAGJ1M,UAAU,EAAc,CAAC;IACxG+L,IAAI,EAAE3P,SAAS;IACfuQ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBH,QAAQ,EAAE,YAAY;MACtBI,IAAI,EAAE;QACF,OAAO,EAAE,yBAAyB;QAClC,kCAAkC,EAAE;MACxC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAExO,QAAQ,EAAE,CAAC;MACnD0N,IAAI,EAAE1P,KAAK;MACXsQ,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE5K,gBAAgB,EAAE,CAAC;MACnBgK,IAAI,EAAE1P,KAAK;MACXsQ,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAE1K,QAAQ,EAAE,CAAC;MACX8J,IAAI,EAAE1P,KAAK;MACXsQ,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEvN,SAAS,EAAE,CAAC;MACZ2M,IAAI,EAAE1P,KAAK;MACXsQ,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEtN,SAAS,EAAE,CAAC;MACZ0M,IAAI,EAAE1P,KAAK;MACXsQ,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEjK,aAAa,EAAE,CAAC;MAChBqJ,IAAI,EAAE1P,KAAK;MACXsQ,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAErK,OAAO,EAAE,CAAC;MACVyJ,IAAI,EAAE1P,KAAK;MACXsQ,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE1J,YAAY,EAAE,CAAC;MACf8I,IAAI,EAAE1P,KAAK;MACXsQ,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMtL,gBAAgB,CAAC;EACnByL,kBAAkB,GAAGhR,MAAM,CAACQ,iBAAiB,CAAC;EAC9C2D,WAAW,GAAGnE,MAAM,CAACC,UAAU,CAAC;EAChC;EACAgR,YAAY,GAAG,KAAK;EACpB;EACAzK,OAAO;EACP;EACAW,YAAY;EACZ;EACA+J,cAAc;EACd;EACAC,cAAc;EACd;EACA/H,eAAe;EACf;EACAzC,oBAAoB;EACpB;EACAyK,mBAAmB;EACnB;EACAC,QAAQ;EACR;EACAC,mBAAmB,GAAG,KAAK;EAC3B;EACAC,UAAU,GAAG,KAAK;EAClB;EACAC,OAAO,GAAG,IAAIvP,OAAO,CAAC,CAAC;EACvB;EACAwP,cAAc,GAAG,sBAAsB;EACvC;EACAC,cAAc,GAAG,sBAAsB;EACvCjK,WAAWA,CAAA,EAAG;IACV,MAAMkK,aAAa,GAAG3R,MAAM,CAACS,qBAAqB,EAAE;MAAEmE,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAACwM,mBAAmB,GAAGO,aAAa,KAAK,gBAAgB;EACjE;EACA;AACJ;AACA;AACA;EACI5L,IAAIA,CAAC+C,KAAK,EAAE;IACR;IACA,IAAI,IAAI,CAACqI,cAAc,IAAI,IAAI,EAAE;MAC7BhJ,YAAY,CAAC,IAAI,CAACgJ,cAAc,CAAC;IACrC;IACA,IAAI,CAACD,cAAc,GAAGpD,UAAU,CAAC,MAAM;MACnC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACV,cAAc,GAAG1H,SAAS;IACnC,CAAC,EAAEV,KAAK,CAAC;EACb;EACA;AACJ;AACA;AACA;EACIzC,IAAIA,CAACyC,KAAK,EAAE;IACR;IACA,IAAI,IAAI,CAACoI,cAAc,IAAI,IAAI,EAAE;MAC7B/I,YAAY,CAAC,IAAI,CAAC+I,cAAc,CAAC;IACrC;IACA,IAAI,CAACC,cAAc,GAAGrD,UAAU,CAAC,MAAM;MACnC,IAAI,CAAC8D,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAACT,cAAc,GAAG3H,SAAS;IACnC,CAAC,EAAEV,KAAK,CAAC;EACb;EACA;EACAO,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmI,OAAO;EACvB;EACA;EACAlI,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACiI,UAAU;EAC1B;EACAtJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACc,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACyI,OAAO,CAAC7I,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACS,eAAe,GAAG,IAAI;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACI8B,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACoG,mBAAmB,EAAE;MAC1B,IAAI,CAACjL,IAAI,CAAC,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI0G,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACiE,kBAAkB,CAACa,YAAY,CAAC,CAAC;EAC1C;EACAC,iBAAiBA,CAAC;IAAE3D;EAAc,CAAC,EAAE;IACjC,IAAI,CAACA,aAAa,IAAI,CAAC,IAAI,CAAC/E,eAAe,CAACiF,QAAQ,CAACF,aAAa,CAAC,EAAE;MACjE,IAAI,IAAI,CAAC7E,SAAS,CAAC,CAAC,EAAE;QAClB,IAAI,CAACjD,IAAI,CAAC,IAAI,CAACM,oBAAoB,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACoL,kBAAkB,CAAC,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACf,YAAY,GAAG,IAAI,CAACgB,mBAAmB,CAAC,CAAC;IAC9C,IAAI,CAAClF,aAAa,CAAC,CAAC;EACxB;EACA;EACAkF,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,IAAI,GAAG,IAAI,CAAC/N,WAAW,CAAC+D,aAAa,CAACiK,qBAAqB,CAAC,CAAC;IACnE,OAAOD,IAAI,CAACE,MAAM,GAAGpO,UAAU,IAAIkO,IAAI,CAACG,KAAK,IAAIpO,SAAS;EAC9D;EACA;EACAqO,mBAAmBA,CAAC;IAAEC;EAAc,CAAC,EAAE;IACnC,IAAIA,aAAa,KAAK,IAAI,CAACd,cAAc,IAAIc,aAAa,KAAK,IAAI,CAACb,cAAc,EAAE;MAChF,IAAI,CAACK,kBAAkB,CAACQ,aAAa,KAAK,IAAI,CAACd,cAAc,CAAC;IAClE;EACJ;EACA;EACA1I,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACmI,cAAc,IAAI,IAAI,EAAE;MAC7B/I,YAAY,CAAC,IAAI,CAAC+I,cAAc,CAAC;IACrC;IACA,IAAI,IAAI,CAACC,cAAc,IAAI,IAAI,EAAE;MAC7BhJ,YAAY,CAAC,IAAI,CAACgJ,cAAc,CAAC;IACrC;IACA,IAAI,CAACD,cAAc,GAAG,IAAI,CAACC,cAAc,GAAG3H,SAAS;EACzD;EACA;EACAuI,kBAAkBA,CAACS,SAAS,EAAE;IAC1B,IAAIA,SAAS,EAAE;MACX,IAAI,CAAClB,mBAAmB,GAAG,IAAI;IACnC,CAAC,MACI,IAAI,CAAC,IAAI,CAAChI,SAAS,CAAC,CAAC,EAAE;MACxB,IAAI,CAACkI,OAAO,CAAC9I,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACAkJ,iBAAiBA,CAACtI,SAAS,EAAE;IACzB;IACA;IACA;IACA,MAAMmJ,OAAO,GAAG,IAAI,CAACpB,QAAQ,CAACnJ,aAAa;IAC3C,MAAMwK,SAAS,GAAG,IAAI,CAACjB,cAAc;IACrC,MAAMkB,SAAS,GAAG,IAAI,CAACjB,cAAc;IACrCe,OAAO,CAACG,SAAS,CAACC,MAAM,CAACvJ,SAAS,GAAGqJ,SAAS,GAAGD,SAAS,CAAC;IAC3DD,OAAO,CAACG,SAAS,CAACE,GAAG,CAACxJ,SAAS,GAAGoJ,SAAS,GAAGC,SAAS,CAAC;IACxD,IAAI,IAAI,CAACpB,UAAU,KAAKjI,SAAS,EAAE;MAC/B,IAAI,CAACiI,UAAU,GAAGjI,SAAS;MAC3B,IAAI,CAAC0H,kBAAkB,CAACa,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAIvI,SAAS,IAAI,CAAC,IAAI,CAAC8H,mBAAmB,IAAI,OAAO2B,gBAAgB,KAAK,UAAU,EAAE;MAClF,MAAMC,MAAM,GAAGD,gBAAgB,CAACN,OAAO,CAAC;MACxC;MACA,IAAIO,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,IAAI,IACtDD,MAAM,CAACC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,MAAM,EAAE;QACtD,IAAI,CAAC7B,mBAAmB,GAAG,IAAI;MACnC;IACJ;IACA,IAAI9H,SAAS,EAAE;MACX,IAAI,CAAC0I,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,IAAI,CAACZ,mBAAmB,EAAE;MAC1BqB,OAAO,CAACG,SAAS,CAACE,GAAG,CAAC,yBAAyB,CAAC;MAChD,IAAI,CAACf,kBAAkB,CAACzI,SAAS,CAAC;IACtC;EACJ;EACA,OAAOsG,IAAI,YAAAsD,yBAAApD,iBAAA;IAAA,YAAAA,iBAAA,IAAwFvK,gBAAgB;EAAA;EACnH,OAAO4N,IAAI,kBA1N8ErT,EAAE,CAAAsT,iBAAA;IAAAnD,IAAA,EA0NJ1K,gBAAgB;IAAA2K,SAAA;IAAAmD,SAAA,WAAAC,uBAAA/C,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA1NdzQ,EAAE,CAAAyT,WAAA,CAAAnR,GAAA;MAAA;MAAA,IAAAmO,EAAA;QAAA,IAAAiD,EAAA;QAAF1T,EAAE,CAAA2T,cAAA,CAAAD,EAAA,GAAF1T,EAAE,CAAA4T,WAAA,QAAAlD,GAAA,CAAAa,QAAA,GAAAmC,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAxD,SAAA,kBA0N0G,MAAM;IAAAE,YAAA,WAAAuD,8BAAArD,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA1NlHzQ,EAAE,CAAA+T,UAAA,wBAAAC,+CAAAC,MAAA;UAAA,OA0NJvD,GAAA,CAAAsB,iBAAA,CAAAiC,MAAwB,CAAC;QAAA,CAAV,CAAC;MAAA;IAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAA7D,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA8D,GAAA,GA1NdvU,EAAE,CAAAwU,gBAAA;QAAFxU,EAAE,CAAAyU,cAAA,eA0Nwf,CAAC;QA1N3fzU,EAAE,CAAA+T,UAAA,0BAAAW,sDAAAT,MAAA;UAAFjU,EAAE,CAAA2U,aAAA,CAAAJ,GAAA;UAAA,OAAFvU,EAAE,CAAA4U,WAAA,CA0NwalE,GAAA,CAAA8B,mBAAA,CAAAyB,MAA0B,CAAC;QAAA,CAAC,CAAC;QA1NvcjU,EAAE,CAAAyU,cAAA,YA0NwjB,CAAC;QA1N3jBzU,EAAE,CAAA6U,MAAA,EA0NmkB,CAAC;QA1NtkB7U,EAAE,CAAA8U,YAAA,CA0NykB,CAAC,CAAO,CAAC;MAAA;MAAA,IAAArE,EAAA;QA1NplBzQ,EAAE,CAAA2Q,WAAA,2BAAAD,GAAA,CAAAS,YA0Nuf,CAAC;QA1N1fnR,EAAE,CAAA+U,UAAA,YAAArE,GAAA,CAAArJ,YA0NkZ,CAAC;QA1NrZrH,EAAE,CAAAgV,SAAA,EA0NmkB,CAAC;QA1NtkBhV,EAAE,CAAAiV,iBAAA,CAAAvE,GAAA,CAAAhK,OA0NmkB,CAAC;MAAA;IAAA;IAAAwO,YAAA,GAAo2EpT,OAAO;IAAAoR,MAAA;IAAAiC,aAAA;IAAAC,eAAA;EAAA;AAC9gG;AACA;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KA5N6F1M,EAAE,CAAA8Q,iBAAA,CA4NJrL,gBAAgB,EAAc,CAAC;IAC9G0K,IAAI,EAAEvP,SAAS;IACfmQ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,uBAAuB;MAAEmE,aAAa,EAAEtU,iBAAiB,CAACwU,IAAI;MAAED,eAAe,EAAEtU,uBAAuB,CAACwU,MAAM;MAAErE,IAAI,EAAE;QAC9H,cAAc,EAAE,2BAA2B;QAC3C,aAAa,EAAE;MACnB,CAAC;MAAEsE,OAAO,EAAE,CAACzT,OAAO,CAAC;MAAEuS,QAAQ,EAAE,0RAA0R;MAAEnB,MAAM,EAAE,CAAC,2xEAA2xE;IAAE,CAAC;EAChnF,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE3B,QAAQ,EAAE,CAAC;MACnDpB,IAAI,EAAEpP,SAAS;MACfgQ,IAAI,EAAE,CAAC,SAAS,EAAE;QACV;QACA;QACAyE,MAAM,EAAE;MACZ,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMC,gBAAgB,CAAC;EACnB,OAAO3F,IAAI,YAAA4F,yBAAA1F,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyF,gBAAgB;EAAA;EACnH,OAAOE,IAAI,kBA7O8E3V,EAAE,CAAA4V,gBAAA;IAAAzF,IAAA,EA6OSsF;EAAgB;EACpH,OAAOI,IAAI,kBA9O8E7V,EAAE,CAAA8V,gBAAA;IAAAC,SAAA,EA8OsC,CAAC5S,4CAA4C,CAAC;IAAAoS,OAAA,GAAYpU,UAAU,EAAEG,aAAa,EAAEe,eAAe,EAAEA,eAAe,EAAEd,mBAAmB;EAAA;AAC/Q;AACA;EAAA,QAAAmL,SAAA,oBAAAA,SAAA,KAhP6F1M,EAAE,CAAA8Q,iBAAA,CAgPJ2E,gBAAgB,EAAc,CAAC;IAC9GtF,IAAI,EAAEnP,QAAQ;IACd+P,IAAI,EAAE,CAAC;MACCwE,OAAO,EAAE,CAACpU,UAAU,EAAEG,aAAa,EAAEe,eAAe,EAAE+B,UAAU,EAAEqB,gBAAgB,CAAC;MACnFuQ,OAAO,EAAE,CAAC5R,UAAU,EAAEqB,gBAAgB,EAAEpD,eAAe,EAAEd,mBAAmB,CAAC;MAC7EwU,SAAS,EAAE,CAAC5S,4CAA4C;IAC5D,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASiB,UAAU,IAAIhC,CAAC,EAAEG,kBAAkB,IAAI0T,CAAC,EAAErS,mBAAmB,IAAIsS,CAAC,EAAET,gBAAgB,IAAIU,CAAC,EAAExT,2BAA2B,IAAIyT,CAAC,EAAElT,mCAAmC,IAAImT,CAAC,EAAElT,4CAA4C,IAAImT,CAAC,EAAE/S,mCAAmC,IAAIgT,CAAC,EAAE5S,2BAA2B,IAAI6S,CAAC,EAAEhU,iCAAiC,IAAIiU,CAAC,EAAEhR,gBAAgB,IAAIiR,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}