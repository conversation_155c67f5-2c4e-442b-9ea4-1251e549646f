{"ast": null, "code": "/**\n * A specialized version of `_.lastIndexOf` which performs strict equality\n * comparisons of values, i.e. `===`.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction strictLastIndexOf(array, value, fromIndex) {\n  var index = fromIndex + 1;\n  while (index--) {\n    if (array[index] === value) {\n      return index;\n    }\n  }\n  return index;\n}\nexport default strictLastIndexOf;", "map": {"version": 3, "names": ["strictLastIndexOf", "array", "value", "fromIndex", "index"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_strictLastIndexOf.js"], "sourcesContent": ["/**\n * A specialized version of `_.lastIndexOf` which performs strict equality\n * comparisons of values, i.e. `===`.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction strictLastIndexOf(array, value, fromIndex) {\n  var index = fromIndex + 1;\n  while (index--) {\n    if (array[index] === value) {\n      return index;\n    }\n  }\n  return index;\n}\n\nexport default strictLastIndexOf;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAClD,IAAIC,KAAK,GAAGD,SAAS,GAAG,CAAC;EACzB,OAAOC,KAAK,EAAE,EAAE;IACd,IAAIH,KAAK,CAACG,KAAK,CAAC,KAAKF,KAAK,EAAE;MAC1B,OAAOE,KAAK;IACd;EACF;EACA,OAAOA,KAAK;AACd;AAEA,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}