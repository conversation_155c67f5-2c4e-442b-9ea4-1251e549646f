{"ast": null, "code": "import { namespace } from \"d3-selection\";\nfunction attrInterpolate(name, i) {\n  return function (t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\nfunction attrInterpolateNS(fullname, i) {\n  return function (t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\nexport default function (name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error();\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}", "map": {"version": 3, "names": ["namespace", "attrInterpolate", "name", "i", "t", "setAttribute", "call", "attrInterpolateNS", "fullname", "setAttributeNS", "space", "local", "attrTweenNS", "value", "t0", "i0", "tween", "apply", "arguments", "_value", "attrTween", "key", "length", "Error"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/d3-transition/src/transition/attrTween.js"], "sourcesContent": ["import {namespace} from \"d3-selection\";\n\nfunction attrInterpolate(name, i) {\n  return function(t) {\n    this.setAttribute(name, i.call(this, t));\n  };\n}\n\nfunction attrInterpolateNS(fullname, i) {\n  return function(t) {\n    this.setAttributeNS(fullname.space, fullname.local, i.call(this, t));\n  };\n}\n\nfunction attrTweenNS(fullname, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolateNS(fullname, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nfunction attrTween(name, value) {\n  var t0, i0;\n  function tween() {\n    var i = value.apply(this, arguments);\n    if (i !== i0) t0 = (i0 = i) && attrInterpolate(name, i);\n    return t0;\n  }\n  tween._value = value;\n  return tween;\n}\n\nexport default function(name, value) {\n  var key = \"attr.\" + name;\n  if (arguments.length < 2) return (key = this.tween(key)) && key._value;\n  if (value == null) return this.tween(key, null);\n  if (typeof value !== \"function\") throw new Error;\n  var fullname = namespace(name);\n  return this.tween(key, (fullname.local ? attrTweenNS : attrTween)(fullname, value));\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,cAAc;AAEtC,SAASC,eAAeA,CAACC,IAAI,EAAEC,CAAC,EAAE;EAChC,OAAO,UAASC,CAAC,EAAE;IACjB,IAAI,CAACC,YAAY,CAACH,IAAI,EAAEC,CAAC,CAACG,IAAI,CAAC,IAAI,EAAEF,CAAC,CAAC,CAAC;EAC1C,CAAC;AACH;AAEA,SAASG,iBAAiBA,CAACC,QAAQ,EAAEL,CAAC,EAAE;EACtC,OAAO,UAASC,CAAC,EAAE;IACjB,IAAI,CAACK,cAAc,CAACD,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,KAAK,EAAER,CAAC,CAACG,IAAI,CAAC,IAAI,EAAEF,CAAC,CAAC,CAAC;EACtE,CAAC;AACH;AAEA,SAASQ,WAAWA,CAACJ,QAAQ,EAAEK,KAAK,EAAE;EACpC,IAAIC,EAAE,EAAEC,EAAE;EACV,SAASC,KAAKA,CAAA,EAAG;IACf,IAAIb,CAAC,GAAGU,KAAK,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAIf,CAAC,KAAKY,EAAE,EAAED,EAAE,GAAG,CAACC,EAAE,GAAGZ,CAAC,KAAKI,iBAAiB,CAACC,QAAQ,EAAEL,CAAC,CAAC;IAC7D,OAAOW,EAAE;EACX;EACAE,KAAK,CAACG,MAAM,GAAGN,KAAK;EACpB,OAAOG,KAAK;AACd;AAEA,SAASI,SAASA,CAAClB,IAAI,EAAEW,KAAK,EAAE;EAC9B,IAAIC,EAAE,EAAEC,EAAE;EACV,SAASC,KAAKA,CAAA,EAAG;IACf,IAAIb,CAAC,GAAGU,KAAK,CAACI,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACpC,IAAIf,CAAC,KAAKY,EAAE,EAAED,EAAE,GAAG,CAACC,EAAE,GAAGZ,CAAC,KAAKF,eAAe,CAACC,IAAI,EAAEC,CAAC,CAAC;IACvD,OAAOW,EAAE;EACX;EACAE,KAAK,CAACG,MAAM,GAAGN,KAAK;EACpB,OAAOG,KAAK;AACd;AAEA,eAAe,UAASd,IAAI,EAAEW,KAAK,EAAE;EACnC,IAAIQ,GAAG,GAAG,OAAO,GAAGnB,IAAI;EACxB,IAAIgB,SAAS,CAACI,MAAM,GAAG,CAAC,EAAE,OAAO,CAACD,GAAG,GAAG,IAAI,CAACL,KAAK,CAACK,GAAG,CAAC,KAAKA,GAAG,CAACF,MAAM;EACtE,IAAIN,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI,CAACG,KAAK,CAACK,GAAG,EAAE,IAAI,CAAC;EAC/C,IAAI,OAAOR,KAAK,KAAK,UAAU,EAAE,MAAM,IAAIU,KAAK,CAAD,CAAC;EAChD,IAAIf,QAAQ,GAAGR,SAAS,CAACE,IAAI,CAAC;EAC9B,OAAO,IAAI,CAACc,KAAK,CAACK,GAAG,EAAE,CAACb,QAAQ,CAACG,KAAK,GAAGC,WAAW,GAAGQ,SAAS,EAAEZ,QAAQ,EAAEK,KAAK,CAAC,CAAC;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}