{"ast": null, "code": "import baseForRight from './_baseForRight.js';\nimport keys from './keys.js';\n\n/**\n * The base implementation of `_.forOwnRight` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\nfunction baseForOwnRight(object, iteratee) {\n  return object && baseForRight(object, iteratee, keys);\n}\nexport default baseForOwnRight;", "map": {"version": 3, "names": ["baseForRight", "keys", "baseForOwnRight", "object", "iteratee"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_baseForOwnRight.js"], "sourcesContent": ["import baseForRight from './_baseForRight.js';\nimport keys from './keys.js';\n\n/**\n * The base implementation of `_.forOwnRight` without support for iteratee shorthands.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Object} Returns `object`.\n */\nfunction baseForOwnRight(object, iteratee) {\n  return object && baseForRight(object, iteratee, keys);\n}\n\nexport default baseForOwnRight;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACzC,OAAOD,MAAM,IAAIH,YAAY,CAACG,MAAM,EAAEC,QAAQ,EAAEH,IAAI,CAAC;AACvD;AAEA,eAAeC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}