{"ast": null, "code": "import isObject from './isObject.js';\n\n/**\n * Checks if `value` is suitable for strict equality comparisons, i.e. `===`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` if suitable for strict\n *  equality comparisons, else `false`.\n */\nfunction isStrictComparable(value) {\n  return value === value && !isObject(value);\n}\nexport default isStrictComparable;", "map": {"version": 3, "names": ["isObject", "isStrictComparable", "value"], "sources": ["D:/portals/madhura/digital_qa_portal/node_modules/lodash-es/_isStrictComparable.js"], "sourcesContent": ["import isObject from './isObject.js';\n\n/**\n * Checks if `value` is suitable for strict equality comparisons, i.e. `===`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` if suitable for strict\n *  equality comparisons, else `false`.\n */\nfunction isStrictComparable(value) {\n  return value === value && !isObject(value);\n}\n\nexport default isStrictComparable;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAOA,KAAK,KAAKA,KAAK,IAAI,CAACF,QAAQ,CAACE,KAAK,CAAC;AAC5C;AAEA,eAAeD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}