{"ast": null, "code": "import { permissionGuard } from 'app/core/auth/guards/permission.guard';\nimport { UsersComponent } from './users/users.component';\nimport technicianRoutes from './technician/technician.routes';\ntechnicianRoutes;\nexport default [{\n  path: 'users',\n  component: UsersComponent,\n  canActivate: [permissionGuard],\n  data: {\n    permission: \"ADMIN_USERS\"\n  }\n}, {\n  path: 'role',\n  canActivate: [permissionGuard],\n  data: {\n    permission: \"ADMIN_ROLE_MASTER\"\n  },\n  loadChildren: () => import('./role/role.routes')\n}, {\n  path: 'brand',\n  canActivate: [permissionGuard],\n  data: {\n    permission: \"ADMIN_BRAND_MASTER\"\n  },\n  loadChildren: () => import('./brand/brand.routes')\n}, {\n  path: 'category',\n  canActivate: [permissionGuard],\n  data: {\n    permission: \"ADMIN_CATEGORY_MASTER\"\n  },\n  loadChildren: () => import('./category/category.routes')\n}, {\n  path: 'auditor',\n  canActivate: [permissionGuard],\n  data: {\n    permission: \"ADMIN_AUDITOR_MASTER\"\n  },\n  loadChildren: () => import('./auditor/auditor.routes')\n}, {\n  path: 'technician',\n  canActivate: [permissionGuard],\n  data: {\n    permission: \"ADMIN_TECHNICIAN_MASTER\"\n  },\n  loadChildren: () => import('./technician/technician.routes')\n}, {\n  path: 'not-found',\n  loadChildren: () => import('app/modules/page-not-found/page-not-found.routes')\n}, {\n  path: '**',\n  pathMatch: 'full',\n  redirectTo: 'not-found'\n}];", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "UsersComponent", "technician<PERSON><PERSON>es", "path", "component", "canActivate", "data", "permission", "loadChildren", "pathMatch", "redirectTo"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\app\\modules\\admin\\admin.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\nimport { permissionGuard } from 'app/core/auth/guards/permission.guard';\r\n\r\nimport { UsersComponent } from './users/users.component';\r\nimport technicianRoutes from './technician/technician.routes';\r\ntechnicianRoutes\r\n\r\nexport default [\r\n    {\r\n        path: 'users',\r\n        component: UsersComponent,\r\n        canActivate: [permissionGuard],\r\n        data: { permission: \"ADMIN_USERS\" },\r\n    },\r\n    {\r\n        path: 'role',\r\n        canActivate: [permissionGuard],\r\n        data: { permission: \"ADMIN_ROLE_MASTER\" },\r\n        loadChildren: () => import('./role/role.routes')\r\n    },\r\n    {\r\n        path: 'brand',\r\n        canActivate: [permissionGuard],\r\n        data: { permission: \"ADMIN_BRAND_MASTER\" },\r\n        loadChildren: () => import('./brand/brand.routes')\r\n    },\r\n    {\r\n        path: 'category',\r\n        canActivate: [permissionGuard],\r\n        data: { permission: \"ADMIN_CATEGORY_MASTER\" },\r\n        loadChildren: () => import('./category/category.routes')\r\n    },\r\n    {\r\n        path: 'auditor',\r\n        canActivate: [permissionGuard],\r\n        data: { permission: \"ADMIN_AUDITOR_MASTER\" },\r\n        loadChildren: () => import('./auditor/auditor.routes')\r\n    },\r\n    {\r\n        path: 'technician',\r\n        canActivate: [permissionGuard],\r\n        data: { permission: \"ADMIN_TECHNICIAN_MASTER\" },\r\n        loadChildren: () => import('./technician/technician.routes')\r\n    },\r\n\r\n    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },\r\n    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },\r\n] as Routes;\r\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,uCAAuC;AAEvE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAOC,gBAAgB,MAAM,gCAAgC;AAC7DA,gBAAgB;AAEhB,eAAe,CACX;EACIC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEH,cAAc;EACzBI,WAAW,EAAE,CAACL,eAAe,CAAC;EAC9BM,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAa;CACpC,EACD;EACIJ,IAAI,EAAE,MAAM;EACZE,WAAW,EAAE,CAACL,eAAe,CAAC;EAC9BM,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAmB,CAAE;EACzCC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB;CAClD,EACD;EACIL,IAAI,EAAE,OAAO;EACbE,WAAW,EAAE,CAACL,eAAe,CAAC;EAC9BM,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAoB,CAAE;EAC1CC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sBAAsB;CACpD,EACD;EACIL,IAAI,EAAE,UAAU;EAChBE,WAAW,EAAE,CAACL,eAAe,CAAC;EAC9BM,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAuB,CAAE;EAC7CC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B;CAC1D,EACD;EACIL,IAAI,EAAE,SAAS;EACfE,WAAW,EAAE,CAACL,eAAe,CAAC;EAC9BM,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAsB,CAAE;EAC5CC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B;CACxD,EACD;EACIL,IAAI,EAAE,YAAY;EAClBE,WAAW,EAAE,CAACL,eAAe,CAAC;EAC9BM,IAAI,EAAE;IAAEC,UAAU,EAAE;EAAyB,CAAE;EAC/CC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,gCAAgC;CAC9D,EAED;EAAEL,IAAI,EAAE,WAAW;EAAEK,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kDAAkD;AAAC,CAAE,EACrG;EAAEL,IAAI,EAAE,IAAI;EAAEM,SAAS,EAAE,MAAM;EAAEC,UAAU,EAAE;AAAW,CAAE,CACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}