{"ast": null, "code": "export class ScrollbarGeometry {\n  constructor(x, y, w, h) {\n    this.x = x;\n    this.y = y;\n    this.w = w;\n    this.h = h;\n  }\n}\nexport class ScrollbarPosition {\n  constructor(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n}", "map": {"version": 3, "names": ["ScrollbarGeometry", "constructor", "x", "y", "w", "h", "ScrollbarPosition"], "sources": ["D:\\portals\\madhura\\digital_qa_portal\\src\\@fuse\\directives\\scrollbar\\scrollbar.types.ts"], "sourcesContent": ["export class ScrollbarGeometry\r\n{\r\n    public x: number;\r\n    public y: number;\r\n\r\n    public w: number;\r\n    public h: number;\r\n\r\n    constructor(x: number, y: number, w: number, h: number)\r\n    {\r\n        this.x = x;\r\n        this.y = y;\r\n        this.w = w;\r\n        this.h = h;\r\n    }\r\n}\r\n\r\nexport class ScrollbarPosition\r\n{\r\n    public x: number | 'start' | 'end';\r\n    public y: number | 'start' | 'end';\r\n\r\n    constructor(x: number | 'start' | 'end', y: number | 'start' | 'end')\r\n    {\r\n        this.x = x;\r\n        this.y = y;\r\n    }\r\n}\r\n"], "mappings": "AAAA,OAAM,MAAOA,iBAAiB;EAQ1BC,YAAYC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS;IAElD,IAAI,CAACH,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACd;;AAGJ,OAAM,MAAOC,iBAAiB;EAK1BL,YAAYC,CAA2B,EAAEC,CAA2B;IAEhE,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}